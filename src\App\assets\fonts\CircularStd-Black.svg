<svg xmlns="http://www.w3.org/2000/svg"><defs><font horiz-adv-x="587"><font-face ascent="809" bbox="-60 -268 1578 1017" cap-height="709" descent="-191" panose-1="2 11 10 4 2 1 1 1 1 2" stemh="131" stemv="152" underline-position="-195" underline-thickness="80" unicode-range="U+000D-FEFF" x-height="497"/><missing-glyph d="M67 0v709h461v-709h-461zM191 124h213v461h-213v-461z" horiz-adv-x="595"/><glyph d="M677 369h-108v-369h-152v369h-175v-369h-152v369h-79v128h79v43c0 114 69 192 190 192 25 0 58-4 72-11v-124c-8 2-22 5-45 5-26 0-65-12-65-62v-43h175v43c0 114 69 192 189 192 26 0 59-3 73-10v-125c-8 2-23 5-45 5-27 0-65-12-65-62v-43h108v-128z" glyph-name="f_f" horiz-adv-x="690" unicode="&#x66;&#x66;"/><glyph d="M280 732c25 0 58-5 72-12v-123c-8 2-22 5-45 5-29 0-65-12-65-62v-43h175v43c0 114 69 192 189 192 26 0 59-4 73-11v-124c-8 2-23 5-45 5-27 0-65-12-65-62v-43h339v-497h-153v369h-186v-369h-152v369h-175v-369h-152v369h-79v128h79v43c0 114 69 192 190 192zM742 649c0 50 40 89 89 89 50 0 89-39 89-89 0-49-39-89-89-89-49 0-89 40-89 89z" glyph-name="f_f_i" horiz-adv-x="973" unicode="&#x66;&#x66;&#x69;"/><glyph d="M307 602c-26 0-65-12-65-62v-43h175v43c0 114 69 192 189 192 26 0 59-4 73-11v-124c-8 2-23 5-45 5-27 0-65-12-65-62v-43h187v227h152v-724h-152v369h-187v-369h-152v369h-175v-369h-152v369h-79v128h79v43c0 114 69 192 190 192 25 0 58-4 72-11v-124c-8 2-22 5-45 5z" glyph-name="f_f_l" horiz-adv-x="975" unicode="&#x66;&#x66;&#x6c;"/><glyph d="M425 369h-183v-369h-152v369h-79v128h79v43c0 114 69 192 190 192 25 0 56-4 70-11v-124c-7 2-21 5-43 5-26 0-65-12-65-62v-43h335v-497h-152v369zM412 649c0 50 40 89 88 89 50 0 90-39 90-89 0-49-40-89-90-89-48 0-88 40-88 89z" glyph-name="f_i" horiz-adv-x="643" unicode="&#x66;&#x69;"/><glyph d="M307 602c-26 0-65-12-65-62v-43h182v227h153v-724h-153v369h-182v-369h-152v369h-79v128h79v43c0 114 69 192 190 192 25 0 56-4 70-11v-124c-7 2-21 5-43 5z" glyph-name="f_l" horiz-adv-x="644" unicode="&#x66;&#x6c;"/><glyph d="M67 0v709h461v-709h-461zM191 124h213v461h-213v-461z" glyph-name=".notdef" horiz-adv-x="595"/><glyph glyph-name=".null" horiz-adv-x="0"/><glyph glyph-name=".null" horiz-adv-x="0"/><glyph glyph-name="uni000D" horiz-adv-x="246" unicode="&#xd;"/><glyph glyph-name="space" horiz-adv-x="246" unicode="&#x20;"/><glyph d="M264 709l-35-487h-112l-35 487h182zM86 76c0 48 38 87 86 87s87-39 87-87c0-47-39-86-87-86s-86 39-86 86z" glyph-name="exclam" horiz-adv-x="345" unicode="&#x21;"/><glyph d="M31 577c0 47 35 83 82 83 46 0 81-36 81-83 0-9-2-23-4-30l-50-181h-54l-50 181c-1 6-5 19-5 30zM222 577c0 47 35 83 82 83 46 0 81-36 81-83 0-9-2-23-4-30l-50-181h-54l-50 181c-1 6-5 19-5 30z" glyph-name="quotedbl" horiz-adv-x="416" unicode="&#x22;"/><glyph d="M86 392v105h144l33 152h114l-33-152h126l33 152h115l-33-152h123v-105h-145l-28-130h122v-106h-145l-33-156h-115l33 156h-126l-33-156h-114l33 156h-120v106h143l28 130h-122zM322 392l-28-130h126l28 130h-126z" glyph-name="numbersign" horiz-adv-x="744" unicode="&#x23;"/><glyph d="M368-117h-115v107c-141 23-205 127-212 209l138 31c5-58 45-110 131-110 50 0 86 24 86 63 0 29-22 48-62 57l-89 21c-112 27-181 95-181 190 0 112 84 189 189 207v108h115v-111c110-24 160-103 174-171l-139-37c-6 31-23 83-102 83-56 0-84-33-84-65 0-27 20-48 59-57l87-19c134-30 187-108 187-198 0-92-66-178-182-199v-109z" glyph-name="dollar" horiz-adv-x="596" unicode="&#x24;"/><glyph d="M147 495c0-40 26-66 61-66s60 27 60 66c0 38-25 64-60 64s-61-26-61-64zM39 495c0 93 77 166 169 166s169-73 169-166c0-96-77-166-170-166s-168 73-168 166zM604 153c0-40 26-66 61-66s60 27 60 66c0 38-25 64-60 64s-61-26-61-64zM496 153c0 93 77 166 169 166s169-73 169-166c0-96-77-166-170-166s-168 73-168 166zM274 0h-128l454 649h129z" glyph-name="percent" horiz-adv-x="872" unicode="&#x25;"/><glyph d="M535 0l-64 69c-49-50-111-83-186-83-140 0-219 97-219 195 0 87 45 138 121 193l-10 11c-34 37-74 88-74 152 0 121 102 188 201 188 117 0 203-75 203-182 0-71-48-126-100-164l-23-17 95-100 189 193v-186l-100-101 159-168h-192zM289 116c35 0 65 17 94 47l-110 118-5-4c-33-24-55-49-55-88 0-37 30-73 76-73zM244 542c0-28 19-51 36-69l17-18 28 20c29 21 41 45 41 68 0 34-27 60-62 60-30 0-60-19-60-61z" glyph-name="ampersand" horiz-adv-x="727" unicode="&#x26;"/><glyph d="M31 577c0 47 35 83 82 83 46 0 81-36 81-83 0-9-2-23-4-30l-50-181h-54l-50 181c-1 6-5 19-5 30z" glyph-name="quotesingle" horiz-adv-x="225" unicode="&#x27;"/><glyph d="M37 324c0 234 97 390 220 507l86-81c-109-114-179-243-179-426 0-182 70-312 179-426l-86-81c-123 118-220 273-220 507z" glyph-name="parenleft" horiz-adv-x="343" unicode="&#x28;"/><glyph d="M306 324c0-234-97-389-219-507l-87 81c109 114 180 244 180 426 0 183-71 312-180 426l87 81c122-117 219-273 219-507z" glyph-name="parenright" horiz-adv-x="343" unicode="&#x29;"/><glyph d="M291 709v-123l114 37 38-110-115-38 70-97-94-69-73 98-72-98-94 69 70 97-115 38 37 110 114-37v123h120z" glyph-name="asterisk" horiz-adv-x="463" unicode="&#x2a;"/><glyph d="M46 360h190v195h128v-195h189v-124h-189v-195h-128v195h-190v124z" glyph-name="plus" horiz-adv-x="599" unicode="&#x2b;"/><glyph d="M57 88c0 53 43 93 100 93 70 0 108-59 108-125 0-155-110-221-190-230v71c43 8 98 48 100 108-5-4-16-7-28-7-55 0-90 40-90 90z" glyph-name="comma" horiz-adv-x="321" unicode="&#x2c;"/><glyph d="M345 233h-300v137h300v-137z" glyph-name="hyphen" horiz-adv-x="390" unicode="&#x2d;"/><glyph d="M61 94c0 56 46 102 102 102s103-46 103-102c0-55-47-103-103-103s-102 48-102 103z" glyph-name="period" horiz-adv-x="327" unicode="&#x2e;"/><glyph d="M495 723l-360-737h-135l359 737h136z" glyph-name="slash" horiz-adv-x="495" unicode="&#x2f;"/><glyph d="M195 324c0-52 6-103 24-141 18-36 47-60 95-60 49 0 77 24 95 60 19 38 25 89 25 141s-6 103-25 141c-18 36-46 60-95 60-48 0-77-24-95-60-18-38-24-89-24-141zM37 324c0 80 12 161 57 229 43 65 115 111 220 111 106 0 178-46 221-111 45-68 57-149 57-229s-12-161-57-229c-43-64-115-111-221-111-105 0-177 47-220 111-45 68-57 149-57 229z" glyph-name="zero" horiz-adv-x="628" unicode="&#x30;"/><glyph d="M346 0h-157v422h-155v105c97 1 166 56 177 122h135v-649z" glyph-name="one" horiz-adv-x="417" unicode="&#x31;"/><glyph d="M189 389l-148 1c-3 8-6 34-6 54 0 116 87 220 243 220 149 0 241-97 241-213 0-84-45-153-128-202l-123-73c-19-12-35-25-44-44h300v-132h-493c0 119 34 217 153 286l106 62c51 30 71 57 71 100 0 41-29 77-87 77-61 0-91-42-91-96 0-13 2-27 6-40z" glyph-name="two" horiz-adv-x="551" unicode="&#x32;"/><glyph d="M209 282l-68 104 160 131h-271v132h468v-122l-160-124c100-7 182-87 182-198 0-115-95-219-255-219-151 0-247 97-257 208l152 28c2-57 43-102 104-102s97 41 97 89c0 62-48 88-94 88-21 0-43-7-58-15z" glyph-name="three" horiz-adv-x="547" unicode="&#x33;"/><glyph d="M31 135v160l270 354h188v-382h104v-132h-104v-135h-158v135h-300zM331 267v214l-163-214h163z" glyph-name="four" horiz-adv-x="608" unicode="&#x34;"/><glyph d="M26 185l146 30c3-50 41-97 105-97 53 0 97 35 97 94 0 65-49 96-101 96-44 0-75-22-91-42-3 1-138 44-141 46l73 337h377v-132h-268l-26-122c25 22 73 35 110 35 133 0 224-77 224-217 0-121-92-229-254-229-144 0-243 99-251 201z" glyph-name="five" horiz-adv-x="557" unicode="&#x35;"/><glyph d="M545 637l-42-128c-30 11-66 21-102 21-107 0-191-53-210-165 23 37 78 68 154 68 132 0 217-90 217-216 0-133-105-232-256-232-154 0-269 112-269 298 0 230 148 381 373 381 61 0 109-12 135-27zM203 211c0-55 45-94 101-94s102 37 102 94c0 58-46 95-102 95s-101-37-101-95z" glyph-name="six" horiz-adv-x="584" unicode="&#x36;"/><glyph d="M509 649v-126c-45-39-226-203-254-523h-164c27 336 232 517 232 517h-318v132h504z" glyph-name="seven" horiz-adv-x="514" unicode="&#x37;"/><glyph d="M286 388c57 1 83 37 83 76 0 34-29 70-83 70s-83-36-83-70c0-39 27-75 83-76zM286 113c64 0 97 42 97 81 0 40-30 83-97 83-66 0-96-43-96-83 0-39 33-81 96-81zM286-15c-147 0-250 77-250 191 0 67 42 131 110 158-67 24-98 91-98 142 0 109 98 188 238 188 141 0 239-79 239-188 0-51-32-118-99-142 68-27 111-91 111-158 0-114-103-191-251-191z" glyph-name="eight" horiz-adv-x="573" unicode="&#x38;"/><glyph d="M51 19l34 127c21-11 68-26 114-26 99 0 187 45 197 160-27-40-83-63-141-63-125 0-224 88-224 218 0 132 102 229 254 229 148 0 263-105 263-312 0-230-123-367-346-367-58 0-117 14-151 34zM375 440c0 54-42 92-97 92s-97-38-97-92 42-92 97-92 97 39 97 92z" glyph-name="nine" horiz-adv-x="585" unicode="&#x39;"/><glyph d="M61 91c0 55 45 100 100 100 54 0 100-45 100-100 0-53-46-100-100-100-55 0-100 47-100 100zM61 406c0 55 45 100 100 100 54 0 100-45 100-100 0-53-46-100-100-100-55 0-100 47-100 100z" glyph-name="colon" horiz-adv-x="322" unicode="&#x3a;"/><glyph d="M64 82c0 50 41 88 95 88 67 0 103-56 103-119 0-147-105-210-181-218v67c41 8 93 46 95 103-5-4-15-7-26-7-53 0-86 38-86 86zM61 406c0 55 45 100 100 100 54 0 100-45 100-100 0-53-46-100-100-100-55 0-100 47-100 100z" glyph-name="semicolon" horiz-adv-x="323" unicode="&#x3b;"/><glyph d="M35 244v108l439 224v-138l-288-137 288-139v-140z" glyph-name="less" horiz-adv-x="505" unicode="&#x3c;"/><glyph d="M551 339h-495v122h495v-122zM551 138h-495v122h495v-122z" glyph-name="equal" horiz-adv-x="607" unicode="&#x3d;"/><glyph d="M470 353v-108l-439-223v137l288 138-288 139v140z" glyph-name="greater" horiz-adv-x="505" unicode="&#x3e;"/><glyph d="M313 222h-119c-3 14-4 28-4 44 0 47 19 98 76 141l51 38c27 20 36 44 36 70 0 39-29 77-89 77-58 0-92-47-92-94 0-23 2-33 3-37l-140 5c-3 14-4 29-4 42 0 117 87 216 233 216 162 0 243-97 243-197 0-80-37-135-97-180l-38-28c-35-26-59-52-59-97zM171 76c0 48 38 87 86 87s87-39 87-87c0-47-39-86-87-86s-86 39-86 86z" glyph-name="question" horiz-adv-x="538" unicode="&#x3f;"/><glyph d="M583 141h-4c-55 0-84 30-92 58-23-40-69-60-115-60-84 0-151 71-151 172 0 130 91 223 196 223 35 0 76-17 91-53l8 40h103l-52-243c-1-4-1-8-1-12 0-15 11-29 28-29 47 0 94 63 94 158 0 138-107 231-255 231-163 0-286-117-286-293 0-162 109-284 293-284 56 0 119 14 173 52l51-73c-52-46-149-76-224-76-234 0-404 151-404 378 0 234 179 395 396 395 208 0 360-141 360-329 0-153-106-253-209-255zM486 365c0 42-21 70-59 70-55 0-92-51-92-115 0-49 19-83 63-83 49 0 87 53 88 128z" glyph-name="at" horiz-adv-x="827" unicode="&#x40;"/><glyph d="M552 0l-51 144h-274l-51-144h-166l267 709h183l264-709h-172zM366 533l-87-245h172z" glyph-name="A" horiz-adv-x="734" unicode="&#x41;"/><glyph d="M77 709h265c140 0 221-81 221-190 0-73-46-133-104-152 64-15 126-74 126-166 0-116-88-201-222-201h-286v709zM231 425h86c55 0 91 29 91 77 0 46-31 76-93 76h-84v-153zM231 129h99c61 0 98 32 98 83 0 49-36 83-98 83h-99v-166z" glyph-name="B" horiz-adv-x="617" unicode="&#x42;"/><glyph d="M404-15c-204 0-368 147-368 368s168 371 366 371c213 0 307-137 330-239l-149-47c-11 49-56 132-181 132-95 0-202-68-202-217 0-133 98-210 204-210 123 0 172 82 186 133l151-43c-23-98-117-248-337-248z" glyph-name="C" horiz-adv-x="772" unicode="&#x43;"/><glyph d="M235 149h92c109 0 198 63 198 205s-89 206-198 206h-92v-411zM333 0h-256v709h257c213 0 356-136 356-355s-143-354-357-354z" glyph-name="D" horiz-adv-x="726" unicode="&#x44;"/><glyph d="M527 0h-450v709h449v-149h-291v-135h264v-139h-264v-136h292v-150z" glyph-name="E" horiz-adv-x="583" unicode="&#x45;"/><glyph d="M236 0h-159v709h457v-150h-299v-150h263v-144h-262v-265z" glyph-name="F" horiz-adv-x="568" unicode="&#x46;"/><glyph d="M732 0h-128l-10 72c-31-45-98-87-199-87-197 0-359 145-359 369 0 223 169 370 369 370 203 0 297-120 328-219l-152-53c-11 44-58 124-176 124-96 0-209-64-209-222 0-148 98-225 213-225 117 0 166 76 175 116h-213v134h361v-379z" glyph-name="G" horiz-adv-x="788" unicode="&#x47;"/><glyph d="M677 0h-159v282h-283v-282h-158v709h158v-278h283v278h159v-709z" glyph-name="H" horiz-adv-x="753" unicode="&#x48;"/><glyph d="M237 0h-160v709h160v-709z" glyph-name="I" horiz-adv-x="313" unicode="&#x49;"/><glyph d="M20 219v47l150 26v-65c0-63 40-92 86-92 52 0 83 38 83 91v483h158v-487c0-129-99-237-240-237-140 0-237 93-237 234z" glyph-name="J" horiz-adv-x="568" unicode="&#x4a;"/><glyph d="M506 0l-191 279-80-89v-190h-158v709h158v-298l259 298h208l-276-307 277-402h-197z" glyph-name="K" horiz-adv-x="713" unicode="&#x4b;"/><glyph d="M541 0h-464v709h158v-558h306v-151z" glyph-name="L" horiz-adv-x="566" unicode="&#x4c;"/><glyph d="M880 0h-155v470l-185-470h-128l-185 464v-464h-150v709h206l195-479 185 479h217v-709z" glyph-name="M" horiz-adv-x="957" unicode="&#x4d;"/><glyph d="M693 0h-167l-292 466v-466h-157v709h192l266-431v431h158v-709z" glyph-name="N" horiz-adv-x="769" unicode="&#x4e;"/><glyph d="M200 355c0-148 106-214 205-214 98 0 204 66 204 214s-106 215-204 215c-99 0-205-67-205-215zM36 354c0 225 169 370 369 370 199 0 368-145 368-370 0-224-169-369-368-369-200 0-369 145-369 369z" glyph-name="O" horiz-adv-x="809" unicode="&#x4f;"/><glyph d="M236 391h92c60 0 101 35 101 90 0 57-41 91-101 91h-92v-181zM344 255h-109v-255h-158v709h267c144 0 243-95 243-227 0-134-99-227-243-227z" glyph-name="P" horiz-adv-x="613" unicode="&#x50;"/><glyph d="M36 354c0 225 169 370 369 370 199 0 368-145 368-370 0-95-30-175-80-236l87-95-104-88-87 96c-54-30-118-46-184-46-200 0-369 145-369 369zM200 355c0-148 106-214 205-214 24 0 48 4 72 12l-104 114 105 88 104-114c17 31 27 68 27 114 0 148-106 215-204 215-99 0-205-67-205-215z" glyph-name="Q" horiz-adv-x="809" unicode="&#x51;"/><glyph d="M418 0l-127 264h-56v-264h-158v709h281c140 0 230-96 230-222 0-96-53-169-140-200l144-287h-174zM235 400h93c65 0 100 36 100 86 0 53-35 86-100 86h-93v-172z" glyph-name="R" horiz-adv-x="628" unicode="&#x52;"/><glyph d="M565 535l-142-40c-5 33-32 89-118 89-59 0-97-37-97-76 0-34 21-59 70-68l95-18c135-25 205-112 205-213 0-111-93-224-262-224-196 0-280 127-290 227l145 35c6-65 50-122 147-122 62 0 100 30 100 74 0 36-29 62-76 71l-97 18c-120 23-193 103-193 208 0 134 118 228 254 228 177 0 244-106 259-189z" glyph-name="S" horiz-adv-x="609" unicode="&#x53;"/><glyph d="M614 558h-219v-558h-158v558h-218v151h595v-151z" glyph-name="T" horiz-adv-x="633" unicode="&#x54;"/><glyph d="M352-16c-156 0-281 94-281 272v453h158v-440c0-86 48-129 123-129s123 43 123 129v440h158v-453c0-178-125-272-281-272z" glyph-name="U" horiz-adv-x="704" unicode="&#x55;"/><glyph d="M355 210l169 499h168l-259-709h-163l-260 709h174z" glyph-name="V" horiz-adv-x="702" unicode="&#x56;"/><glyph d="M752 254l115 455h162l-193-709h-163l-151 470-152-470h-166l-193 709h168l117-454 147 454h162z" glyph-name="W" horiz-adv-x="1039" unicode="&#x57;"/><glyph d="M674 709l-228-356 230-353h-192l-140 230-139-230h-186l229 355-229 354h190l140-230 140 230h185z" glyph-name="X" horiz-adv-x="695" unicode="&#x58;"/><glyph d="M256 296l-246 413h185l145-263 147 263h176l-249-413v-296h-158v296z" glyph-name="Y" horiz-adv-x="673" unicode="&#x59;"/><glyph d="M599 0h-563v152l342 406h-327v151h544v-146l-350-412h354v-151z" glyph-name="Z" horiz-adv-x="644" unicode="&#x5a;"/><glyph d="M326-171h-249v994h249v-114h-123v-764h123v-116z" glyph-name="bracketleft" horiz-adv-x="331" unicode="&#x5b;"/><glyph d="M360-14l-360 737h138l359-737h-137z" glyph-name="backslash" horiz-adv-x="497" unicode="&#x5c;"/><glyph d="M255-171h-250v116h124v764h-124v114h250v-994z" glyph-name="bracketright" horiz-adv-x="331" unicode="&#x5d;"/><glyph d="M523 350h-138l-116 217-117-217h-140l199 359h112z" glyph-name="asciicircum" horiz-adv-x="535" unicode="&#x5e;"/><glyph d="M512-124h-510v124h510v-124z" glyph-name="underscore" horiz-adv-x="513" unicode="&#x5f;"/><glyph d="M163 556l-142 151h173l93-151h-124z" glyph-name="grave" horiz-adv-x="308" unicode="&#x60;"/><glyph d="M37 137c0 88 65 138 147 150l117 18c27 4 36 17 36 34 0 29-25 54-73 54-53 0-82-36-85-73l-132 27c6 71 72 165 218 165 161 0 220-90 220-192v-243c0-39 5-72 6-77h-137c-1 4-5 22-5 56-26-42-74-70-140-70-109 0-172 72-172 151zM246 96c45 0 91 22 91 99v22l-94-15c-32-5-56-20-56-55 0-26 17-51 59-51z" glyph-name="a" horiz-adv-x="546" unicode="&#x61;"/><glyph d="M214 0h-147v724h149v-274c21 31 76 59 144 59 144 0 231-110 231-259 0-152-98-261-237-261-66 0-117 29-140 67v-56zM439 249c0 84-53 123-112 123s-113-39-113-123c0-82 54-124 113-124s112 41 112 124z" glyph-name="b" horiz-adv-x="622" unicode="&#x62;"/><glyph d="M296 372c-63 0-114-44-114-124 0-79 53-123 116-123 59 0 92 37 103 74l134-41c-22-87-104-173-237-173-148 0-267 109-267 263 0 155 116 264 262 264 137 0 217-84 239-173l-136-41c-11 38-41 74-100 74z" glyph-name="c" horiz-adv-x="561" unicode="&#x63;"/><glyph d="M556 724v-635c0-45 3-78 4-89h-146c-1 6-4 28-4 48-23-34-73-59-133-59-140 0-246 108-246 261 0 148 100 259 241 259 85 0 122-32 134-51v266h150zM184 249c0-82 53-124 112-124s112 42 112 124c0 83-53 123-112 123s-112-40-112-123z" glyph-name="d" horiz-adv-x="626" unicode="&#x64;"/><glyph d="M182 308h199c-2 38-28 84-100 84-64 0-96-47-99-84zM392 179l127-36c-24-86-103-158-229-158-137 0-259 98-259 265 0 160 119 262 248 262 154 0 249-95 249-255 0-21-2-44-3-47h-346c3-56 54-96 113-96 55 0 86 26 100 65z" glyph-name="e" horiz-adv-x="559" unicode="&#x65;"/><glyph d="M350 369h-108v-369h-152v369h-79v128h79v43c0 114 69 192 190 192 31 0 58-4 72-11v-124c-8 2-21 5-45 5-27 0-65-12-65-67v-38h108v-128z" glyph-name="f" horiz-adv-x="363" unicode="&#x66;"/><glyph d="M32-28l134 38c10-48 50-83 107-83 76 0 123 37 123 127v23c-18-27-61-56-133-56-132 0-231 104-231 242 0 131 95 243 231 243 81 0 124-36 138-62v53h145v-435c0-143-80-267-266-267-143 0-234 88-248 177zM291 153c62 0 106 42 106 110s-49 110-106 110-107-42-107-110 46-110 107-110z" glyph-name="g" horiz-adv-x="607" unicode="&#x67;"/><glyph d="M219 292v-292h-152v724h152v-259c31 31 83 45 127 45 130 0 188-89 188-198v-312h-152v286c0 49-26 86-81 86-48 0-79-34-82-80z" glyph-name="h" horiz-adv-x="595" unicode="&#x68;"/><glyph d="M219 0h-152v497h152v-497zM54 649c0 49 40 89 88 89 50 0 90-40 90-89s-40-89-90-89c-48 0-88 40-88 89z" glyph-name="i" horiz-adv-x="285" unicode="&#x69;"/><glyph d="M67-20v517h152v-533c0-100-63-169-164-169-53 0-77 11-84 14v126c7-2 22-6 45-6 35 0 51 20 51 51zM54 649c0 50 40 89 88 89 50 0 90-39 90-89 0-49-40-89-90-89-48 0-88 40-88 89z" glyph-name="j" horiz-adv-x="286" unicode="&#x6a;"/><glyph d="M567 497l-194-209 198-288h-186l-117 175-49-53v-122h-152v724h152v-394l151 167h197z" glyph-name="k" horiz-adv-x="576" unicode="&#x6b;"/><glyph d="M219 0h-152v724h152v-724z" glyph-name="l" horiz-adv-x="286" unicode="&#x6c;"/><glyph d="M219 0h-152v497h145v-57c25 44 89 72 143 72 71 0 122-29 147-78 39 56 87 78 154 78 94 0 184-55 184-190v-322h-147v288c0 47-25 84-79 84s-83-41-83-85v-287h-150v288c0 47-25 84-80 84-53 0-82-41-82-86v-286z" glyph-name="m" horiz-adv-x="901" unicode="&#x6d;"/><glyph d="M219 285v-285h-152v497h147v-57c27 47 88 70 139 70 125 0 181-89 181-198v-312h-152v286c0 49-26 86-81 86-50 0-82-37-82-87z" glyph-name="n" horiz-adv-x="595" unicode="&#x6e;"/><glyph d="M295 125c58 0 112 40 112 124s-54 123-112 123c-57 0-112-39-112-123 0-83 55-124 112-124zM295 512c149 0 264-109 264-263s-115-264-264-264c-148 0-264 110-264 264s116 263 264 263z" glyph-name="o" horiz-adv-x="590" unicode="&#x6f;"/><glyph d="M219-190h-152v687h147v-51c21 32 75 63 146 63 144 0 231-110 231-259 0-152-98-261-237-261-64 0-112 22-135 50v-229zM442 249c0 83-53 123-113 123-59 0-112-40-112-123 0-82 53-124 112-124 60 0 113 41 113 124z" glyph-name="p" horiz-adv-x="622" unicode="&#x70;"/><glyph d="M556-190h-149v230c-22-31-68-51-130-51-142 0-246 109-246 261 0 148 98 259 240 259 88 0 129-40 140-61v49h145v-687zM185 249c0-82 53-124 112-124s112 41 112 124-53 123-112 123-112-40-112-123z" glyph-name="q" horiz-adv-x="622" unicode="&#x71;"/><glyph d="M383 497v-150c-17 4-33 5-48 5-61 0-116-36-116-135v-217h-152v497h147v-67c26 56 89 72 129 72 15 0 30-2 40-5z" glyph-name="r" horiz-adv-x="403" unicode="&#x72;"/><glyph d="M26 141l129 23c2-34 26-67 78-67 39 0 58 21 58 43 0 18-12 33-49 41l-57 13c-106 23-148 83-148 153 0 91 80 165 191 165 144 0 194-90 199-148l-126-23c-4 33-25 61-71 61-29 0-54-17-54-43 0-21 17-33 39-37l66-13c103-21 153-83 153-156 0-85-65-168-198-168-156 0-206 101-210 156z" glyph-name="s" horiz-adv-x="466" unicode="&#x73;"/><glyph d="M249 642v-145h97v-133h-97v-186c0-41 22-52 55-52 16 0 31 3 40 5v-126c-6-3-31-14-79-14-103 0-166 61-166 160v213h-88v133h25c52 0 77 35 77 81v64h136z" glyph-name="t" horiz-adv-x="382" unicode="&#x74;"/><glyph d="M388 0c-2 9-4 37-4 50-26-43-83-61-133-61-121 0-189 88-189 194v314h152v-282c0-48 26-86 80-86 51 0 83 35 83 85v283h152v-408c0-45 4-81 5-89h-146z" glyph-name="u" horiz-adv-x="596" unicode="&#x75;"/><glyph d="M556 497l-189-497h-153l-204 497h165l115-309 106 309h160z" glyph-name="v" horiz-adv-x="566" unicode="&#x76;"/><glyph d="M350 497h161l97-295 83 295h149l-154-497h-151l-108 321-106-321h-154l-156 497h158l83-294z" glyph-name="w" horiz-adv-x="851" unicode="&#x77;"/><glyph d="M14 0l171 248-174 249h179l89-136 87 136h170l-172-243c56-80 121-174 178-254h-178l-92 140c-27-40-64-99-91-140h-167z" glyph-name="x" horiz-adv-x="552" unicode="&#x78;"/><glyph d="M262-191h-160l113 258-209 430h170l121-266 110 266h161z" glyph-name="y" horiz-adv-x="578" unicode="&#x79;"/><glyph d="M453 0h-417v134l219 233h-213v130h407v-125l-229-241h233v-131z" glyph-name="z" horiz-adv-x="489" unicode="&#x7a;"/><glyph d="M45 269v124c56 0 96 24 96 88v145c0 134 79 197 199 197h40v-114h-37c-37 0-76-19-76-81v-170c0-61-35-108-99-127 63-19 99-66 99-128v-170c0-62 39-82 76-82h37v-113h-40c-120 0-199 62-199 196v146c0 64-40 89-96 89z" glyph-name="braceleft" horiz-adv-x="385" unicode="&#x7b;"/><glyph d="M203-162h-126v985h126v-985z" glyph-name="bar" horiz-adv-x="279" unicode="&#x7c;"/><glyph d="M340 393v-124c-56 0-95-25-95-89v-146c0-134-79-196-200-196h-40v113h37c37 0 77 20 77 82v170c0 62 35 109 98 128-64 19-98 66-98 127v170c0 62-40 81-77 81h-37v114h40c121 0 200-63 200-197v-145c0-64 39-88 95-88z" glyph-name="braceright" horiz-adv-x="385" unicode="&#x7d;"/><glyph d="M285 230l-47 26c-16 9-33 13-49 13-33 0-63-17-85-50l-82 65c37 71 100 110 172 110 35 0 72-9 109-29l48-26c17-9 34-14 51-14 32 0 63 17 85 50l80-65c-37-71-100-110-171-110-36 0-74 10-111 30z" glyph-name="asciitilde" horiz-adv-x="589" unicode="&#x7e;"/><glyph glyph-name="uni00A0" horiz-adv-x="246" unicode="&#xa0;"/><glyph d="M213 281l35-487h-182l35 487h112zM71 426c0 48 38 86 86 86s87-38 87-86c0-47-39-86-87-86s-86 39-86 86z" glyph-name="exclamdown" horiz-adv-x="315" unicode="&#xa1;"/><glyph d="M341 0h-115v84c-109 26-189 116-189 235 0 121 80 212 189 237v93h115v-91c97-18 155-85 173-155l-129-38c-11 35-39 69-96 69-59 0-108-41-108-115 0-72 50-113 110-113 57 0 88 34 98 68l128-37c-18-70-79-138-176-156v-81z" glyph-name="cent" horiz-adv-x="553" unicode="&#xa2;"/><glyph d="M59 383h57c-8 24-13 51-13 84 0 98 80 197 220 197 159 0 213-110 214-193l-132-19c-1 53-28 88-80 88-40 0-77-25-77-78 0-30 7-53 17-79h176v-110h-141c1-6 1-12 1-18 0-47-17-95-62-125h131c42 0 74 29 74 74l132-8c0-113-75-196-186-196h-334v118c63 22 104 63 104 122 0 11-2 22-4 33h-97v110z" glyph-name="sterling" horiz-adv-x="592" unicode="&#xa3;"/><glyph d="M77 248c0 37 9 71 25 101l-71 72 91 90 74-74c30 15 63 23 99 23 35 0 71-8 98-23l74 74 91-90-71-72c16-27 26-64 26-101 0-38-10-72-26-102l71-70-91-91-73 74c-30-15-64-23-99-23-36 0-69 8-99 23l-74-74-91 91 72 71c-16 27-26 64-26 101zM202 248c0-53 38-91 93-91s93 38 93 91-38 91-93 91-93-38-93-91z" glyph-name="currency" horiz-adv-x="589" unicode="&#xa4;"/><glyph d="M569 125h-182v-125h-146v125h-172v106h172v63h-172v106h98l-146 249h169l129-236 129 236h162l-149-249h108v-106h-182v-63h182v-106z" glyph-name="yen" horiz-adv-x="631" unicode="&#xa5;"/><glyph d="M77 823h126v-398h-126v398zM77 235h126v-398h-126v398z" glyph-name="brokenbar" horiz-adv-x="279" unicode="&#xa6;"/><glyph d="M494 257c0-62-34-102-73-126 48-37 72-87 72-140 0-121-107-197-216-197-131 0-213 75-219 188l143 22c0-58 34-80 76-80 38 0 63 24 63 57 0 20-13 41-36 51l-101 51c-75 38-142 85-142 177 0 63 33 103 73 126-48 38-71 88-71 142 0 120 106 196 216 196 130 0 213-76 218-188l-143-22c0 59-33 80-75 80-39 0-64-25-64-57 0-20 13-40 36-50l101-52c75-39 142-84 142-178zM324 282l-102 54c-14-13-24-28-24-45 0-25 7-41 33-55l102-54c13 12 25 27 25 45 0 23-8 41-34 55z" glyph-name="section" horiz-adv-x="555" unicode="&#xa7;"/><glyph d="M22 632c0 43 33 77 76 77s77-34 77-77-34-76-77-76-76 33-76 76zM243 632c0 43 33 77 76 77s77-34 77-77-34-76-77-76-76 33-76 76z" glyph-name="dieresis" horiz-adv-x="418" unicode="&#xa8;"/><glyph d="M125 354c0-161 122-288 281-288s282 127 282 288-123 289-282 289-281-128-281-289zM36 354c0 204 166 371 370 371s371-167 371-371-167-370-371-370-370 166-370 370zM411 442c-46 0-83-32-83-91 0-58 38-90 85-90 43 0 67 27 75 54l99-30c-17-64-77-127-174-127-109 0-196 80-196 193 0 114 85 194 192 194 101 0 159-62 175-127l-99-30c-8 28-30 54-74 54z" glyph-name="copyright" horiz-adv-x="813" unicode="&#xa9;"/><glyph d="M50 397c0 61 47 96 104 105l81 12c18 3 25 12 25 24 0 19-17 35-49 35-36 0-56-25-58-49l-96 20c5 47 49 116 155 116 114 0 156-64 156-136v-170c0-26 3-47 4-54h-100c-1 5-3 18-3 38-19-30-52-48-97-48-77 0-122 51-122 107zM200 369c29 0 60 15 60 67v15l-62-10c-22-3-38-14-38-37 0-18 11-35 40-35z" glyph-name="ordfeminine" horiz-adv-x="422" unicode="&#xaa;"/><glyph d="M306 102h-142l-138 195 140 200h141l-139-201zM534 102h-143l-138 195 140 200h142l-139-201z" glyph-name="guillemotleft" horiz-adv-x="550" unicode="&#xab;"/><glyph d="M548 129h-130v195h-373v125h503v-320z" glyph-name="logicalnot" horiz-adv-x="599" unicode="&#xac;"/><glyph d="M345 233h-300v137h300v-137z" glyph-name="uni00AD" horiz-adv-x="390" unicode="&#xad;"/><glyph d="M36 354c0 204 166 371 370 371s371-167 371-371-167-370-371-370-370 166-370 370zM125 354c0-161 122-288 281-288s282 127 282 288-123 289-282 289-281-128-281-289zM463 157l-61 134h-30v-134h-104v398h167c77 0 137-58 137-131 0-51-29-94-71-115l76-152h-114zM372 375h43c32 0 52 19 52 48 0 30-20 48-52 48h-43v-96z" glyph-name="registered" horiz-adv-x="813" unicode="&#xae;"/><glyph d="M308 577h-286v105h286v-105z" glyph-name="macron" horiz-adv-x="330" unicode="&#xaf;"/><glyph d="M126 498c0-40 26-66 61-66s60 27 60 66c0 38-25 64-60 64s-61-26-61-64zM18 498c0 93 77 166 169 166s169-73 169-166c0-96-77-166-170-166s-168 73-168 166z" glyph-name="degree" horiz-adv-x="374" unicode="&#xb0;"/><glyph d="M61 438h177v117h128v-117h178v-124h-178v-116h-128v116h-177v124zM61 164h483v-124h-483v124z" glyph-name="plusminus" horiz-adv-x="605" unicode="&#xb1;"/><glyph d="M154 563h-100c-2 8-4 19-4 27 0 68 49 128 149 128 94 0 148-57 148-125 0-48-31-83-84-114l-67-39c-3-2-5-3-5-5h159v-95h-302v13c0 68 22 123 91 162l60 34c23 13 36 28 36 45 0 13-9 34-39 34s-45-22-45-48c0-9 2-15 3-17z" glyph-name="twosuperior" horiz-adv-x="396" unicode="&#xb2;"/><glyph d="M47 452l103 18c1-25 21-46 52-46 29 0 47 16 47 37 0 28-22 40-46 40-10 0-20-2-29-6l-35 75 79 47h-158v92h291v-93l-85-51c63-10 98-50 98-107 0-66-60-127-162-127-93 0-150 60-155 121z" glyph-name="threesuperior" horiz-adv-x="411" unicode="&#xb3;"/><glyph d="M284 707l-141-151h-121l92 151h170z" glyph-name="acute" horiz-adv-x="306" unicode="&#xb4;"/><glyph d="M394 0c-2 9-4 37-4 50-26-43-83-61-133-61-13 0-25 1-37 3v-182h-152v687h152v-282c0-48 26-86 80-86 51 0 83 35 83 85v283h152v-408c0-45 4-80 5-89h-146z" glyph-name="mu" horiz-adv-x="606" unicode="&#xb5;"/><glyph d="M363 597v-788h-115v482h-18c-113 0-208 90-208 205 0 138 111 213 243 213h410v-112h-97v-788h-117v788h-98z" glyph-name="paragraph" horiz-adv-x="686" unicode="&#xb6;"/><glyph d="M57 322c0 51 42 91 94 91s94-40 94-91c0-53-42-92-94-92s-94 39-94 92z" glyph-name="periodcentered" horiz-adv-x="293" unicode="&#xb7;"/><glyph d="M120-112l-37 49 48 75h90l-40-61c59-2 97-38 97-88 0-45-34-98-133-98-58 0-106 31-124 55l47 59c15-15 40-38 76-38 24 0 38 9 38 26 0 13-13 25-35 25-8 0-19-1-27-4z" glyph-name="cedilla" horiz-adv-x="299" unicode="&#xb8;"/><glyph d="M241 341h-115v212h-94v74c70 0 101 47 108 82h101v-368z" glyph-name="onesuperior" horiz-adv-x="307" unicode="&#xb9;"/><glyph d="M241 392c39 0 76 26 76 83s-37 83-76 83c-38 0-75-26-75-83 0-56 37-83 75-83zM241 660c104 0 186-77 186-185 0-109-82-185-186-185s-186 76-186 185c0 108 82 185 186 185z" glyph-name="ordmasculine" horiz-adv-x="482" unicode="&#xba;"/><glyph d="M386 102h-142l138 194-139 201h142l140-200zM159 102h-143l139 194-139 201h141l140-200z" glyph-name="guillemotright" horiz-adv-x="550" unicode="&#xbb;"/><glyph d="M251 281h-115v212h-94v74c70 0 101 47 108 82h101v-368zM441 71v112l164 186h121v-213h59v-85h-59v-71h-111v71h-174zM615 156v102l-91-102h91zM233 0h-128l454 649h129z" glyph-name="onequarter" horiz-adv-x="822" unicode="&#xbc;"/><glyph d="M251 281h-115v212h-94v74c70 0 101 47 108 82h101v-368zM232 0h-128l454 649h129zM608 223h-100c-2 8-4 19-4 27 0 68 49 128 149 128 94 0 148-57 148-125 0-48-31-83-84-114l-67-39c-3-2-5-3-5-5h159v-95h-302v13c0 68 22 123 91 162l60 34c23 13 36 28 36 45 0 13-9 34-39 34s-45-22-45-48c0-9 2-15 3-17z" glyph-name="onehalf" horiz-adv-x="849" unicode="&#xbd;"/><glyph d="M47 392l103 18c1-25 21-46 52-46 29 0 47 16 47 37 0 28-22 40-46 40-10 0-20-2-29-6l-35 75 79 47h-158v92h291v-93l-85-51c63-10 98-50 98-107 0-66-60-127-162-127-93 0-150 60-155 121zM520 71v112l164 186h121v-213h59v-85h-59v-71h-111v71h-174zM694 156v102l-91-102h91zM314 0h-128l454 649h129z" glyph-name="threequarters" horiz-adv-x="901" unicode="&#xbe;"/><glyph d="M203 282h119c2-14 4-28 4-44 0-47-17-98-74-141l-50-38c-27-21-37-45-37-70 0-39 29-75 89-75 61 0 92 44 92 94 0 13-2 26-5 38l143-12c3-14 4-29 4-43 0-122-97-212-234-212-163 0-243 98-243 198 0 80 37 136 95 180l38 28c36 27 59 54 59 97zM175 427c0 48 39 86 86 86 48 0 86-38 86-86 0-47-38-86-86-86-47 0-86 39-86 86z" glyph-name="questiondown" horiz-adv-x="499" unicode="&#xbf;"/><glyph d="M552 0l-51 144h-274l-51-144h-166l267 709h183l264-709h-172zM366 533l-87-245h172zM314 759l-150 132h181l104-132h-135z" glyph-name="Agrave" horiz-adv-x="734" unicode="&#xc0;"/><glyph d="M552 0l-51 144h-274l-51-144h-166l267 709h183l264-709h-172zM366 533l-87-245h172zM576 891l-150-132h-135l104 132h181z" glyph-name="Aacute" horiz-adv-x="734" unicode="&#xc1;"/><glyph d="M552 0l-51 144h-274l-51-144h-166l267 709h183l264-709h-172zM366 533l-87-245h172zM305 759h-135l122 132h145l124-132h-141l-58 65z" glyph-name="Acircumflex" horiz-adv-x="734" unicode="&#xc2;"/><glyph d="M552 0l-51 144h-274l-51-144h-166l267 709h183l264-709h-172zM366 533l-87-245h172zM535 895v-34c0-73-49-110-104-110-31 0-57 12-79 25l-5 3c-18 11-33 20-47 20-13 0-26-9-26-29v-13h-87v33c0 73 49 108 103 108 32 0 57-11 79-24 20-11 37-22 52-22s27 9 27 29v14h87z" glyph-name="Atilde" horiz-adv-x="734" unicode="&#xc3;"/><glyph d="M120 824c0 45 34 80 78 80 45 0 80-35 80-80 0-44-35-77-80-77-44 0-78 33-78 77zM453 824c0 45 33 80 78 80 44 0 79-35 79-80 0-44-35-77-79-77-45 0-78 33-78 77zM552 0l-51 144h-274l-51-144h-166l267 709h183l264-709h-172zM366 533l-87-245h172z" glyph-name="Adieresis" horiz-adv-x="734" unicode="&#xc4;"/><glyph d="M413 757c0 31-22 49-47 49-27 0-49-18-49-49s22-48 49-48c25 0 47 17 47 48zM366 531l-87-243h172zM552 0l-51 144h-274l-51-144h-166l250 664c-23 24-37 56-37 93 0 81 67 138 142 138s142-57 142-138c0-35-12-65-32-88l249-669h-172z" glyph-name="Aring" horiz-adv-x="734" unicode="&#xc5;"/><glyph d="M1021 0h-451v153h-274l-97-153h-189l468 709h542v-149h-292v-135h265v-139h-265v-136h293v-150zM379 282h191v299z" glyph-name="AE" horiz-adv-x="1076" unicode="&#xc6;"/><glyph d="M369-112l-37 49 32 50c-185 18-328 159-328 366 0 221 168 371 366 371 213 0 307-137 330-239l-149-47c-11 49-56 132-181 132-95 0-202-68-202-217 0-133 98-210 204-210 123 0 172 82 186 133l151-43c-21-90-103-225-287-245l-24-37c59-2 97-38 97-88 0-45-34-98-133-98-58 0-106 31-124 55l47 59c15-15 40-38 76-38 24 0 38 9 38 26 0 13-13 25-35 25-8 0-19-1-27-4z" glyph-name="Ccedilla" horiz-adv-x="772" unicode="&#xc7;"/><glyph d="M267 759l-150 132h181l104-132h-135zM527 0h-450v709h449v-149h-291v-135h264v-139h-264v-136h292v-150z" glyph-name="Egrave" horiz-adv-x="583" unicode="&#xc8;"/><glyph d="M489 891l-150-132h-135l104 132h181zM527 0h-450v709h449v-149h-291v-135h264v-139h-264v-136h292v-150z" glyph-name="Eacute" horiz-adv-x="583" unicode="&#xc9;"/><glyph d="M239 759h-135l122 132h145l124-132h-141l-58 65zM527 0h-450v709h449v-149h-291v-135h264v-139h-264v-136h292v-150z" glyph-name="Ecircumflex" horiz-adv-x="583" unicode="&#xca;"/><glyph d="M114 824c0 45 34 80 78 80 45 0 80-35 80-80 0-44-35-77-80-77-44 0-78 33-78 77zM330 824c0 45 34 80 78 80 45 0 80-35 80-80 0-44-35-77-80-77-44 0-78 33-78 77zM527 0h-450v709h449v-149h-291v-135h264v-139h-264v-136h292v-150z" glyph-name="Edieresis" horiz-adv-x="583" unicode="&#xcb;"/><glyph d="M237 0h-160v709h160v-709zM100 759l-150 132h181l104-132h-135z" glyph-name="Igrave" horiz-adv-x="313" unicode="&#xcc;"/><glyph d="M237 0h-160v709h160v-709zM363 891l-150-132h-135l104 132h181z" glyph-name="Iacute" horiz-adv-x="313" unicode="&#xcd;"/><glyph d="M237 0h-160v709h160v-709zM98 759h-135l122 132h145l124-132h-141l-58 65z" glyph-name="Icircumflex" horiz-adv-x="313" unicode="&#xce;"/><glyph d="M-24 824c0 45 33 80 77 80 45 0 79-35 79-80 0-44-34-77-79-77-44 0-77 33-77 77zM183 824c0 45 33 80 77 80 45 0 79-35 79-80 0-44-34-77-79-77-44 0-77 33-77 77zM237 0h-160v709h160v-709z" glyph-name="Idieresis" horiz-adv-x="313" unicode="&#xcf;"/><glyph d="M365 0h-256v310h-94v111h94v288h257c213 0 356-136 356-355s-143-354-357-354zM267 149h92c109 0 198 63 198 205s-89 206-198 206h-92v-139h144v-111h-144v-161z" glyph-name="Eth" horiz-adv-x="759" unicode="&#xd0;"/><glyph d="M562 895v-34c0-73-49-110-104-110-31 0-57 12-79 25l-5 3c-18 11-33 20-47 20-13 0-26-9-26-29v-13h-87v33c0 73 49 108 103 108 32 0 57-11 79-24 20-11 37-22 52-22s27 9 27 29v14h87zM693 0h-167l-292 466v-466h-157v709h192l266-431v431h158v-709z" glyph-name="Ntilde" horiz-adv-x="769" unicode="&#xd1;"/><glyph d="M338 759l-150 132h181l104-132h-135zM200 355c0-148 106-214 205-214 98 0 204 66 204 214s-106 215-204 215c-99 0-205-67-205-215zM36 354c0 225 169 370 369 370 199 0 368-145 368-370 0-224-169-369-368-369-200 0-369 145-369 369z" glyph-name="Ograve" horiz-adv-x="809" unicode="&#xd2;"/><glyph d="M610 891l-150-132h-135l104 132h181zM200 355c0-148 106-214 205-214 98 0 204 66 204 214s-106 215-204 215c-99 0-205-67-205-215zM36 354c0 225 169 370 369 370 199 0 368-145 368-370 0-224-169-369-368-369-200 0-369 145-369 369z" glyph-name="Oacute" horiz-adv-x="809" unicode="&#xd3;"/><glyph d="M344 759h-135l122 132h145l124-132h-141l-58 65zM200 355c0-148 106-214 205-214 98 0 204 66 204 214s-106 215-204 215c-99 0-205-67-205-215zM36 354c0 225 169 370 369 370 199 0 368-145 368-370 0-224-169-369-368-369-200 0-369 145-369 369z" glyph-name="Ocircumflex" horiz-adv-x="809" unicode="&#xd4;"/><glyph d="M573 895v-34c0-73-49-110-104-110-31 0-57 12-79 25l-5 3c-18 11-33 20-47 20-13 0-26-9-26-29v-13h-87v33c0 73 49 108 103 108 32 0 57-11 79-24 20-11 37-22 52-22s27 9 27 29v14h87zM200 355c0-148 106-214 205-214 98 0 204 66 204 214s-106 215-204 215c-99 0-205-67-205-215zM36 354c0 225 169 370 369 370 199 0 368-145 368-370 0-224-169-369-368-369-200 0-369 145-369 369z" glyph-name="Otilde" horiz-adv-x="809" unicode="&#xd5;"/><glyph d="M150 824c0 45 33 80 78 80 44 0 79-35 79-80 0-44-35-77-79-77-45 0-78 33-78 77zM503 824c0 45 33 80 78 80s80-35 80-80c0-44-35-77-80-77s-78 33-78 77zM200 355c0-148 106-214 205-214 98 0 204 66 204 214s-106 215-204 215c-99 0-205-67-205-215zM36 354c0 225 169 370 369 370 199 0 368-145 368-370 0-224-169-369-368-369-200 0-369 145-369 369z" glyph-name="Odieresis" horiz-adv-x="809" unicode="&#xd6;"/><glyph d="M454 43l-164 165-164-165-90 92 163 164-163 165 90 90 164-165 164 165 90-90-164-165 164-164z" glyph-name="multiply" horiz-adv-x="580" unicode="&#xd7;"/><glyph d="M36 354c0 225 169 370 369 370 63 0 123-15 175-42l70 90 96-74-69-89c59-63 96-151 96-255 0-224-169-369-368-369-64 0-125 15-178 42l-69-89-98 73 70 90c-58 63-94 150-94 253zM609 355c0 50-12 92-33 124l-250-324c25-10 52-14 79-14 98 0 204 66 204 214zM200 355c0-50 12-90 32-122l250 322c-25 10-51 15-77 15-99 0-205-67-205-215z" glyph-name="Oslash" horiz-adv-x="809" unicode="&#xd8;"/><glyph d="M300 759l-150 132h181l104-132h-135zM352-16c-156 0-281 94-281 272v453h158v-440c0-86 48-129 123-129s123 43 123 129v440h158v-453c0-178-125-272-281-272z" glyph-name="Ugrave" horiz-adv-x="704" unicode="&#xd9;"/><glyph d="M545 891l-150-132h-135l104 132h181zM352-16c-156 0-281 94-281 272v453h158v-440c0-86 48-129 123-129s123 43 123 129v440h158v-453c0-178-125-272-281-272z" glyph-name="Uacute" horiz-adv-x="704" unicode="&#xda;"/><glyph d="M292 759h-135l122 132h145l124-132h-141l-58 65zM352-16c-156 0-281 94-281 272v453h158v-440c0-86 48-129 123-129s123 43 123 129v440h158v-453c0-178-125-272-281-272z" glyph-name="Ucircumflex" horiz-adv-x="704" unicode="&#xdb;"/><glyph d="M152 824c0 45 33 80 78 80s80-35 80-80c0-44-35-77-80-77s-78 33-78 77zM397 824c0 45 33 80 77 80 45 0 80-35 80-80 0-44-35-77-80-77-44 0-77 33-77 77zM352-16c-156 0-281 94-281 272v453h158v-440c0-86 48-129 123-129s123 43 123 129v440h158v-453c0-178-125-272-281-272z" glyph-name="Udieresis" horiz-adv-x="704" unicode="&#xdc;"/><glyph d="M256 296l-246 413h185l145-263 147 263h176l-249-413v-296h-158v296zM540 891l-150-132h-135l104 132h181z" glyph-name="Yacute" horiz-adv-x="673" unicode="&#xdd;"/><glyph d="M236 264h92c60 0 99 37 99 92 0 57-39 93-99 93h-92v-185zM340 130h-105v-130h-158v709h158v-125h105c139 0 245-98 245-227 0-130-106-227-245-227z" glyph-name="Thorn" horiz-adv-x="610" unicode="&#xde;"/><glyph d="M424 343c1-15 10-21 31-29l28-11c71-28 117-84 117-157 0-68-53-161-186-161-122 0-170 87-171 145l119 20c1-22 14-52 53-52 27 0 44 18 44 39s-12 35-47 48l-26 10c-52 19-113 57-113 135 0 70 41 102 69 121l15 10c29 19 44 38 44 65 0 31-24 64-87 64-50 0-99-31-99-118v-472h-148v483c0 169 120 241 247 241 148 0 237-87 237-190 0-78-41-121-88-150l-16-10c-15-10-23-17-23-31z" glyph-name="germandbls" horiz-adv-x="611" unicode="&#xdf;"/><glyph d="M37 137c0 88 65 138 147 150l117 18c27 4 36 17 36 34 0 29-25 54-73 54-53 0-82-36-85-73l-132 27c6 71 72 165 218 165 161 0 220-90 220-192v-243c0-39 5-72 6-77h-137c-1 4-5 22-5 56-26-42-74-70-140-70-109 0-172 72-172 151zM246 96c45 0 91 22 91 99v22l-94-15c-32-5-56-20-56-55 0-26 17-51 59-51zM223 556l-142 151h173l93-151h-124z" glyph-name="agrave" horiz-adv-x="546" unicode="&#xe0;"/><glyph d="M37 137c0 88 65 138 147 150l117 18c27 4 36 17 36 34 0 29-25 54-73 54-53 0-82-36-85-73l-132 27c6 71 72 165 218 165 161 0 220-90 220-192v-243c0-39 5-72 6-77h-137c-1 4-5 22-5 56-26-42-74-70-140-70-109 0-172 72-172 151zM246 96c45 0 91 22 91 99v22l-94-15c-32-5-56-20-56-55 0-26 17-51 59-51zM452 707l-141-151h-121l92 151h170z" glyph-name="aacute" horiz-adv-x="546" unicode="&#xe1;"/><glyph d="M37 137c0 88 65 138 147 150l117 18c27 4 36 17 36 34 0 29-25 54-73 54-53 0-82-36-85-73l-132 27c6 71 72 165 218 165 161 0 220-90 220-192v-243c0-39 5-72 6-77h-137c-1 4-5 22-5 56-26-42-74-70-140-70-109 0-172 72-172 151zM246 96c45 0 91 22 91 99v22l-94-15c-32-5-56-20-56-55 0-26 17-51 59-51zM194 556h-113l113 151h143l114-151h-116l-71 71z" glyph-name="acircumflex" horiz-adv-x="546" unicode="&#xe2;"/><glyph d="M37 137c0 88 65 138 147 150l117 18c27 4 36 17 36 34 0 29-25 54-73 54-53 0-82-36-85-73l-132 27c6 71 72 165 218 165 161 0 220-90 220-192v-243c0-39 5-72 6-77h-137c-1 4-5 22-5 56-26-42-74-70-140-70-109 0-172 72-172 151zM246 96c45 0 91 22 91 99v22l-94-15c-32-5-56-20-56-55 0-26 17-51 59-51zM454 706v-20c0-88-47-130-112-130-30 0-53 11-76 23-20 10-37 21-56 21-20 0-33-13-34-43h-86v23c0 86 50 127 113 127 31 0 54-11 76-23 20-12 37-22 55-22 22 0 34 15 35 44h85z" glyph-name="atilde" horiz-adv-x="546" unicode="&#xe3;"/><glyph d="M37 137c0 88 65 138 147 150l117 18c27 4 36 17 36 34 0 29-25 54-73 54-53 0-82-36-85-73l-132 27c6 71 72 165 218 165 161 0 220-90 220-192v-243c0-39 5-72 6-77h-137c-1 4-5 22-5 56-26-42-74-70-140-70-109 0-172 72-172 151zM246 96c45 0 91 22 91 99v22l-94-15c-32-5-56-20-56-55 0-26 17-51 59-51zM81 632c0 43 33 77 76 77s77-34 77-77-34-76-77-76-76 33-76 76zM302 632c0 43 33 77 76 77s77-34 77-77-34-76-77-76-76 33-76 76z" glyph-name="adieresis" horiz-adv-x="546" unicode="&#xe4;"/><glyph d="M37 137c0 88 65 138 147 150l117 18c27 4 36 17 36 34 0 29-25 54-73 54-53 0-82-36-85-73l-132 27c6 71 72 165 218 165 161 0 220-90 220-192v-243c0-39 5-72 6-77h-137c-1 4-5 22-5 56-26-42-74-70-140-70-109 0-172 72-172 151zM246 96c45 0 91 22 91 99v22l-94-15c-32-5-56-20-56-55 0-26 17-51 59-51zM377 660c0-66-54-111-116-111s-116 45-116 111 54 113 116 113 116-47 116-113zM298 660c0 23-16 38-37 38s-37-15-37-38 16-38 37-38 37 15 37 38z" glyph-name="aring" horiz-adv-x="546" unicode="&#xe5;"/><glyph d="M699 181l126-38c-25-86-106-158-231-158-73 0-139 28-185 79-36-42-96-79-180-79-127 0-192 71-192 152 0 88 65 138 147 150l120 18c28 4 37 17 37 34 0 29-25 54-74 54-56 0-85-36-89-73l-131 27c6 71 74 165 221 165 80 0 133-22 165-57 44 36 99 57 157 57 152 0 244-95 244-255 0-21-2-44-3-47h-349c4-60 55-97 114-97 56 0 88 28 103 68zM248 96c44 0 93 22 93 99v22l-98-15c-32-5-57-21-57-56 0-24 20-50 62-50zM485 308h202c-2 38-28 84-102 84-65 0-97-47-100-84z" glyph-name="ae" horiz-adv-x="870" unicode="&#xe6;"/><glyph d="M296 372c-63 0-114-44-114-124 0-79 53-123 116-123 59 0 92 37 103 74l134-41c-20-76-85-152-190-169l-25-38c59-2 97-38 97-88 0-45-34-98-133-98-58 0-106 31-124 55l47 59c15-15 40-38 76-38 24 0 38 9 38 26 0 13-13 25-35 25-8 0-19-1-27-4l-37 49 33 51c-127 19-224 121-224 260 0 155 116 264 262 264 137 0 217-84 239-173l-136-41c-11 38-41 74-100 74z" glyph-name="ccedilla" horiz-adv-x="561" unicode="&#xe7;"/><glyph d="M182 308h199c-2 38-28 84-100 84-64 0-96-47-99-84zM392 179l127-36c-24-86-103-158-229-158-137 0-259 98-259 265 0 160 119 262 248 262 154 0 249-95 249-255 0-21-2-44-3-47h-346c3-56 54-96 113-96 55 0 86 26 100 65zM235 556l-142 151h173l93-151h-124z" glyph-name="egrave" horiz-adv-x="559" unicode="&#xe8;"/><glyph d="M470 707l-141-151h-121l92 151h170zM182 308h199c-2 38-28 84-100 84-64 0-96-47-99-84zM392 179l127-36c-24-86-103-158-229-158-137 0-259 98-259 265 0 160 119 262 248 262 154 0 249-95 249-255 0-21-2-44-3-47h-346c3-56 54-96 113-96 55 0 86 26 100 65z" glyph-name="eacute" horiz-adv-x="559" unicode="&#xe9;"/><glyph d="M211 556h-113l113 151h143l114-151h-116l-71 71zM182 308h199c-2 38-28 84-100 84-64 0-96-47-99-84zM392 179l127-36c-24-86-103-158-229-158-137 0-259 98-259 265 0 160 119 262 248 262 154 0 249-95 249-255 0-21-2-44-3-47h-346c3-56 54-96 113-96 55 0 86 26 100 65z" glyph-name="ecircumflex" horiz-adv-x="559" unicode="&#xea;"/><glyph d="M97 632c0 43 33 77 76 77s77-34 77-77-34-76-77-76-76 33-76 76zM318 632c0 43 33 77 76 77s77-34 77-77-34-76-77-76-76 33-76 76zM182 308h199c-2 38-28 84-100 84-64 0-96-47-99-84zM392 179l127-36c-24-86-103-158-229-158-137 0-259 98-259 265 0 160 119 262 248 262 154 0 249-95 249-255 0-21-2-44-3-47h-346c3-56 54-96 113-96 55 0 86 26 100 65z" glyph-name="edieresis" horiz-adv-x="559" unicode="&#xeb;"/><glyph d="M219 0h-152v497h152v-497zM89 556l-142 151h173l93-151h-124z" glyph-name="igrave" horiz-adv-x="285" unicode="&#xec;"/><glyph d="M331 707l-141-151h-121l92 151h170zM219 0h-152v497h152v-497z" glyph-name="iacute" horiz-adv-x="285" unicode="&#xed;"/><glyph d="M71 556h-113l113 151h143l114-151h-116l-71 71zM219 0h-152v497h152v-497z" glyph-name="icircumflex" horiz-adv-x="285" unicode="&#xee;"/><glyph d="M-33 632c0 42 33 76 76 76 42 0 76-34 76-76 0-43-34-76-76-76-43 0-76 33-76 76zM166 632c0 42 33 76 75 76 43 0 77-34 77-76 0-43-34-76-77-76-42 0-75 33-75 76zM219 0h-152v497h152v-497z" glyph-name="idieresis" horiz-adv-x="285" unicode="&#xef;"/><glyph d="M530 613l-75-29c64-77 101-178 101-299 0-157-85-300-263-300-147 0-262 104-262 246 0 154 127 230 246 230 38 0 86-12 105-33-12 41-34 75-61 104l-139-53-34 88 83 33c-36 20-74 34-109 44l46 112c80-20 154-54 211-98l116 45zM293 122c58 0 112 39 112 110 0 75-54 112-112 112-56 0-111-39-111-111 0-70 55-111 111-111z" glyph-name="eth" unicode="&#xf0;"/><glyph d="M219 285v-285h-152v497h147v-57c27 47 88 70 139 70 125 0 181-89 181-198v-312h-152v286c0 49-26 86-81 86-50 0-82-37-82-87zM486 706v-20c0-88-47-130-112-130-30 0-53 11-76 23-20 10-37 21-56 21-20 0-33-13-34-43h-86v23c0 86 50 127 113 127 31 0 54-11 76-23 20-12 37-22 55-22 22 0 34 15 35 44h85z" glyph-name="ntilde" horiz-adv-x="595" unicode="&#xf1;"/><glyph d="M295 125c58 0 112 40 112 124s-54 123-112 123c-57 0-112-39-112-123 0-83 55-124 112-124zM295 512c149 0 264-109 264-263s-115-264-264-264c-148 0-264 110-264 264s116 263 264 263zM241 556l-142 151h173l93-151h-124z" glyph-name="ograve" horiz-adv-x="590" unicode="&#xf2;"/><glyph d="M484 707l-141-151h-121l92 151h170zM295 125c58 0 112 40 112 124s-54 123-112 123c-57 0-112-39-112-123 0-83 55-124 112-124zM295 512c149 0 264-109 264-263s-115-264-264-264c-148 0-264 110-264 264s116 263 264 263z" glyph-name="oacute" horiz-adv-x="590" unicode="&#xf3;"/><glyph d="M225 556h-113l113 151h143l114-151h-116l-71 71zM295 125c58 0 112 40 112 124s-54 123-112 123c-57 0-112-39-112-123 0-83 55-124 112-124zM295 512c149 0 264-109 264-263s-115-264-264-264c-148 0-264 110-264 264s116 263 264 263z" glyph-name="ocircumflex" horiz-adv-x="590" unicode="&#xf4;"/><glyph d="M295 125c58 0 112 40 112 124s-54 123-112 123c-57 0-112-39-112-123 0-83 55-124 112-124zM295 512c149 0 264-109 264-263s-115-264-264-264c-148 0-264 110-264 264s116 263 264 263zM479 706v-20c0-88-47-130-112-130-30 0-53 11-76 23-20 10-37 21-56 21-20 0-33-13-34-43h-86v23c0 86 50 127 113 127 31 0 54-11 76-23 20-12 37-22 55-22 22 0 34 15 35 44h85z" glyph-name="otilde" horiz-adv-x="590" unicode="&#xf5;"/><glyph d="M108 632c0 43 33 77 76 77s77-34 77-77-34-76-77-76-76 33-76 76zM329 632c0 43 33 77 76 77s77-34 77-77-34-76-77-76-76 33-76 76zM295 125c58 0 112 40 112 124s-54 123-112 123c-57 0-112-39-112-123 0-83 55-124 112-124zM295 512c149 0 264-109 264-263s-115-264-264-264c-148 0-264 110-264 264s116 263 264 263z" glyph-name="odieresis" horiz-adv-x="590" unicode="&#xf6;"/><glyph d="M210 113c0 46 38 84 85 84 46 0 84-38 84-84s-38-85-84-85c-47 0-85 39-85 85zM210 485c0 46 38 84 85 84 46 0 84-38 84-84s-38-85-84-85c-47 0-85 39-85 85zM46 236v125h505v-125h-505z" glyph-name="divide" horiz-adv-x="597" unicode="&#xf7;"/><glyph d="M295 512c50 0 96-12 135-34l54 65 68-57-53-64c38-45 60-105 60-173 0-154-115-264-264-264-48 0-92 12-130 32l-51-62-69 57 50 60c-40 46-64 107-64 177 0 154 116 263 264 263zM183 249c0-22 4-41 10-57l143 172c-13 5-27 8-41 8-57 0-112-39-112-123zM295 125c58 0 112 40 112 124 0 19-3 36-8 51l-140-169c11-4 23-6 36-6z" glyph-name="oslash" horiz-adv-x="590" unicode="&#xf8;"/><glyph d="M388 0c-2 9-4 37-4 50-26-43-83-61-133-61-121 0-189 88-189 194v314h152v-282c0-48 26-86 80-86 51 0 83 35 83 85v283h152v-408c0-45 4-81 5-89h-146zM259 556l-142 151h173l93-151h-124z" glyph-name="ugrave" horiz-adv-x="596" unicode="&#xf9;"/><glyph d="M467 707l-141-151h-121l92 151h170zM388 0c-2 9-4 37-4 50-26-43-83-61-133-61-121 0-189 88-189 194v314h152v-282c0-48 26-86 80-86 51 0 83 35 83 85v283h152v-408c0-45 4-81 5-89h-146z" glyph-name="uacute" horiz-adv-x="596" unicode="&#xfa;"/><glyph d="M226 556h-113l113 151h143l114-151h-116l-71 71zM388 0c-2 9-4 37-4 50-26-43-83-61-133-61-121 0-189 88-189 194v314h152v-282c0-48 26-86 80-86 51 0 83 35 83 85v283h152v-408c0-45 4-81 5-89h-146z" glyph-name="ucircumflex" horiz-adv-x="596" unicode="&#xfb;"/><glyph d="M113 632c0 43 33 77 76 77s77-34 77-77-34-76-77-76-76 33-76 76zM334 632c0 43 33 77 76 77s77-34 77-77-34-76-77-76-76 33-76 76zM388 0c-2 9-4 37-4 50-26-43-83-61-133-61-121 0-189 88-189 194v314h152v-282c0-48 26-86 80-86 51 0 83 35 83 85v283h152v-408c0-45 4-81 5-89h-146z" glyph-name="udieresis" horiz-adv-x="596" unicode="&#xfc;"/><glyph d="M262-191h-160l113 258-209 430h170l121-266 110 266h161zM469 707l-141-151h-121l92 151h170z" glyph-name="yacute" horiz-adv-x="578" unicode="&#xfd;"/><glyph d="M219-190h-152v914h152v-272c23 30 75 57 141 57 144 0 231-110 231-259 0-152-98-261-237-261-64 0-112 22-135 50v-229zM442 249c0 83-53 123-113 123-59 0-112-40-112-123 0-82 53-124 112-124 60 0 113 41 113 124z" glyph-name="thorn" horiz-adv-x="622" unicode="&#xfe;"/><glyph d="M262-191h-160l113 258-209 430h170l121-266 110 266h161zM105 632c0 43 33 77 76 77s77-34 77-77-34-76-77-76-76 33-76 76zM326 632c0 43 33 77 76 77s77-34 77-77-34-76-77-76-76 33-76 76z" glyph-name="ydieresis" horiz-adv-x="578" unicode="&#xff;"/><glyph d="M219 0h-152v497h152v-497z" glyph-name="dotlessi" horiz-adv-x="285" unicode="&#x131;"/><glyph d="M569 0h-464v244l-91-42v123l91 42v342h158v-269l161 74v-122l-161-75v-166h306v-151z" glyph-name="Lslash" horiz-adv-x="594" unicode="&#x141;"/><glyph d="M249 0h-152v250l-83-38v123l83 38v351h152v-281l84 39v-122l-84-39v-321z" glyph-name="lslash" horiz-adv-x="347" unicode="&#x142;"/><glyph d="M200 355c0-148 106-214 205-214 98 0 204 66 204 214s-106 215-204 215c-99 0-205-67-205-215zM1060 0h-451v60c-49-49-115-75-204-75-200 0-369 145-369 369 0 225 169 370 369 370 89 0 157-27 204-75v60h450v-149h-291v-135h264v-139h-264v-136h292v-150z" glyph-name="OE" horiz-adv-x="1116" unicode="&#x152;"/><glyph d="M560 308h199c-2 38-28 84-100 84-64 0-96-47-99-84zM295 125c58 0 112 40 112 124s-54 123-112 123c-57 0-112-39-112-123 0-83 55-124 112-124zM295 512c74 0 140-27 187-73 46 47 109 73 175 73 154 0 249-95 249-255 0-21-2-44-3-47h-346c3-56 54-96 113-96 55 0 86 26 100 65l127-36c-24-86-103-158-229-158-71 0-138 26-186 74-47-46-113-74-187-74-148 0-264 110-264 264s116 263 264 263z" glyph-name="oe" horiz-adv-x="937" unicode="&#x153;"/><glyph d="M116 891h140l58-65 58 65h135l-123-132h-145zM565 535l-142-40c-5 33-32 89-118 89-59 0-97-37-97-76 0-34 21-59 70-68l95-18c135-25 205-112 205-213 0-111-93-224-262-224-196 0-280 127-290 227l145 35c6-65 50-122 147-122 62 0 100 30 100 74 0 36-29 62-76 71l-97 18c-120 23-193 103-193 208 0 134 118 228 254 228 177 0 244-106 259-189z" glyph-name="Scaron" horiz-adv-x="609" unicode="&#x160;"/><glyph d="M48 707h116l71-72 70 72h113l-112-151h-145zM26 141l129 23c2-34 26-67 78-67 39 0 58 21 58 43 0 18-12 33-49 41l-57 13c-106 23-148 83-148 153 0 91 80 165 191 165 144 0 194-90 199-148l-126-23c-4 33-25 61-71 61-29 0-54-17-54-43 0-21 17-33 39-37l66-13c103-21 153-83 153-156 0-85-65-168-198-168-156 0-206 101-210 156z" glyph-name="scaron" horiz-adv-x="466" unicode="&#x161;"/><glyph d="M139 824c0 45 33 80 78 80s80-35 80-80c0-44-35-77-80-77s-78 33-78 77zM384 824c0 45 33 80 77 80 45 0 80-35 80-80 0-44-35-77-80-77-44 0-77 33-77 77zM256 296l-246 413h185l145-263 147 263h176l-249-413v-296h-158v296z" glyph-name="Ydieresis" horiz-adv-x="673" unicode="&#x178;"/><glyph d="M131 891h140l58-65 58 65h135l-123-132h-145zM599 0h-563v152l342 406h-327v151h544v-146l-350-412h354v-151z" glyph-name="Zcaron" horiz-adv-x="644" unicode="&#x17d;"/><glyph d="M64 707h116l71-72 70 72h113l-112-151h-145zM453 0h-417v134l219 233h-213v130h407v-125l-229-241h233v-131z" glyph-name="zcaron" horiz-adv-x="489" unicode="&#x17e;"/><glyph d="M118 30l69 340h-93v126h118l17 82c23 114 115 163 198 163 50 0 65-5 97-16v-130c-21 5-41 8-67 8-23 0-66-10-78-68l-8-39h110v-126h-136l-77-383c-23-115-116-163-198-163-50 0-68 5-100 15v131c20-5 44-8 70-8 23 0 66 11 78 68z" glyph-name="florin" horiz-adv-x="529" unicode="&#x192;"/><glyph d="M135 556h-113l113 151h143l114-151h-116l-71 71z" glyph-name="circumflex" horiz-adv-x="414" unicode="&#x2c6;"/><glyph d="M22 707h116l71-72 70 72h113l-112-151h-145z" glyph-name="caron" horiz-adv-x="414" unicode="&#x2c7;"/><glyph d="M308 577h-286v105h286v-105z" glyph-name="uni02C9" horiz-adv-x="330" unicode="&#x2c9;"/><glyph d="M190 551c-110 0-166 74-168 156h96c2-26 22-56 72-56s70 30 72 56h97c-3-82-58-156-169-156z" glyph-name="breve" horiz-adv-x="380" unicode="&#x2d8;"/><glyph d="M12 633c0 49 39 87 89 87 49 0 89-38 89-87s-40-86-89-86c-50 0-89 37-89 86z" glyph-name="dotaccent" horiz-adv-x="201" unicode="&#x2d9;"/><glyph d="M254 652c0-66-54-111-116-111s-116 45-116 111 54 113 116 113 116-47 116-113zM175 652c0 23-16 38-37 38s-37-15-37-38 16-38 37-38 37 15 37 38z" glyph-name="ring" horiz-adv-x="276" unicode="&#x2da;"/><glyph d="M145-226c-75 0-126 43-126 109 0 77 58 113 103 133l76-16c-38-18-76-53-76-93 0-28 20-43 47-43 13 0 22 1 29 2v-84c-13-5-30-8-53-8z" glyph-name="ogonek" horiz-adv-x="220" unicode="&#x2db;"/><glyph d="M381 706v-20c0-88-47-130-112-130-30 0-53 11-76 23-20 10-37 21-56 21-20 0-33-13-34-43h-86v23c0 86 50 127 113 127 31 0 54-11 76-23 20-12 37-22 55-22 22 0 34 15 35 44h85z" glyph-name="tilde" horiz-adv-x="398" unicode="&#x2dc;"/><glyph d="M213 707l-102-151h-92l53 151h141zM391 707l-118-151h-93l69 151h142z" glyph-name="hungarumlaut" horiz-adv-x="410" unicode="&#x2dd;"/><glyph d="M0 0l266 709h173l266-709h-705zM352 539l-136-391h273z" glyph-name="uni0394" horiz-adv-x="705" unicode="&#x394;"/><glyph d="M387 575c-93 0-178-65-178-178 0-97 67-162 145-178v-219h-300v145h162c-95 39-171 133-171 266 0 191 160 314 342 314s342-123 342-314c0-133-75-227-171-266h163v-145h-301v219c79 16 146 81 146 178 0 113-85 178-179 178z" glyph-name="uni03A9" horiz-adv-x="774" unicode="&#x3a9;"/><glyph d="M394 0c-2 9-4 37-4 50-26-43-83-61-133-61-13 0-25 1-37 3v-182h-152v687h152v-282c0-48 26-86 80-86 51 0 83 35 83 85v283h152v-408c0-45 4-80 5-89h-146z" glyph-name="uni03BC" horiz-adv-x="606" unicode="&#x3bc;"/><glyph d="M613 5c-1 0-28-14-75-14-102 0-154 60-154 156v221h-116v-368h-153v368h-83v129h579v-129h-75v-200c0-36 24-45 46-45 20 0 22 1 31 3v-121z" glyph-name="pi" horiz-adv-x="649" unicode="&#x3c0;"/><glyph glyph-name="uni2007" horiz-adv-x="628" unicode="&#x2007;"/><glyph glyph-name="uni2008" horiz-adv-x="327" unicode="&#x2008;"/><glyph glyph-name="uni200B" horiz-adv-x="0" unicode="&#x200b;"/><glyph d="M578 236h-517v132h517v-132z" glyph-name="endash" horiz-adv-x="638" unicode="&#x2013;"/><glyph d="M1004 236h-933v132h933v-132z" glyph-name="emdash" horiz-adv-x="1074" unicode="&#x2014;"/><glyph d="M210 499c0-42-36-77-86-77-60 0-93 50-93 101 0 138 98 187 164 192v-68c-35-4-82-32-85-78 2 1 12 5 22 5 50 0 78-31 78-75z" glyph-name="quoteleft" horiz-adv-x="241" unicode="&#x2018;"/><glyph d="M31 637c0 42 35 77 86 77 60 0 93-49 93-101 0-138-98-187-164-192v68c35 4 81 30 85 77 0-1-12-5-22-5-50 0-78 33-78 76z" glyph-name="quoteright" horiz-adv-x="241" unicode="&#x2019;"/><glyph d="M31 73c0 42 35 77 86 77 60 0 93-50 93-101 0-139-98-187-164-192v67c35 4 81 30 85 77-4-2-13-5-22-5-50 0-78 33-78 77z" glyph-name="quotesinglbase" horiz-adv-x="241" unicode="&#x201a;"/><glyph d="M210 499c0-42-36-77-86-77-60 0-93 50-93 101 0 138 98 187 164 192v-68c-35-4-82-31-85-77 2 1 12 5 22 5 50 0 78-32 78-76zM420 499c0-42-36-77-86-77-60 0-92 50-92 101 0 138 97 187 163 192v-68c-35-4-82-31-85-77 2 1 12 5 22 5 50 0 78-32 78-76z" glyph-name="quotedblleft" horiz-adv-x="451" unicode="&#x201c;"/><glyph d="M31 637c0 42 35 77 86 77 59 0 93-49 93-101 0-138-98-186-164-191v67c35 4 81 30 85 77-2-1-10-5-22-5-50 0-78 33-78 76zM239 637c0 42 35 77 86 77 59 0 92-49 92-101 0-138-97-186-163-191v67c35 4 81 30 85 77-4-2-11-5-22-5-50 0-78 33-78 76z" glyph-name="quotedblright" horiz-adv-x="448" unicode="&#x201d;"/><glyph d="M31 73c0 42 35 77 86 77 60 0 93-50 93-101 0-139-98-187-164-192v67c35 4 81 30 85 77-4-2-13-5-22-5-50 0-78 33-78 77zM238 73c0 42 35 77 86 77 60 0 93-50 93-101 0-139-98-187-164-192v67c35 4 81 30 85 77-4-2-13-5-22-5-50 0-78 33-78 77z" glyph-name="quotedblbase" horiz-adv-x="448" unicode="&#x201e;"/><glyph d="M276 163h-126v293h-120v114h120v139h126v-139h121v-114h-121v-293z" glyph-name="dagger" horiz-adv-x="427" unicode="&#x2020;"/><glyph d="M50 255h120v199h-120v114h120v141h126v-141h121v-114h-121v-199h121v-114h-121v-141h-126v141h-120v114z" glyph-name="daggerdbl" horiz-adv-x="467" unicode="&#x2021;"/><glyph d="M52 303c0 116 94 210 209 210s209-94 209-210c0-113-94-208-209-208s-209 95-209 208z" glyph-name="bullet" horiz-adv-x="522" unicode="&#x2022;"/><glyph d="M848 75c0-47-39-88-89-88-48 0-88 41-88 88 0 50 40 90 88 90 50 0 89-40 89-90zM546 75c0-47-39-88-89-88-49 0-88 41-88 88 0 50 39 90 88 90 50 0 89-40 89-90zM245 75c0-47-40-88-89-88s-88 41-88 88c0 50 39 90 88 90s89-40 89-90z" glyph-name="ellipsis" horiz-adv-x="916" unicode="&#x2026;"/><glyph d="M147 494c0-39 26-65 61-65s60 27 60 65c0 39-25 65-60 65s-61-26-61-65zM39 494c0 94 77 167 169 167s169-73 169-167c0-95-77-165-170-165s-168 73-168 165zM602 155c0-40 28-66 63-66s62 27 62 66c0 38-27 64-62 64s-63-26-63-64zM274 0h-128l454 649h129zM849 155c0-40 28-66 63-66s62 27 62 66c0 38-27 64-62 64s-63-26-63-64zM494 155c0 93 79 166 171 166 51 0 94-22 123-57 29 35 73 57 124 57 92 0 171-73 171-166 0-96-79-166-172-166-51 0-94 21-123 55-29-35-73-55-124-55-93 0-170 73-170 166z" glyph-name="perthousand" horiz-adv-x="1128" unicode="&#x2030;"/><glyph d="M306 102h-142l-138 195 140 200h141l-139-201z" glyph-name="guilsinglleft" horiz-adv-x="323" unicode="&#x2039;"/><glyph d="M159 102h-143l139 194-139 201h142l139-200z" glyph-name="guilsinglright" horiz-adv-x="323" unicode="&#x203a;"/><glyph d="M467 708v-105l97 32 33-94-99-33 60-83-80-59-62 84-62-84-80 59 59 83-98 33 32 94 97-32v105h103zM266 327v-105l97 32 33-94-99-33 60-83-80-59-62 84-62-84-80 59 59 83-98 33 32 94 97-32v105h103zM676 327v-105l97 32 33-94-99-33 60-83-80-59-62 84-62-84-80 59 59 83-98 33 32 94 97-32v105h103z" glyph-name="uni2042" horiz-adv-x="839" unicode="&#x2042;"/><glyph d="M128 0h-128l454 649h129z" glyph-name="fraction" horiz-adv-x="583" unicode="&#x2044;"/><glyph d="M267 708v-105l97 32 33-94-99-33 60-83-80-59-62 84-62-84-80 59 59 83-98 33 32 94 97-32v105h103zM267 331v-105l97 32 33-94-99-33 60-83-80-59-62 84-62-84-80 59 59 83-98 33 32 94 97-32v105h103z" glyph-name="uni2051" horiz-adv-x="430" unicode="&#x2051;"/><glyph d="M163 525c0-49 7-100 56-100s56 51 56 100-7 100-56 100-56-51-56-100zM47 525c0 45 8 92 36 130s73 63 136 63c64 0 109-25 137-63s36-85 36-130-8-92-36-130c-28-37-73-63-137-63-63 0-108 26-136 64-28 37-36 84-36 129z" glyph-name="uni2070" horiz-adv-x="439" unicode="&#x2070;"/><glyph d="M47 411v112l164 186h121v-213h59v-85h-59v-71h-111v71h-174zM221 496v102l-91-102h91z" glyph-name="uni2074" horiz-adv-x="438" unicode="&#x2074;"/><glyph d="M47 454l100 18c2-27 26-45 55-45 27 0 46 15 46 40 0 32-21 46-50 46-19 0-36-8-47-20l-92 26 44 190h231v-90h-165l-12-52c17 13 45 20 71 20 80 0 130-44 130-119 0-73-55-134-156-134-86 0-148 53-155 120z" glyph-name="uni2075" horiz-adv-x="405" unicode="&#x2075;"/><glyph d="M360 701l-19-88c-17 6-41 11-67 11-46 0-96-17-114-71 21 18 51 28 82 28 72 0 130-39 130-116 0-79-65-135-161-135-93 0-164 66-164 170 0 136 98 215 221 215 31 0 61-5 92-14zM165 461c0-24 20-42 46-42s47 18 47 42c0 26-21 44-47 44s-46-18-46-44z" glyph-name="uni2076" horiz-adv-x="419" unicode="&#x2076;"/><glyph d="M359 709v-92c-34-29-121-112-136-277h-120c11 175 123 276 123 276h-179v93h312z" glyph-name="uni2077" horiz-adv-x="405" unicode="&#x2077;"/><glyph d="M212 563c27 1 40 18 40 34 0 14-12 32-40 32s-40-18-40-32c0-16 13-33 40-34zM212 418c36 0 49 21 49 39 0 17-14 36-49 36s-49-19-49-36c0-18 13-39 49-39zM212 332c-101 0-161 44-161 110 0 43 33 77 76 88-42 12-65 47-65 83 0 58 52 104 150 104 99 0 150-46 150-104 0-36-22-71-65-83 41-11 76-44 76-84 0-68-57-114-161-114z" glyph-name="uni2078" horiz-adv-x="424" unicode="&#x2078;"/><glyph d="M66 345l17 88c16-7 39-11 62-11 50 0 103 15 118 72-21-18-52-28-84-28-65 0-132 39-132 116 0 79 61 136 161 136 94 0 163-68 163-171 0-139-94-217-214-217-30 0-61 6-91 15zM251 586c0 24-20 42-46 42s-46-18-46-42c0-26 20-43 46-43s46 17 46 43z" glyph-name="uni2079" horiz-adv-x="419" unicode="&#x2079;"/><glyph d="M163 110c0-49 7-100 56-100s56 51 56 100-7 100-56 100-56-51-56-100zM47 110c0 45 8 92 36 130s73 63 136 63c64 0 109-25 137-63s36-85 36-130-8-92-36-130c-28-37-73-63-137-63-63 0-108 26-136 64-28 37-36 84-36 129z" glyph-name="uni2080" horiz-adv-x="439" unicode="&#x2080;"/><glyph d="M241-120h-115v212h-94v74c70 0 101 47 108 82h101v-368z" glyph-name="uni2081" horiz-adv-x="308" unicode="&#x2081;"/><glyph d="M154 103h-100c-2 8-4 19-4 27 0 68 49 128 149 128 94 0 148-57 148-125 0-48-31-83-84-114l-67-39c-3-2-5-3-5-5h159v-95h-302v13c0 68 22 123 91 162l60 34c23 13 36 28 36 45 0 13-9 34-39 34s-45-22-45-48c0-9 2-15 3-17z" glyph-name="uni2082" horiz-adv-x="396" unicode="&#x2082;"/><glyph d="M47-7l103 18c1-25 21-46 52-46 29 0 47 16 47 37 0 28-22 40-46 40-10 0-20-2-29-6l-35 75 79 47h-158v92h291v-93l-85-51c63-10 98-50 98-107 0-66-60-127-162-127-93 0-150 60-155 121z" glyph-name="uni2083" horiz-adv-x="411" unicode="&#x2083;"/><glyph d="M47-49v112l164 186h121v-213h59v-85h-59v-71h-111v71h-174zM221 36v102l-91-102h91z" glyph-name="uni2084" horiz-adv-x="438" unicode="&#x2084;"/><glyph d="M47-6l100 18c2-27 26-45 55-45 27 0 46 15 46 40 0 32-21 46-50 46-19 0-36-8-47-20l-92 26 44 190h231v-90h-165l-12-52c17 13 45 20 71 20 80 0 130-44 130-119 0-73-55-134-156-134-86 0-148 53-155 120z" glyph-name="uni2085" horiz-adv-x="405" unicode="&#x2085;"/><glyph d="M360 243l-19-88c-17 6-41 11-67 11-46 0-96-17-114-71 21 18 51 28 82 28 72 0 130-39 130-116 0-79-65-135-161-135-93 0-164 66-164 170 0 136 98 215 221 215 31 0 61-5 92-14zM165 3c0-24 20-42 46-42s47 18 47 42c0 26-21 44-47 44s-46-18-46-44z" glyph-name="uni2086" horiz-adv-x="419" unicode="&#x2086;"/><glyph d="M359 248v-92c-34-29-121-112-136-277h-120c11 175 123 276 123 276h-179v93h312z" glyph-name="uni2087" horiz-adv-x="405" unicode="&#x2087;"/><glyph d="M212 103c27 1 40 18 40 34 0 14-12 32-40 32s-40-18-40-32c0-16 13-33 40-34zM212-42c36 0 49 21 49 39 0 17-14 36-49 36s-49-19-49-36c0-18 13-39 49-39zM212-128c-101 0-161 44-161 110 0 43 33 77 76 88-42 12-65 47-65 83 0 58 52 104 150 104 99 0 150-46 150-104 0-36-22-71-65-83 41-11 76-44 76-84 0-68-57-114-161-114z" glyph-name="uni2088" horiz-adv-x="424" unicode="&#x2088;"/><glyph d="M66-113l17 88c16-7 39-11 62-11 50 0 103 15 118 72-21-18-52-28-84-28-65 0-132 39-132 116 0 79 61 136 161 136 94 0 163-68 163-171 0-139-94-217-214-217-30 0-61 6-91 15zM251 128c0 24-20 42-46 42s-46-18-46-42c0-26 20-43 46-43s46 17 46 43z" glyph-name="uni2089" horiz-adv-x="419" unicode="&#x2089;"/><glyph d="M593 161l69-114c-44-35-111-63-191-63-148 0-270 78-319 209h-91v102h70c-1 10-1 16-1 28 0 8 0 16 1 26h-70v101h91c49 131 174 214 317 214 76 0 127-17 172-45l-56-120c-29 20-70 29-116 29-57 0-115-23-150-78h244l-47-101h-227c-1-10-1-17-1-26 0-8 0-18 1-28h206l-45-102h-125c34-47 87-70 146-70 51 0 93 15 122 38z" glyph-name="Euro" horiz-adv-x="674" unicode="&#x20ac;"/><glyph d="M26 277c19 5 43 12 70 22v193c0 107 69 171 160 171 95 0 157-59 157-164 0-97-69-203-182-275v-21c0-60 43-77 69-77 51 0 75 12 92 22v-124c-12-10-43-29-111-29-109 0-179 63-185 167-22-7-46-12-70-16v131zM279 506c0 20-11 31-23 31-11 0-25-7-25-37v-114c28 32 48 72 48 120z" glyph-name="uni2113" horiz-adv-x="439" unicode="&#x2113;"/><glyph d="M336 618h-99v-274h-102v274h-100v91h301v-91zM824 344h-100v222l-82-222h-76l-83 218v-218h-99v365h142l80-194 76 194h142v-365z" glyph-name="trademark" horiz-adv-x="884" unicode="&#x2122;"/><glyph d="M387 575c-93 0-178-65-178-178 0-97 67-162 145-178v-219h-300v145h162c-95 39-171 133-171 266 0 191 160 314 342 314s342-123 342-314c0-133-75-227-171-266h163v-145h-301v219c79 16 146 81 146 178 0 113-85 178-179 178z" glyph-name="Omega" horiz-adv-x="774" unicode="&#x2126;"/><glyph d="M452 649v-131h-126v-387h126v-131h-410v131h127v387h-127v131h410z" glyph-name="uni2160" horiz-adv-x="493" unicode="&#x2160;"/><glyph d="M752 131v-131h-710v131h135v387h-135v131h710v-131h-134v-387h134zM334 518v-387h127v387h-127z" glyph-name="uni2161" horiz-adv-x="793" unicode="&#x2161;"/><glyph d="M1026 131v-131h-984v131h135v387h-135v131h984v-131h-134v-387h134zM334 518v-387h122v387h-122zM613 518v-387h122v387h-122z" glyph-name="uni2162" horiz-adv-x="1067" unicode="&#x2162;"/><glyph d="M1036 131v-131h-994v131h132v387h-132v131h994v-131h-104l-121-387h225zM331 518v-387h200l-124 387h-76zM673 179l99 339h-200z" glyph-name="uni2163" horiz-adv-x="1077" unicode="&#x2163;"/><glyph d="M769 649v-131h-104l-120-387h223v-131h-726v131h223l-124 387h-99v131h727zM407 179l99 339h-200z" glyph-name="uni2164" horiz-adv-x="810" unicode="&#x2164;"/><glyph d="M1031 131v-131h-989v131h223l-124 387h-99v131h989v-131h-133v-387h133zM666 518l-120-387h195v387h-75zM407 179l99 339h-200z" glyph-name="uni2165" horiz-adv-x="1073" unicode="&#x2165;"/><glyph d="M1306 131v-131h-1264v131h223l-124 387h-99v131h1264v-131h-133v-387h133zM1016 131v387h-118v-387h118zM666 518l-120-387h195v387h-75zM407 179l99 339h-200z" glyph-name="uni2166" horiz-adv-x="1348" unicode="&#x2166;"/><glyph d="M1578 131v-131h-1536v131h223l-124 387h-99v131h1535v-131h-132v-387h133zM665 518l-120-387h196v387h-76zM1171 518v-387h117v387h-117zM898 518v-387h116v387h-116zM407 179l99 339h-200z" glyph-name="uni2167" horiz-adv-x="1619" unicode="&#x2167;"/><glyph d="M1010 131v-131h-968v131h132v387h-132v131h968v-131h-103l-145-197 143-190h105zM555 322l-146 196h-78v-387h80zM596 518l63-89 64 89h-127zM657 216l-62-85h123z" glyph-name="uni2168" horiz-adv-x="1052" unicode="&#x2168;"/><glyph d="M745 649v-131h-103l-145-197 143-190h105v-131h-703v131h104l144 191-146 196h-102v131h703zM332 518l63-89 63 89h-126zM392 216l-61-85h122z" glyph-name="uni2169" horiz-adv-x="787" unicode="&#x2169;"/><glyph d="M619 297v-297h-577v131h111v387h-111v131h389v-131h-121v-387h161v166h148z" glyph-name="uni216C" horiz-adv-x="660" unicode="&#x216c;"/><glyph d="M701 252v-252h-152v55c-37-40-92-71-177-71-187 0-330 139-330 339s145 341 327 341c84 0 138-30 174-69v54h153v-247h-153c-9 44-48 125-166 125-87 0-184-64-184-204 0-125 88-198 187-198 116 0 158 82 169 127h152z" glyph-name="uni216D" horiz-adv-x="743" unicode="&#x216d;"/><glyph d="M382 649c199 0 323-127 323-325 0-199-125-324-324-324h-339v131h117v387h-117v131h340zM316 131h53c99 1 177 64 177 193s-78 194-178 194h-52v-387z" glyph-name="uni216E" horiz-adv-x="747" unicode="&#x216e;"/><glyph d="M373 131v-131h-331v131h111v387h-111v131h324l155-391 146 391h334v-131h-110v-387h110v-131h-341v131h75v282l-158-404h-114l-158 400v-278h68z" glyph-name="uni216F" horiz-adv-x="1042" unicode="&#x216f;"/><glyph d="M271 57l-285 244 285 246v-180h753v-131h-753v-179z" glyph-name="arrowleft" horiz-adv-x="1080" unicode="&#x2190;"/><glyph d="M273 800l243-277h-177v-669h-131v669h-181z" glyph-name="arrowup" horiz-adv-x="504" unicode="&#x2191;"/><glyph d="M1084 301l-314-247v182h-749v131h749v182z" glyph-name="arrowright" horiz-adv-x="1114" unicode="&#x2192;"/><glyph d="M479 131l-245-279-248 279h183v668h131v-668h179z" glyph-name="arrowdown" horiz-adv-x="492" unicode="&#x2193;"/><glyph d="M69 708h352l-130-131 499-498-92-92-499 498-130-131v354z" glyph-name="uni2196" horiz-adv-x="877" unicode="&#x2196;"/><glyph d="M310 708h352v-354l-130 131-499-498-93 92 499 499z" glyph-name="uni2197" horiz-adv-x="749" unicode="&#x2197;"/><glyph d="M763 1h-352l129 130-498 499 92 92 499-498 130 131v-354z" glyph-name="uni2198" horiz-adv-x="850" unicode="&#x2198;"/><glyph d="M384 1h-352v354l130-131 499 498 92-92-499-498z" glyph-name="uni2199" horiz-adv-x="740" unicode="&#x2199;"/><glyph d="M252 24l-284 244 284 246v-182h359c72 0 115 43 115 92 0 53-43 91-115 91h-204v131h210c146 0 246-95 246-222 0-129-97-224-246-224h-365v-176z" glyph-name="uni21A9" horiz-adv-x="874" unicode="&#x21a9;"/><glyph d="M316 235l-284 245 284 245v-180h427v-545h-131v414h-296v-179z" glyph-name="uni21B0" horiz-adv-x="819" unicode="&#x21b0;"/><glyph d="M166 0h-131v545h432v180l284-245-284-245v179h-301v-414z" glyph-name="uni21B1" horiz-adv-x="764" unicode="&#x21b1;"/><glyph d="M570 709h131v-545h-431v-180l-285 246 285 244v-179h300v414z" glyph-name="uni21B2" horiz-adv-x="777" unicode="&#x21b2;"/><glyph d="M499 474l284-244-284-246v180h-432v545h131v-414h301v179z" glyph-name="uni21B3" horiz-adv-x="857" unicode="&#x21b3;"/><glyph d="M64 578v131h558v-449h180l-242-275-246 275h177v318h-427z" glyph-name="uni21B4" horiz-adv-x="792" unicode="&#x21b4;"/><glyph d="M1142 158l-284-244v179h-757v131h757v180zM353 320l-284 244 284 246v-183h756v-131h-756v-176z" glyph-name="uni21C6" horiz-adv-x="1197" unicode="&#x21c6;"/><glyph d="M255 580c-28 0-82-3-148-35l-54 123c76 41 152 52 210 52 130 0 327-61 327-372 0-199-91-364-297-364-135 0-261 84-261 248 0 150 129 230 242 230 99 0 138-34 156-55-12 131-87 173-175 173zM311 335c-85 0-125-48-125-109 0-64 58-102 112-102 97 0 130 83 136 163-28 28-66 48-123 48z" glyph-name="partialdiff" horiz-adv-x="626" unicode="&#x2202;"/><glyph d="M0 0l266 709h173l266-709h-705zM352 539l-136-391h273z" glyph-name="Delta" horiz-adv-x="705" unicode="&#x2206;"/><glyph d="M609 0h-159v563h-215v-563h-158v709h532v-709z" glyph-name="product" horiz-adv-x="685" unicode="&#x220f;"/><glyph d="M32 0v145l246 208-246 212v144h523v-147h-305l242-209-251-206h314v-147h-523z" glyph-name="summation" horiz-adv-x="571" unicode="&#x2211;"/><glyph d="M547 237h-501v124h501v-124z" glyph-name="minus" horiz-adv-x="593" unicode="&#x2212;"/><glyph d="M464 662l-329-676h-135l328 676h136z" glyph-name="uni2215" horiz-adv-x="464" unicode="&#x2215;"/><glyph d="M57 322c0 51 42 91 94 91s94-40 94-91c0-53-42-92-94-92s-94 39-94 92z" glyph-name="uni2219" horiz-adv-x="293" unicode="&#x2219;"/><glyph d="M744 762h-138l-253-762h-132l-190 542h140l117-346 225 689h231v-123z" glyph-name="radical" horiz-adv-x="714" unicode="&#x221a;"/><glyph d="M422 188c-58-60-114-92-180-92-117 0-201 83-201 194s84 195 201 195c66 0 127-34 183-95 56 58 116 95 180 95 116 0 201-84 201-195s-85-194-201-194c-69 0-126 31-183 92zM244 353c-40 0-66-28-66-64s26-63 66-63c33 0 66 25 96 61-32 38-63 66-96 66zM603 226c39 0 66 27 66 63s-27 64-66 64c-34 0-65-27-97-64 31-36 63-63 97-63z" glyph-name="infinity" horiz-adv-x="846" unicode="&#x221e;"/><glyph d="M109-9v520c0 122 75 199 198 199h58v-133h-53c-30 0-50-21-50-58v-520c0-122-75-199-198-199h-59v133h54c29 0 50 21 50 58z" glyph-name="integral" horiz-adv-x="370" unicode="&#x222b;"/><glyph d="M497 479l79-65c-36-70-99-108-169-109-37 0-75 10-113 31l-48 26c-16 9-34 13-50 13-32 0-63-18-84-49l-81 65c36 70 99 109 169 109 36 0 74-8 112-29l47-26c16-9 34-14 51-14 33 0 64 15 87 48zM497 275l79-66c-37-70-100-108-170-108-37 0-74 9-112 30l-48 26c-16 8-32 13-49 13-31 0-62-16-85-48l-81 65c37 70 100 108 171 108 34 0 73-9 110-29l47-26c17-9 34-14 51-14 33 0 64 15 87 49z" glyph-name="approxequal" horiz-adv-x="607" unicode="&#x2248;"/><glyph d="M551 138h-275l-59-96h-132l59 96h-88v122h163l48 79h-211v122h286l54 88h132l-54-88h77v-122h-152l-48-79h200v-122z" glyph-name="notequal" horiz-adv-x="607" unicode="&#x2260;"/><glyph d="M52 164h439v-124h-439v124zM52 322v120l439 130v-123l-266-65 266-66v-126z" glyph-name="lessequal" horiz-adv-x="547" unicode="&#x2264;"/><glyph d="M56 164h439v-124h-439v124zM495 442v-120l-439-130v123l266 65-266 67v125z" glyph-name="greaterequal" horiz-adv-x="547" unicode="&#x2265;"/><glyph d="M57 322c0 51 42 91 94 91s94-40 94-91c0-53-42-92-94-92s-94 39-94 92z" glyph-name="dotmath" horiz-adv-x="293" unicode="&#x22c5;"/><glyph d="M125 354c0-161 122-288 281-288s282 127 282 288-123 289-282 289-281-128-281-289zM36 354c0 204 166 371 370 371s371-167 371-371-167-370-371-370-370 166-370 370zM385 372h44c32 0 53 18 53 48s-21 49-53 49h-44v-97zM450 288h-65v-134h-104v399h169c77 0 137-60 137-133s-60-132-137-132z" glyph-name="uni24C5" horiz-adv-x="813" unicode="&#x24c5;"/><glyph d="M97 0v709h708v-709h-708z" glyph-name="filledbox" horiz-adv-x="901" unicode="&#x25a0;"/><glyph d="M805 0h-708v709h708v-709zM227 579v-450h444v450h-444z" glyph-name="H22073" horiz-adv-x="901" unicode="&#x25a1;"/><glyph d="M421 725l418-725h-839z" glyph-name="triagup" horiz-adv-x="839" unicode="&#x25b2;"/><glyph d="M419 725l419-725h-838zM611 130l-192 333-192-333h384z" glyph-name="uni25B3" horiz-adv-x="838" unicode="&#x25b3;"/><glyph d="M201 354l123-212 123 212-123 213zM26 354l210 355h176l210-355-210-354h-176z" glyph-name="lozenge" horiz-adv-x="648" unicode="&#x25ca;"/><glyph d="M32 354c0 205 165 371 370 371s371-166 371-371-166-370-371-370-370 165-370 370zM162 354c0-132 108-241 240-241s238 109 238 241-106 242-238 242-240-110-240-242z" glyph-name="circle" horiz-adv-x="804" unicode="&#x25cb;"/><glyph d="M32 354c0 205 165 371 370 371s371-166 371-371-166-370-371-370-370 165-370 370z" glyph-name="H18533" horiz-adv-x="804" unicode="&#x25cf;"/><glyph d="M776 354c0-205-165-370-370-370-204 0-370 165-370 370s166 371 370 371c205 0 370-166 370-371zM646 354c0 113-77 208-179 236v-170l169-132c7 21 10 43 10 66zM164 354c0-23 3-44 9-64l167 130v169c-101-29-176-124-176-235zM467 118c40 10 76 32 106 60l-106 87v-147zM235 181c30-29 65-51 105-62v146z" glyph-name="uni262E" horiz-adv-x="812" unicode="&#x262e;"/><glyph d="M916 633l-606-649-298 329 95 89 207-226 510 549z" glyph-name="uni2713" horiz-adv-x="928" unicode="&#x2713;"/><glyph d="M649-5l-273 270-269-270-91 91 268 269-268 266 91 90 268-265 264 265 91-90-264-265 273-270z" glyph-name="uni2715" horiz-adv-x="755" unicode="&#x2715;"/><glyph d="M129 354c0-161 123-288 282-288 158 0 281 127 281 288s-123 289-281 289c-159 0-282-128-282-289zM41 354c0 204 165 371 370 371 204 0 370-167 370-371s-166-370-370-370c-205 0-370 166-370 370zM488 172h-115v212h-94v74c70 0 101 47 108 82h101v-368z" glyph-name="uni2780" horiz-adv-x="822" unicode="&#x2780;"/><glyph d="M129 354c0-161 123-288 282-288 158 0 281 127 281 288s-123 289-281 289c-159 0-282-128-282-289zM41 354c0 204 165 371 370 371 204 0 370-167 370-371s-166-370-370-370c-205 0-370 166-370 370zM363 407h-100c-2 8-4 19-4 27 0 68 49 128 149 128 94 0 148-57 148-125 0-48-31-83-84-114l-67-39c-3-2-5-3-5-5h159v-95h-302v13c0 68 22 123 91 162l60 34c23 13 36 28 36 45 0 13-9 34-39 34s-45-22-45-48c0-9 2-15 3-17z" glyph-name="uni2781" horiz-adv-x="822" unicode="&#x2781;"/><glyph d="M129 354c0-161 123-288 282-288 158 0 281 127 281 288s-123 289-281 289c-159 0-282-128-282-289zM41 354c0 204 165 371 370 371 204 0 370-167 370-371s-166-370-370-370c-205 0-370 166-370 370zM254 277l103 18c1-25 21-46 52-46 29 0 47 16 47 37 0 28-22 40-46 40-10 0-20-2-29-6l-35 75 79 47h-158v92h291v-93l-85-51c63-10 98-50 98-107 0-66-60-127-162-127-93 0-150 60-155 121z" glyph-name="uni2782" horiz-adv-x="822" unicode="&#x2782;"/><glyph d="M129 354c0-161 123-288 282-288 158 0 281 127 281 288s-123 289-281 289c-159 0-282-128-282-289zM41 354c0 204 165 371 370 371 204 0 370-167 370-371s-166-370-370-370c-205 0-370 166-370 370zM230 249v112l164 186h121v-213h59v-85h-59v-71h-111v71h-174zM404 334v102l-91-102h91z" glyph-name="uni2783" horiz-adv-x="822" unicode="&#x2783;"/><glyph d="M129 354c0-161 123-288 282-288 158 0 281 127 281 288s-123 289-281 289c-159 0-282-128-282-289zM41 354c0 204 165 371 370 371 204 0 370-167 370-371s-166-370-370-370c-205 0-370 166-370 370zM249 276l100 18c2-27 26-45 55-45 27 0 46 15 46 40 0 32-21 46-50 46-19 0-36-8-47-20l-92 26 44 190h231v-90h-165l-12-52c17 13 45 20 71 20 80 0 130-44 130-119 0-73-55-134-156-134-86 0-148 53-155 120z" glyph-name="uni2784" horiz-adv-x="822" unicode="&#x2784;"/><glyph d="M129 354c0-161 123-288 282-288 158 0 281 127 281 288s-123 289-281 289c-159 0-282-128-282-289zM41 354c0 204 165 371 370 371 204 0 370-167 370-371s-166-370-370-370c-205 0-370 166-370 370zM549 534l-19-88c-17 6-41 11-67 11-46 0-96-17-114-71 21 18 51 28 82 28 72 0 130-39 130-116 0-79-65-135-161-135-93 0-164 66-164 170 0 136 98 215 221 215 31 0 61-5 92-14zM354 294c0-24 20-42 46-42s47 18 47 42c0 26-21 44-47 44s-46-18-46-44z" glyph-name="uni2785" horiz-adv-x="822" unicode="&#x2785;"/><glyph d="M129 354c0-161 123-288 282-288 158 0 281 127 281 288s-123 289-281 289c-159 0-282-128-282-289zM41 354c0 204 165 371 370 371 204 0 370-167 370-371s-166-370-370-370c-205 0-370 166-370 370zM564 529v-92c-34-29-121-112-136-277h-120c11 175 123 276 123 276h-179v93h312z" glyph-name="uni2786" horiz-adv-x="822" unicode="&#x2786;"/><glyph d="M129 354c0-161 123-288 282-288 158 0 281 127 281 288s-123 289-281 289c-159 0-282-128-282-289zM41 354c0 204 165 371 370 371 204 0 370-167 370-371s-166-370-370-370c-205 0-370 166-370 370zM411 396c27 1 40 18 40 34 0 14-12 32-40 32s-40-18-40-32c0-16 13-33 40-34zM411 251c36 0 49 21 49 39 0 17-14 36-49 36s-49-19-49-36c0-18 13-39 49-39zM411 165c-101 0-161 44-161 110 0 43 33 77 76 88-42 12-65 47-65 83 0 58 52 104 150 104 99 0 150-46 150-104 0-36-22-71-65-83 41-11 76-44 76-84 0-68-57-114-161-114z" glyph-name="uni2787" horiz-adv-x="822" unicode="&#x2787;"/><glyph d="M129 354c0-161 123-288 282-288 158 0 281 127 281 288s-123 289-281 289c-159 0-282-128-282-289zM41 354c0 204 165 371 370 371 204 0 370-167 370-371s-166-370-370-370c-205 0-370 166-370 370zM276 178l17 88c16-7 39-11 62-11 50 0 103 15 118 72-21-18-52-28-84-28-65 0-132 39-132 116 0 79 61 136 161 136 94 0 163-68 163-171 0-139-94-217-214-217-30 0-61 6-91 15zM461 419c0 24-20 42-46 42s-46-18-46-42c0-26 20-43 46-43s46 17 46 43z" glyph-name="uni2788" horiz-adv-x="822" unicode="&#x2788;"/><glyph d="M223-15l-242 275h180v449h558v-131h-427v-318h176z" glyph-name="uniE000" horiz-adv-x="711" unicode="&#xe000;"/><glyph d="M265 83l-233 271h164c0 205 166 371 371 371 60 0 119-14 169-41l-60-116c-33 18-70 27-109 27-132 0-240-109-240-241h170zM932 354c0-205-165-370-370-370-60 0-118 15-169 41l60 115c33-17 70-27 109-27 131 0 237 109 237 241h-169l232 271 233-271h-163z" glyph-name="uni21B5" horiz-adv-x="1125" unicode="&#xe001;"/><glyph d="M478 354l232 271 233-271h-161c0-205-165-370-370-370s-371 165-371 370 166 371 371 371c51 0 100-10 145-29l-51-121c-28 13-60 20-94 20-132 0-240-109-240-241s108-241 240-241c131 0 237 109 237 241h-171z" glyph-name="uniE002" horiz-adv-x="960" unicode="&#xe002;"/><glyph d="M677 369h-108v-369h-152v369h-175v-369h-152v369h-79v128h79v43c0 114 69 192 190 192 25 0 58-4 72-11v-124c-8 2-22 5-45 5-26 0-65-12-65-62v-43h175v43c0 114 69 192 189 192 26 0 59-3 73-10v-125c-8 2-23 5-45 5-27 0-65-12-65-62v-43h108v-128z" glyph-name="uniFB00" horiz-adv-x="690" unicode="&#xfb00;"/><glyph d="M425 369h-183v-369h-152v369h-79v128h79v43c0 114 69 192 190 192 25 0 56-4 70-11v-124c-7 2-21 5-43 5-26 0-65-12-65-62v-43h335v-497h-152v369zM412 649c0 50 40 89 88 89 50 0 90-39 90-89 0-49-40-89-90-89-48 0-88 40-88 89z" glyph-name="fi" horiz-adv-x="643" unicode="&#xfb01;"/><glyph d="M307 602c-26 0-65-12-65-62v-43h182v227h153v-724h-153v369h-182v-369h-152v369h-79v128h79v43c0 114 69 192 190 192 25 0 56-4 70-11v-124c-7 2-21 5-43 5z" glyph-name="fl" horiz-adv-x="644" unicode="&#xfb02;"/><glyph d="M280 732c25 0 58-5 72-12v-123c-8 2-22 5-45 5-29 0-65-12-65-62v-43h175v43c0 114 69 192 189 192 26 0 59-4 73-11v-124c-8 2-23 5-45 5-27 0-65-12-65-62v-43h339v-497h-153v369h-186v-369h-152v369h-175v-369h-152v369h-79v128h79v43c0 114 69 192 190 192zM742 649c0 50 40 89 89 89 50 0 89-39 89-89 0-49-39-89-89-89-49 0-89 40-89 89z" glyph-name="uniFB03" horiz-adv-x="973" unicode="&#xfb03;"/><glyph d="M307 602c-26 0-65-12-65-62v-43h175v43c0 114 69 192 189 192 26 0 59-4 73-11v-124c-8 2-23 5-45 5-27 0-65-12-65-62v-43h187v227h152v-724h-152v369h-187v-369h-152v369h-175v-369h-152v369h-79v128h79v43c0 114 69 192 190 192 25 0 58-4 72-11v-124c-8 2-22 5-45 5z" glyph-name="uniFB04" horiz-adv-x="975" unicode="&#xfb04;"/><glyph glyph-name="uniFEFF" horiz-adv-x="0" unicode="&#xfeff;"/><glyph d="M163 465c0-49 7-100 56-100s56 51 56 100-7 100-56 100-56-51-56-100zM47 465c0 45 8 92 36 130s73 63 136 63c64 0 109-25 137-63s36-85 36-130-8-92-36-130c-28-37-73-63-137-63-63 0-108 26-136 64-28 37-36 84-36 129z" glyph-name="zero.numr" horiz-adv-x="439"/><glyph d="M241 281h-115v212h-94v74c70 0 101 47 108 82h101v-368z" glyph-name="one.numr" horiz-adv-x="317"/><glyph d="M154 503h-100c-2 8-4 19-4 27 0 68 49 128 149 128 94 0 148-57 148-125 0-48-31-83-84-114l-67-39c-3-2-5-3-5-5h159v-95h-302v13c0 68 22 123 91 162l60 34c23 13 36 28 36 45 0 13-9 34-39 34s-45-22-45-48c0-9 2-15 3-17z" glyph-name="two.numr" horiz-adv-x="396"/><glyph d="M47 392l103 18c1-25 21-46 52-46 29 0 47 16 47 37 0 28-22 40-46 40-10 0-20-2-29-6l-35 75 79 47h-158v92h291v-93l-85-51c63-10 98-50 98-107 0-66-60-127-162-127-93 0-150 60-155 121z" glyph-name="three.numr" horiz-adv-x="411"/><glyph d="M47 351v112l164 186h121v-213h59v-85h-59v-71h-111v71h-174zM221 436v102l-91-102h91z" glyph-name="four.numr" horiz-adv-x="438"/><glyph d="M47 394l100 18c2-27 26-45 55-45 27 0 46 15 46 40 0 32-21 46-50 46-19 0-36-8-47-20l-92 26 44 190h231v-90h-165l-12-52c17 13 45 20 71 20 80 0 130-44 130-119 0-73-55-134-156-134-86 0-148 53-155 120z" glyph-name="five.numr" horiz-adv-x="402"/><glyph d="M360 641l-19-88c-17 6-41 11-67 11-46 0-96-17-114-71 21 18 51 28 82 28 72 0 130-39 130-116 0-79-65-135-161-135-93 0-164 66-164 170 0 136 98 215 221 215 31 0 61-5 92-14zM165 401c0-24 20-42 46-42s47 18 47 42c0 26-21 44-47 44s-46-18-46-44z" glyph-name="six.numr" horiz-adv-x="419"/><glyph d="M359 649v-92c-34-29-121-112-136-277h-120c11 175 123 276 123 276h-179v93h312z" glyph-name="seven.numr" horiz-adv-x="405"/><glyph d="M212 503c27 1 40 18 40 34 0 14-12 32-40 32s-40-18-40-32c0-16 13-33 40-34zM212 358c36 0 49 21 49 39 0 17-14 36-49 36s-49-19-49-36c0-18 13-39 49-39zM212 272c-101 0-161 44-161 110 0 43 33 77 76 88-42 12-65 47-65 83 0 58 52 104 150 104 99 0 150-46 150-104 0-36-22-71-65-83 41-11 76-44 76-84 0-68-57-114-161-114z" glyph-name="eight.numr" horiz-adv-x="424"/><glyph d="M66 285l17 88c16-7 39-11 62-11 50 0 103 15 118 72-21-18-52-28-84-28-65 0-132 39-132 116 0 79 61 136 161 136 94 0 163-68 163-171 0-139-94-217-214-217-30 0-61 6-91 15zM251 526c0 24-20 42-46 42s-46-18-46-42c0-26 20-43 46-43s46 17 46 43z" glyph-name="nine.numr" horiz-adv-x="419"/><glyph d="M163 184c0-49 7-100 56-100s56 51 56 100-7 100-56 100-56-51-56-100zM47 184c0 45 8 92 36 130s73 63 136 63c64 0 109-25 137-63s36-85 36-130-8-92-36-130c-28-37-73-63-137-63-63 0-108 26-136 64-28 37-36 84-36 129z" glyph-name="zero.dnom" horiz-adv-x="439"/><glyph d="M241 0h-115v212h-94v74c70 0 101 47 108 82h101v-368z" glyph-name="one.dnom" horiz-adv-x="308"/><glyph d="M154 223h-100c-2 8-4 19-4 27 0 68 49 128 149 128 94 0 148-57 148-125 0-48-31-83-84-114l-67-39c-3-2-5-3-5-5h159v-95h-302v13c0 68 22 123 91 162l60 34c23 13 36 28 36 45 0 13-9 34-39 34s-45-22-45-48c0-9 2-15 3-17z" glyph-name="two.dnom" horiz-adv-x="396"/><glyph d="M47 112l103 18c1-25 21-46 52-46 29 0 47 16 47 37 0 28-22 40-46 40-10 0-20-2-29-6l-35 75 79 47h-158v92h291v-93l-85-51c63-10 98-50 98-107 0-66-60-127-162-127-93 0-150 60-155 121z" glyph-name="three.dnom" horiz-adv-x="411"/><glyph d="M47 71v112l164 186h121v-213h59v-85h-59v-71h-111v71h-174zM221 156v102l-91-102h91z" glyph-name="four.dnom" horiz-adv-x="438"/><glyph d="M47 113l100 18c2-27 26-45 55-45 27 0 46 15 46 40 0 32-21 46-50 46-19 0-36-8-47-20l-92 26 44 190h231v-90h-165l-12-52c17 13 45 20 71 20 80 0 130-44 130-119 0-73-55-134-156-134-86 0-148 53-155 120z" glyph-name="five.dnom" horiz-adv-x="405"/><glyph d="M360 362l-19-88c-17 6-41 11-67 11-46 0-96-17-114-71 21 18 51 28 82 28 72 0 130-39 130-116 0-79-65-135-161-135-93 0-164 66-164 170 0 136 98 215 221 215 31 0 61-5 92-14zM165 122c0-24 20-42 46-42s47 18 47 42c0 26-21 44-47 44s-46-18-46-44z" glyph-name="six.dnom" horiz-adv-x="419"/><glyph d="M359 369v-92c-34-29-121-112-136-277h-120c11 175 123 276 123 276h-179v93h312z" glyph-name="seven.dnom" horiz-adv-x="405"/><glyph d="M212 223c27 1 40 18 40 34 0 14-12 32-40 32s-40-18-40-32c0-16 13-33 40-34zM212 78c36 0 49 21 49 39 0 17-14 36-49 36s-49-19-49-36c0-18 13-39 49-39zM212-8c-101 0-161 44-161 110 0 43 33 77 76 88-42 12-65 47-65 83 0 58 52 104 150 104 99 0 150-46 150-104 0-36-22-71-65-83 41-11 76-44 76-84 0-68-57-114-161-114z" glyph-name="eight.dnom" horiz-adv-x="424"/><glyph d="M66 5l17 88c16-7 39-11 62-11 50 0 103 15 118 72-21-18-52-28-84-28-65 0-132 39-132 116 0 79 61 136 161 136 94 0 163-68 163-171 0-139-94-217-214-217-30 0-61 6-91 15zM251 246c0 24-20 42-46 42s-46-18-46-42c0-26 20-43 46-43s46 17 46 43z" glyph-name="nine.dnom" horiz-adv-x="419"/><glyph glyph-name="space.frac" horiz-adv-x="49"/><glyph d="M50 397c0 61 47 96 104 105l81 12c18 3 25 12 25 24 0 19-17 35-49 35-36 0-56-25-58-49l-96 20c5 47 49 116 155 116 114 0 156-64 156-136v-170c0-26 3-47 4-54h-100c-1 5-3 18-3 38-19-30-52-48-97-48-77 0-122 51-122 107zM200 369c29 0 60 15 60 67v15l-62-10c-22-3-38-14-38-37 0-18 11-35 40-35z" glyph-name="a.ordn" horiz-adv-x="422"/><glyph d="M188 300h-108v507h110v-189c14 21 52 40 99 40 101 0 162-77 162-182 0-107-70-184-166-184-46 0-81 21-97 46v-38zM341 475c0 57-37 83-76 83-40 0-77-26-77-83 0-56 37-83 77-83 39 0 76 26 76 83z" glyph-name="b.ordn" horiz-adv-x="506"/><glyph d="M243 558c-44 0-78-31-78-84s36-82 79-82c38 0 62 23 69 49l98-28c-15-63-73-123-167-123-104 0-189 76-189 184 0 109 82 186 185 186 96 0 154-60 169-123l-99-29c-8 26-28 50-67 50z" glyph-name="c.ordn" horiz-adv-x="461"/><glyph d="M427 807v-445c0-26 1-47 2-62h-106c-1 3-3 19-3 31-16-22-51-39-91-39-98 0-174 76-174 184 0 104 71 182 169 182 60 0 85-22 93-34v183h110zM166 475c0-56 37-83 77-83 39 0 76 26 76 83s-37 83-76 83c-40 0-77-26-77-83z" glyph-name="d.ordn" horiz-adv-x="509"/><glyph d="M159 517h135c-2 24-18 56-68 56-43 0-65-32-67-56zM301 426l93-25c-17-60-72-111-161-111-97 0-183 68-183 186 0 112 84 184 175 184 108 0 176-66 176-179 0-15-2-32-2-35h-242c3-36 37-63 77-63 36 0 57 17 67 43z" glyph-name="e.ordn" horiz-adv-x="451"/><glyph d="M290 556h-75v-256h-110v256h-55v93h55v28c0 79 48 136 135 136 17 0 41-2 51-7v-91c-7 2-16 4-31 4-19 0-45-9-45-42v-28h75v-93z" glyph-name="f.ordn" horiz-adv-x="341"/><glyph d="M55 280l97 28c8-32 35-53 74-53 51 0 84 25 84 87v10c-13-18-42-37-92-37-93 0-163 72-163 170 0 91 67 171 163 171 57 0 86-26 95-43v36h106v-304c0-101-57-188-188-188-102 0-166 62-176 123zM238 411c42 0 72 28 72 74 0 45-34 73-72 73-37 0-72-28-72-73 0-46 32-74 72-74z" glyph-name="g.ordn" horiz-adv-x="467"/><glyph d="M192 505v-205h-112v507h112v-179c21 21 57 30 87 30 92 0 133-63 133-139v-219h-111v200c0 33-18 58-54 58-32 0-53-23-55-53z" glyph-name="h.ordn" horiz-adv-x="492"/><glyph d="M200 300h-112v349h112v-349zM80 753c0 36 29 64 64 64s64-28 64-64c0-35-29-64-64-64s-64 29-64 64z" glyph-name="i.ordn" horiz-adv-x="288"/><glyph d="M137 289v360h112v-373c0-70-46-119-118-119-32 0-55 7-61 9v93c4-1 17-4 33-4 24 0 34 13 34 34zM129 753c0 36 29 64 64 64s64-28 64-64c0-35-29-64-64-64s-64 29-64 64z" glyph-name="j.ordn" horiz-adv-x="337"/><glyph d="M436 649l-135-146 138-203h-135l-80 120-32-35v-85h-112v507h112v-271l101 113h143z" glyph-name="k.ordn" horiz-adv-x="474"/><glyph d="M192 300h-112v507h112v-507z" glyph-name="l.ordn" horiz-adv-x="272"/><glyph d="M192 300h-112v349h107v-40c17 31 62 51 99 51 51 0 86-21 104-54 27 38 59 54 107 54 66 0 130-39 130-134v-226h-107v200c0 32-17 57-54 57-36 0-55-27-55-57v-200h-110v200c0 32-17 57-54 57-36 0-55-27-55-57v-200z" glyph-name="m.ordn" horiz-adv-x="707"/><glyph d="M192 500v-200h-112v349h108v-39c18 32 61 48 97 48 88 0 127-63 127-139v-219h-111v200c0 33-18 58-54 58-34 0-55-25-55-58z" glyph-name="n.ordn" horiz-adv-x="492"/><glyph d="M241 392c39 0 76 26 76 83s-37 83-76 83c-38 0-75-26-75-83 0-56 37-83 75-83zM241 660c104 0 186-77 186-185 0-109-82-185-186-185s-186 76-186 185c0 108 82 185 186 185z" glyph-name="o.ordn" horiz-adv-x="482"/><glyph d="M192 167h-112v482h108v-34c14 22 52 43 101 43 101 0 162-77 162-182 0-107-70-184-166-184-44 0-77 15-93 33v-158zM343 475c0 57-37 83-76 83-40 0-77-26-77-83 0-56 37-83 77-83 39 0 76 26 76 83z" glyph-name="p.ordn" horiz-adv-x="506"/><glyph d="M427 167h-109v159c-15-20-47-34-89-34-100 0-174 76-174 184 0 104 69 182 169 182 62 0 90-28 97-41v32h106v-482zM167 475c0-56 37-83 76-83s77 26 77 83-38 83-77 83-76-26-76-83z" glyph-name="q.ordn" horiz-adv-x="507"/><glyph d="M306 649v-109c-11 3-21 4-31 4-43 0-83-22-83-94v-150h-112v349h108v-45c15 33 53 48 83 48 16 0 24-1 35-3z" glyph-name="r.ordn" horiz-adv-x="311"/><glyph d="M55 400l94 15c1-22 17-44 52-44 27 0 39 13 39 28 0 12-8 21-33 27l-38 8c-77 17-106 60-106 109 0 64 56 117 135 117 102 0 137-64 140-105l-91-16c-3 23-17 41-47 41-19 0-36-11-36-28 0-14 11-22 26-25l45-9c73-14 109-58 109-110 0-60-46-118-141-118-110 0-145 71-148 110z" glyph-name="s.ordn" horiz-adv-x="399"/><glyph d="M206 750v-101h67v-97h-67v-125c0-28 15-35 37-35 11 0 22 1 30 2v-91c-5-2-24-9-58-9-74 0-119 43-119 113v145h-61v97h17c37 0 54 25 54 57v44h100z" glyph-name="t.ordn" horiz-adv-x="328"/><glyph d="M284 300c-2 7-3 22-3 35-18-30-58-43-92-43-86 0-134 63-134 136v221h111v-196c0-33 18-58 54-58 34 0 55 23 55 56v198h112v-287c0-28 2-52 3-62h-106z" glyph-name="u.ordn" horiz-adv-x="470"/><glyph d="M407 649l-132-349h-112l-143 349h120l79-211 72 211h116z" glyph-name="v.ordn" horiz-adv-x="427"/><glyph d="M263 649h118l65-200 56 200h109l-108-349h-110l-74 221-73-221h-112l-109 349h115l56-199z" glyph-name="w.ordn" horiz-adv-x="636"/><glyph d="M28 300l119 174-122 175h131l59-91 59 91h124l-120-171 124-178h-129l-63 94-60-94h-122z" glyph-name="x.ordn" horiz-adv-x="427"/><glyph d="M206 167h-117l79 181-147 301h124l83-181 74 181h118z" glyph-name="y.ordn" horiz-adv-x="438"/><glyph d="M350 300h-295v98l151 157h-146v94h287v-91l-158-163h161v-95z" glyph-name="z.ordn" horiz-adv-x="405"/><glyph glyph-name="space.tf"/><glyph d="M527 649l-33-152h83v-106h-105l-28-128h82v-107h-105l-33-156h-115l33 156h-101l-33-156h-115l33 156h-80v107h103l28 128h-81v106h103l33 152h115l-33-152h101l33 152h115zM256 391l-28-128h101l28 128h-101z" glyph-name="numbersign.tf"/><glyph d="M365-116h-115v107c-140 23-204 127-211 208l137 31c5-58 45-109 131-109 50 0 86 24 86 62 0 29-22 48-62 57l-89 21c-111 27-180 95-180 190 0 111 84 188 188 206v108h115v-111c110-24 159-103 173-170l-138-37c-6 31-23 82-102 82-56 0-84-32-84-64 0-27 20-48 59-57l87-19c134-30 186-108 186-198 0-91-65-177-181-198v-109z" glyph-name="dollar.tf"/><glyph d="M525 536v-101l-482-328v100zM117 517c0-33 21-54 51-54 29 0 50 22 50 54 0 34-21 55-50 55-30 0-51-21-51-55zM23 517c0 80 66 142 145 142 78 0 143-62 143-142s-65-140-144-140c-80 0-144 62-144 140zM368 133c0-33 21-54 51-54 29 0 50 22 50 54 0 34-21 55-50 55-30 0-51-21-51-55zM274 133c0 80 66 142 145 142 78 0 143-62 143-142s-65-140-144-140c-80 0-144 62-144 140z" glyph-name="percent.tf"/><glyph d="M39 360h190v195h128v-195h189v-124h-189v-195h-128v195h-190v124z" glyph-name="plus.tf"/><glyph d="M179 97c0 59 48 103 110 103 78 0 120-65 120-138 0-171-122-244-210-254v78c47 9 108 53 110 120-5-5-17-8-31-8-60 0-99 44-99 99z" glyph-name="comma.tf"/><glyph d="M180 104c0 62 51 113 113 113s114-51 114-113c0-61-52-114-114-114s-113 53-113 114z" glyph-name="period.tf"/><glyph d="M188 324c0-98 4-202 106-202 101 0 105 104 105 202 0 99-4 201-105 201-102 0-106-102-106-201zM29 324c0 80 8 167 52 233 43 65 118 107 213 107 94 0 168-40 212-106 43-66 52-154 52-234s-9-169-52-234c-44-66-118-106-212-106-95 0-169 40-213 106-43 65-52 154-52 234z" glyph-name="zero.tf"/><glyph d="M74 131h163v291h-146v105c97 1 157 56 168 122h136v-518h148v-131h-469v131z" glyph-name="one.tf"/><glyph d="M205 389l-148 1c-3 8-6 34-6 54 0 116 87 220 243 220 149 0 241-97 241-213 0-84-45-153-128-202l-123-73c-19-12-35-25-44-44h300v-132h-493c0 119 34 217 153 286l106 62c51 30 71 57 71 100 0 41-29 77-87 77-61 0-91-42-91-96 0-13 2-27 6-40z" glyph-name="two.tf"/><glyph d="M226 282l-68 104 160 131h-271v132h468v-122l-160-124c100-7 182-87 182-198 0-115-95-219-255-219-151 0-247 97-257 208l152 28c2-57 43-102 104-102s97 41 97 89c0 62-48 88-94 88-21 0-43-7-58-15z" glyph-name="three.tf"/><glyph d="M29 135v160l266 354h187v-382h91v-132h-91v-135h-157v135h-296zM325 267v214l-158-214h158z" glyph-name="four.tf"/><glyph d="M49 185l146 30c3-50 41-97 105-97 53 0 97 35 97 94 0 65-49 96-101 96-44 0-75-22-91-42-3 1-138 44-141 46l73 337h377v-132h-268l-26-122c25 22 73 35 110 35 133 0 224-77 224-217 0-121-92-229-254-229-144 0-243 99-251 201z" glyph-name="five.tf"/><glyph d="M544 637l-41-128c-31 12-69 20-107 20-97 0-190-50-207-168 22 37 73 71 150 71 127 0 214-89 214-215 0-133-104-232-252-232-150 0-265 111-265 296 0 231 148 383 374 383 61 0 109-12 134-27zM202 211c0-55 45-94 97-94 53 0 97 37 97 94 0 58-44 95-97 95-52 0-97-37-97-95z" glyph-name="six.tf"/><glyph d="M552 649v-126c-45-39-236-203-263-523h-164c26 336 242 517 242 517h-343v132h528z" glyph-name="seven.tf"/><glyph d="M293 388c57 1 83 37 83 76 0 34-29 70-83 70s-83-36-83-70c0-39 27-75 83-76zM293 113c64 0 97 42 97 81 0 40-30 83-97 83-66 0-96-43-96-83 0-39 33-81 96-81zM293-15c-147 0-250 77-250 191 0 67 42 131 110 158-67 24-98 91-98 142 0 109 98 188 238 188 141 0 239-79 239-188 0-51-32-118-99-142 68-27 111-91 111-158 0-114-103-191-251-191z" glyph-name="eight.tf"/><glyph d="M43 19l35 127c21-11 68-26 113-26 99 0 187 45 197 160-27-40-82-63-138-63-122 0-219 88-219 218 0 132 101 229 249 229 144 0 259-107 259-312 0-226-122-367-345-367-58 0-116 14-151 34zM367 440c0 54-42 91-93 91s-93-37-93-91 42-91 93-91 93 38 93 91z" glyph-name="nine.tf"/><glyph d="M185 86c0 59 49 108 108 108s109-49 109-108c0-58-50-108-109-108s-108 50-108 108zM185 405c0 59 49 108 108 108s109-49 109-108c0-58-50-108-109-108s-108 50-108 108z" glyph-name="colon.tf"/><glyph d="M185 94c0 55 44 96 103 96 72 0 112-61 112-129 0-160-114-228-197-238v74c45 8 102 49 104 111-5-4-17-7-29-7-57 0-93 41-93 93zM185 404c0 59 49 108 108 108s109-49 109-108c0-58-50-108-109-108s-108 50-108 108z" glyph-name="semicolon.tf"/><glyph d="M71 244v108l439 224v-138l-288-137 288-139v-140z" glyph-name="less.tf"/><glyph d="M540 339h-495v122h495v-122zM540 138h-495v122h495v-122z" glyph-name="equal.tf"/><glyph d="M516 353v-108l-439-223v137l288 138-288 139v140z" glyph-name="greater.tf"/><glyph d="M366 0h-115v84c-110 26-190 115-190 235 0 122 80 213 190 237v93h115v-92c96-19 154-85 172-154l-130-38c-10 35-38 69-94 69-60 0-108-42-108-115s49-113 110-113c55 0 87 34 97 68l128-38c-18-70-79-137-175-155v-81z" glyph-name="cent.tf"/><glyph d="M64 383h57c-8 24-14 51-14 84 0 98 76 197 217 197 156 0 208-106 208-188v-5l-131-19v5c0 49-23 83-75 83-41 0-74-25-74-78 0-30 7-53 17-79h176v-110h-141c1-6 1-12 1-18 0-47-17-95-62-125h112c42 0 74 29 74 74l132-8c0-113-75-196-186-196h-315v118c63 22 104 63 104 122 0 11-1 22-3 33h-97v110z" glyph-name="sterling.tf"/><glyph d="M76 248c0 37 9 71 25 101l-71 72 91 90 74-74c30 15 63 23 99 23 35 0 71-8 98-23l74 74 91-90-71-72c16-27 26-64 26-101 0-38-10-72-26-102l71-70-91-91-73 74c-30-15-64-23-99-23-36 0-69 8-99 23l-74-74-91 91 72 71c-16 27-26 64-26 101zM201 248c0-53 38-91 93-91s93 38 93 91-38 91-93 91-93-38-93-91z" glyph-name="currency.tf"/><glyph d="M533 125h-167v-125h-147v125h-157v106h157v63h-157v106h85l-135 249h170l115-236 115 236h163l-138-249h96v-106h-167v-63h167v-106z" glyph-name="yen.tf"/><glyph d="M511 258c0-62-34-102-73-126 48-37 72-87 72-140 0-121-106-197-215-197-131 0-213 75-219 188l143 22c0-58 34-80 76-80 38 0 63 24 63 57 0 20-13 41-36 51l-101 51c-75 38-142 85-142 177 0 63 33 103 73 126-48 38-71 87-71 141 0 120 106 196 216 196 129 0 212-76 217-188l-142-22c0 59-33 80-75 80-39 0-64-25-64-57 0-20 13-40 36-50l101-52c74-38 141-83 141-177zM342 283l-102 54c-14-13-24-28-24-45 0-25 7-41 33-55l102-54c13 12 25 27 25 45 0 23-8 41-34 55z" glyph-name="section.tf" horiz-adv-x="590"/><glyph d="M535 129h-130v195h-373v125h503v-320z" glyph-name="logicalnot.tf"/><glyph d="M52 438h177v117h128v-117h178v-124h-178v-116h-128v116h-177v124zM52 164h483v-124h-483v124z" glyph-name="plusminus.tf"/><glyph d="M457 43l-164 165-164-165-90 92 163 164-163 165 90 90 164-165 164 165 90-90-164-165 164-164z" glyph-name="multiply.tf"/><glyph d="M205 486c0 46 38 84 85 84s85-38 85-84-38-85-85-85-85 39-85 85zM41 237v124h505v-124h-505zM205 114c0 45 38 83 85 83s85-38 85-83c0-46-38-85-85-85s-85 39-85 85z" glyph-name="divide.tf"/><glyph d="M151 30l69 340h-93v126h118l17 82c23 114 115 163 198 163 50 0 65-5 97-16v-130c-21 5-41 8-67 8-23 0-66-10-78-68l-8-39h110v-126h-136l-77-383c-23-115-116-163-198-163-50 0-68 5-100 15v131c20-5 44-8 70-8 23 0 66 11 78 68z" glyph-name="florin.tf"/><glyph glyph-name="uni2007.tf"/><glyph glyph-name="uni2008.tf"/><glyph d="M535 606v-101l-493-337v100zM105 536c0-29 19-49 45-49s45 20 45 49c0 28-19 48-45 48s-45-20-45-48zM23 536c0 70 58 125 127 125s127-55 127-125c0-71-58-124-127-124-71 0-127 54-127 124zM131 116c0 70 58 125 126 125 35 0 66-14 89-36 23 22 55 36 89 36 69 0 127-55 127-125 0-71-58-124-127-124-35 0-66 13-89 35-23-22-54-35-89-35-70 0-126 54-126 124zM435 164c-26 0-45-19-45-48s19-49 45-49 45 20 45 49c0 28-19 48-45 48zM257 164c-25 0-45-20-45-48 0-29 20-49 45-49 27 0 45 20 45 49 0 28-18 48-45 48z" glyph-name="perthousand.tf"/><glyph d="M108 295c-1 13-1 23-1 28s0 13 1 26h-62v101h83c49 131 174 214 317 214 58 0 92-8 127-22l-38-129c-24 10-53 15-89 15-57 0-115-23-150-78h222l-28-101h-225c-1-13-1-21-1-26 0-8 0-14 1-28h210l-27-102h-147c34-47 88-69 146-69 36 0 66 5 90 15l36-128c-27-14-77-27-126-27-147 0-269 78-318 209h-83v102h62z" glyph-name="Euro.tf"/><glyph d="M544 237h-501v124h501v-124z" glyph-name="minus.tf"/><glyph d="M486 479l80-65c-37-70-99-108-170-109-36 0-74 10-112 31l-48 26c-17 9-34 13-50 13-32 0-62-17-84-49l-82 65c37 70 100 109 171 109 36 0 73-9 110-29l48-26c17-9 34-14 51-14 32 0 64 14 86 48zM486 275l80-66c-37-70-100-108-171-108-37 0-72 9-111 30l-48 26c-17 9-34 13-51 13-31 0-61-17-83-48l-82 65c36 67 98 108 168 108 36 0 75-9 113-29l48-26c17-9 34-14 51-14 33 0 63 15 86 49z" glyph-name="approxequal.tf"/><glyph d="M540 138h-275l-59-96h-132l59 96h-88v122h163l48 79h-211v122h286l54 88h132l-54-88h77v-122h-152l-48-79h200v-122z" glyph-name="notequal.tf"/><glyph d="M75 164h439v-124h-439v124zM75 322v120l439 130v-123l-266-65 266-66v-126z" glyph-name="lessequal.tf"/><glyph d="M74 164h439v-124h-439v124zM513 442v-120l-439-130v123l266 65-266 67v125z" glyph-name="greaterequal.tf"/><glyph d="M37 355c0 222 90 367 197 478l90-75c-99-118-161-229-161-403s62-286 161-403l-90-75c-107 112-197 256-197 478z" glyph-name="parenleft.case" horiz-adv-x="324"/><glyph d="M287 355c0-222-89-366-197-478l-90 75c99 117 161 229 161 403s-62 285-161 403l90 75c108-111 197-256 197-478z" glyph-name="parenright.case" horiz-adv-x="324"/><glyph d="M345 283h-300v137h300v-137z" glyph-name="hyphen.case" horiz-adv-x="390"/><glyph d="M326-118h-249v942h249v-113h-123v-713h123v-116z" glyph-name="bracketleft.case" horiz-adv-x="331"/><glyph d="M255-118h-250v116h124v713h-124v113h250v-942z" glyph-name="bracketright.case" horiz-adv-x="331"/><glyph d="M45 293v124c56 0 96 24 96 88v123c0 133 79 196 199 196h40v-113h-37c-37 0-76-20-76-82v-147c0-61-35-108-99-127 63-18 99-66 99-128v-147c0-62 39-82 76-82h37v-113h-40c-120 0-199 63-199 197v123c0 64-40 88-96 88z" glyph-name="braceleft.case" horiz-adv-x="385"/><glyph d="M340 417v-124c-56 0-95-24-95-88v-123c0-134-79-197-200-197h-40v113h37c37 0 77 20 77 82v147c0 62 35 110 98 128-64 19-98 66-98 127v147c0 62-40 82-77 82h-37v113h40c121 0 200-63 200-196v-123c0-64 39-88 95-88z" glyph-name="braceright.case" horiz-adv-x="385"/><glyph d="M215 487l35-487h-182l35 487h112zM73 632c0 48 38 86 86 86s87-38 87-86c0-47-39-86-87-86s-86 39-86 86z" glyph-name="exclamdown.case" horiz-adv-x="318"/><glyph d="M306 152h-142l-138 195 140 200h141l-139-201zM534 152h-143l-138 195 140 200h142l-139-201z" glyph-name="guillemotleft.case" horiz-adv-x="550"/><glyph d="M345 285h-300v137h300v-137z" glyph-name="uni00AD.case" horiz-adv-x="390"/><glyph d="M386 152h-142l138 194-139 201h142l140-200zM159 152h-143l139 194-139 201h141l140-200z" glyph-name="guillemotright.case" horiz-adv-x="550"/><glyph d="M214 488h119c2-14 4-28 4-44 0-47-17-98-74-141l-50-38c-27-21-37-45-37-70 0-39 29-75 89-75 61 0 92 44 92 94 0 13-2 26-5 38l143-12c3-14 4-29 4-43 0-122-97-212-234-212-163 0-243 98-243 198 0 80 37 136 95 180l38 28c36 27 59 54 59 97zM186 633c0 48 39 86 86 86 48 0 86-38 86-86 0-47-38-86-86-86-47 0-86 39-86 86z" glyph-name="questiondown.case" horiz-adv-x="520"/><glyph d="M577 286h-517v132h517v-132z" glyph-name="endash.case" horiz-adv-x="636"/><glyph d="M1003 286h-933v132h933v-132z" glyph-name="emdash.case" horiz-adv-x="1072"/><glyph d="M306 152h-142l-138 195 140 200h141l-139-201z" glyph-name="guilsinglleft.case" horiz-adv-x="323"/><glyph d="M159 152h-143l139 194-139 201h142l139-200z" glyph-name="guilsinglright.case" horiz-adv-x="323"/><glyph d="M407-15c-209 0-371 151-371 369 0 217 166 370 367 370 206 0 306-130 332-232l-156-42c-10 42-53 126-176 126-98 0-205-64-205-222 0-145 94-222 209-222 75 0 153 35 170 110h-233v137h399c3-21 3-43 3-65-2-181-133-329-339-329z" glyph-name="G.ss01" horiz-adv-x="777"/><glyph d="M184 249c0-82 51-132 114-132 61 0 113 48 113 132 0 86-51 131-113 131-65 0-114-45-114-131zM412 59v5c-22-48-72-78-134-78-146 0-247 111-247 264 0 148 95 262 242 262 89 0 128-49 138-72v57h147v-408c0-46 3-78 4-89h-146c-2 11-4 38-4 59z" glyph-name="a.ss02" horiz-adv-x="627"/><glyph d="M184 249c0-82 51-132 114-132 61 0 113 48 113 132 0 86-51 131-113 131-65 0-114-45-114-131zM412 59v5c-22-48-72-78-134-78-146 0-247 111-247 264 0 148 95 262 242 262 89 0 128-49 138-72v57h147v-408c0-46 3-78 4-89h-146c-2 11-4 38-4 59zM262 556l-142 151h173l93-151h-124z" glyph-name="agrave.ss02" horiz-adv-x="627"/><glyph d="M469 707l-141-151h-121l92 151h170zM184 249c0-82 51-132 114-132 61 0 113 48 113 132 0 86-51 131-113 131-65 0-114-45-114-131zM412 59v5c-22-48-72-78-134-78-146 0-247 111-247 264 0 148 95 262 242 262 89 0 128-49 138-72v57h147v-408c0-46 3-78 4-89h-146c-2 11-4 38-4 59z" glyph-name="aacute.ss02" horiz-adv-x="627"/><glyph d="M235 556h-113l113 151h143l114-151h-116l-71 71zM184 249c0-82 51-132 114-132 61 0 113 48 113 132 0 86-51 131-113 131-65 0-114-45-114-131zM412 59v5c-22-48-72-78-134-78-146 0-247 111-247 264 0 148 95 262 242 262 89 0 128-49 138-72v57h147v-408c0-46 3-78 4-89h-146c-2 11-4 38-4 59z" glyph-name="acircumflex.ss02" horiz-adv-x="627"/><glyph d="M184 249c0-82 51-132 114-132 61 0 113 48 113 132 0 86-51 131-113 131-65 0-114-45-114-131zM412 59v5c-22-48-72-78-134-78-146 0-247 111-247 264 0 148 95 262 242 262 89 0 128-49 138-72v57h147v-408c0-46 3-78 4-89h-146c-2 11-4 38-4 59zM494 706v-20c0-88-47-130-112-130-30 0-53 11-76 23-20 10-37 21-56 21-20 0-33-13-34-43h-86v23c0 86 50 127 113 127 31 0 54-11 76-23 20-12 37-22 55-22 22 0 34 15 35 44h85z" glyph-name="atilde.ss02" horiz-adv-x="627"/><glyph d="M117 632c0 43 33 77 76 77s77-34 77-77-34-76-77-76-76 33-76 76zM338 632c0 43 33 77 76 77s77-34 77-77-34-76-77-76-76 33-76 76zM184 249c0-82 51-132 114-132 61 0 113 48 113 132 0 86-51 131-113 131-65 0-114-45-114-131zM412 59v5c-22-48-72-78-134-78-146 0-247 111-247 264 0 148 95 262 242 262 89 0 128-49 138-72v57h147v-408c0-46 3-78 4-89h-146c-2 11-4 38-4 59z" glyph-name="adieresis.ss02" horiz-adv-x="627"/><glyph d="M413 652c0-66-54-111-116-111s-116 45-116 111 54 113 116 113 116-47 116-113zM334 652c0 23-16 38-37 38s-37-15-37-38 16-38 37-38 37 15 37 38zM184 249c0-82 51-132 114-132 61 0 113 48 113 132 0 86-51 131-113 131-65 0-114-45-114-131zM412 59v5c-22-48-72-78-134-78-146 0-247 111-247 264 0 148 95 262 242 262 89 0 128-49 138-72v57h147v-408c0-46 3-78 4-89h-146c-2 11-4 38-4 59z" glyph-name="aring.ss02" horiz-adv-x="627"/><glyph d="M219 497v-497h-152v497h152zM220 407c0 56 43 99 98 99 56 0 99-43 99-99 0-55-43-98-99-98-55 0-98 43-98 98z" glyph-name="r.ss03" horiz-adv-x="431"/><glyph d="M383 228v127h297v-127h-66c-6-131-105-243-284-243-175 0-278 94-278 218 0 97 74 165 144 182-61 19-109 81-109 149 0 109 95 190 230 190 167 0 234-97 240-197l-151-16c-1 49-28 88-88 88-38 0-79-26-79-71 0-57 52-78 84-78h11v-127h-4c-54 0-119-29-119-106 0-49 37-101 123-101 70 0 118 39 122 112h-73z" glyph-name="ampersand.ss04" horiz-adv-x="722"/><glyph d="M36 131h147v291h-146v105c97 1 156 56 168 122h135v-518h144v-131h-448v131z" glyph-name="one.ss05" horiz-adv-x="498"/><glyph d="M22 526c0 106 86 192 192 192s193-86 193-192-87-192-193-192-192 86-192 192zM77 526c0-78 60-139 137-139s137 61 137 139-60 139-137 139-137-61-137-139zM236 431l-26 65h-11v-65h-58v196h86c39 0 69-28 69-66 0-24-13-44-34-55l36-75h-62zM199 542h14c15 0 25 8 25 19 0 15-10 19-25 19h-14v-38z" glyph-name="registered.ss06" horiz-adv-x="429"/><glyph d="M196 709l-75-207h-99l29 207h145z" glyph-name="caron.alt" horiz-adv-x="217"/><glyph d="M22 698c0 38 32 70 79 70 54 0 90-43 90-93 0-113-88-135-147-135h-8v52c56 0 85 17 88 45 0-1-12-7-31-7-47 0-71 30-71 68z" glyph-name="commaaccent" horiz-adv-x="213"/><glyph d="M181 614c0-38-32-69-79-69-53 0-90 41-90 92 0 100 88 134 147 134h8v-51c-56 0-85-24-88-45 4 2 14 7 31 7 47 0 71-31 71-68z" glyph-name="commaturn" horiz-adv-x="193"/><glyph d="M11 633c0 41 32 74 75 74 41 0 74-33 74-74 0-43-33-75-74-75-43 0-75 32-75 75zM192 633c0 41 32 74 73 74 42 0 75-33 75-74 0-43-33-75-75-75-41 0-73 32-73 75z" glyph-name="dieresis.narrow" horiz-adv-x="352"/><glyph d="M12 824c0 44 32 78 75 78 44 0 77-34 77-78 0-43-33-75-77-75-43 0-75 32-75 75zM210 824c0 44 33 78 76 78 44 0 77-34 77-78 0-43-33-75-77-75-43 0-76 32-76 75z" glyph-name="dieresis.uc.narrow" horiz-adv-x="375"/><glyph d="M155 759l-150 132h181l104-132h-135z" glyph-name="grave.uc" horiz-adv-x="301"/><glyph d="M16 824c0 45 34 80 78 80 45 0 80-35 80-80 0-44-35-77-80-77-44 0-78 33-78 77zM289 824c0 45 33 80 78 80 44 0 79-35 79-80 0-44-35-77-79-77-45 0-78 33-78 77z" glyph-name="dieresis.uc" horiz-adv-x="463"/><glyph d="M323 771h-311v109h311v-109z" glyph-name="macron.uc" horiz-adv-x="334"/><glyph d="M296 891l-150-132h-135l104 132h181z" glyph-name="acute.uc" horiz-adv-x="301"/><glyph d="M150 759h-135l122 132h145l124-132h-141l-58 65z" glyph-name="circumflex.uc" horiz-adv-x="416"/><glyph d="M15 891h140l58-65 58 65h135l-123-132h-145z" glyph-name="caron.uc" horiz-adv-x="416"/><glyph d="M183 753c-112 0-164 71-167 138h113c1-29 20-50 54-50 35 0 54 21 55 50h112c-2-67-54-138-167-138z" glyph-name="breve.uc" horiz-adv-x="367"/><glyph d="M12 841c0 49 39 87 89 87 49 0 89-38 89-87 0-50-40-87-89-87-50 0-89 37-89 87z" glyph-name="dotaccent.uc" horiz-adv-x="201"/><glyph d="M293 880c0-79-66-136-141-136-74 0-140 57-140 136 0 80 66 137 140 137 75 0 141-57 141-137zM200 880c0 31-22 48-47 48-27 0-49-17-49-48 0-30 22-47 49-47 25 0 47 17 47 47z" glyph-name="ring.uc" horiz-adv-x="305"/><glyph d="M360 895v-34c0-73-49-110-104-110-31 0-57 12-79 25l-5 3c-18 11-33 20-47 20-13 0-26-9-26-29v-13h-87v33c0 73 49 108 103 108 32 0 57-11 79-24 20-11 37-22 52-22s27 9 27 29v14h87z" glyph-name="tilde.uc" horiz-adv-x="371"/><glyph d="M199 891l-93-132h-94l48 132h139zM380 891l-110-132h-96l65 132h141z" glyph-name="hungarumlaut.uc" horiz-adv-x="391"/><glyph d="M205 709l-79-217h-104l31 217h152z" glyph-name="caron.alt.uc" horiz-adv-x="217"/><glyph d="M12-109c0 38 32 69 79 69 53 0 90-42 90-92 0-106-83-136-143-136-2 0-6 0-12 1v52c56 0 85 18 88 46-4-2-14-7-31-7-47 0-71 30-71 67z" glyph-name="undercommaaccent" horiz-adv-x="193"/><hkern g2="ampersand.ss04" k="40" u1=""/><hkern k="40" u1="" u2="&#x26;"/><hkern g2="one.ss05" k="2" u1="&#x21;"/><hkern k="18" u1="&#x21;" u2="&#xdf;"/><hkern k="-9" u1="&#x21;" u2="&#x37;"/><hkern k="-4" u1="&#x22;" u2="&#xef;"/><hkern k="-4" u1="&#x22;" u2="&#xee;"/><hkern k="-4" u1="&#x22;" u2="&#xec;"/><hkern g2="one.ss05" k="5" u1="&#x23;"/><hkern k="2" u1="&#x23;" u2="&#x39;"/><hkern k="2" u1="&#x23;" u2="&#x38;"/><hkern k="1" u1="&#x23;" u2="&#x37;"/><hkern k="3" u1="&#x23;" u2="&#x35;"/><hkern k="22" u1="&#x23;" u2="&#x34;"/><hkern k="12" u1="&#x23;" u2="&#x33;"/><hkern k="1" u1="&#x23;" u2="&#x32;"/><hkern k="10" u1="&#x23;" u2="&#x31;"/><hkern g2="one.ss05" k="2" u1="&#x24;"/><hkern k="1" u1="&#x24;" u2="&#x39;"/><hkern k="1" u1="&#x24;" u2="&#x38;"/><hkern k="1" u1="&#x24;" u2="&#x37;"/><hkern k="1" u1="&#x24;" u2="&#x35;"/><hkern k="1" u1="&#x24;" u2="&#x34;"/><hkern k="1" u1="&#x24;" u2="&#x33;"/><hkern k="9" u1="&#x24;" u2="&#x31;"/><hkern k="1" u1="&#x25;" u2="&#x31;"/><hkern k="27" u1="&#x26;" u2="v"/><hkern k="48" u1="&#x26;" u2="V"/><hkern k="5" u1="&#x26;" u2="&#x38;"/><hkern k="1" u1="&#x26;" u2="&#x35;"/><hkern k="10" u1="&#x26;" u2="&#x34;"/><hkern k="1" u1="&#x26;" u2="&#x33;"/><hkern k="1" u1="&#x26;" u2="&#x31;"/><hkern k="40" u1="&#x26;" u2=""/><hkern k="-4" u1="&#x27;" u2="&#xef;"/><hkern k="-4" u1="&#x27;" u2="&#xee;"/><hkern k="-4" u1="&#x27;" u2="&#xec;"/><hkern k="40" u1="&#x28;" u2="&#x2212;"/><hkern k="-59" u1="&#x28;" u2="&#x192;"/><hkern k="-27" u1="&#x28;" u2="&#xef;"/><hkern k="-18" u1="&#x28;" u2="&#xee;"/><hkern k="-47" u1="&#x28;" u2="&#xec;"/><hkern k="23" u1="&#x28;" u2="&#xdf;"/><hkern k="9" u1="&#x28;" u2="x"/><hkern k="25" u1="&#x28;" u2="v"/><hkern k="11" u1="&#x28;" u2="g"/><hkern k="-2" u1="&#x28;" u2="X"/><hkern k="-11" u1="&#x28;" u2="V"/><hkern k="18" u1="&#x28;" u2="&#x38;"/><hkern k="-27" u1="&#x28;" u2="&#x37;"/><hkern k="38" u1="&#x28;" u2="&#x36;"/><hkern k="12" u1="&#x28;" u2="&#x35;"/><hkern k="47" u1="&#x28;" u2="&#x34;"/><hkern k="-9" u1="&#x28;" u2="&#x33;"/><hkern k="-1" u1="&#x28;" u2="&#x32;"/><hkern k="19" u1="&#x28;" u2="&#x31;"/><hkern k="59" u1="&#x28;" u2="&#x2b;"/><hkern k="-2" u1="&#x29;" u2="&#x141;"/><hkern g2="one.ss05" k="4" u1="&#x2a;"/><hkern k="45" u1="&#x2a;" u2="&#x192;"/><hkern k="9" u1="&#x2a;" u2="&#x141;"/><hkern k="-36" u1="&#x2a;" u2="&#xef;"/><hkern k="-38" u1="&#x2a;" u2="&#xee;"/><hkern k="-18" u1="&#x2a;" u2="&#xec;"/><hkern k="-9" u1="&#x2a;" u2="x"/><hkern k="-28" u1="&#x2a;" u2="v"/><hkern k="9" u1="&#x2a;" u2="X"/><hkern k="-20" u1="&#x2a;" u2="V"/><hkern k="-31" u1="&#x2a;" u2="&#x37;"/><hkern k="52" u1="&#x2a;" u2="&#x34;"/><hkern k="-18" u1="&#x2a;" u2="&#x33;"/><hkern k="-9" u1="&#x2a;" u2="&#x32;"/><hkern g2="one.ss05" k="16" u1="&#x2b;"/><hkern k="33" u1="&#x2b;" u2="X"/><hkern k="49" u1="&#x2b;" u2="V"/><hkern k="1" u1="&#x2b;" u2="&#x39;"/><hkern k="10" u1="&#x2b;" u2="&#x37;"/><hkern k="2" u1="&#x2b;" u2="&#x33;"/><hkern k="1" u1="&#x2b;" u2="&#x32;"/><hkern k="39" u1="&#x2b;" u2="&#x31;"/><hkern k="59" u1="&#x2b;" u2="&#x29;"/><hkern k="-18" u1="&#x2c;" u2="j"/><hkern k="19" u1="&#x2c;" u2="g"/><hkern k="19" u1="&#x2e;" u2="g"/><hkern g2="one.ss05" k="31" u1="&#x2f;"/><hkern g2="a.ss02" k="68" u1="&#x2f;"/><hkern k="90" u1="&#x2f;" u2="&#x192;"/><hkern k="23" u1="&#x2f;" u2="&#x17e;"/><hkern k="24" u1="&#x2f;" u2="&#x161;"/><hkern k="18" u1="&#x2f;" u2="&#x142;"/><hkern k="23" u1="&#x2f;" u2="&#x141;"/><hkern k="102" u1="&#x2f;" u2="&#xf6;"/><hkern k="89" u1="&#x2f;" u2="&#xf2;"/><hkern k="117" u1="&#x2f;" u2="&#xf0;"/><hkern k="-48" u1="&#x2f;" u2="&#xef;"/><hkern k="-30" u1="&#x2f;" u2="&#xee;"/><hkern k="1" u1="&#x2f;" u2="&#xed;"/><hkern k="-70" u1="&#x2f;" u2="&#xec;"/><hkern k="83" u1="&#x2f;" u2="&#xeb;"/><hkern k="83" u1="&#x2f;" u2="&#xe8;"/><hkern k="83" u1="&#x2f;" u2="&#xe5;"/><hkern k="55" u1="&#x2f;" u2="&#xe4;"/><hkern k="64" u1="&#x2f;" u2="&#xe3;"/><hkern k="83" u1="&#x2f;" u2="&#xe2;"/><hkern k="64" u1="&#x2f;" u2="&#xe0;"/><hkern k="54" u1="&#x2f;" u2="&#xdf;"/><hkern k="20" u1="&#x2f;" u2="x"/><hkern k="19" u1="&#x2f;" u2="v"/><hkern k="-10" u1="&#x2f;" u2="X"/><hkern k="-20" u1="&#x2f;" u2="V"/><hkern k="20" u1="&#x2f;" u2="&#x39;"/><hkern k="21" u1="&#x2f;" u2="&#x38;"/><hkern k="-21" u1="&#x2f;" u2="&#x37;"/><hkern k="11" u1="&#x2f;" u2="&#x35;"/><hkern k="89" u1="&#x2f;" u2="&#x34;"/><hkern k="43" u1="&#x2f;" u2="&#x32;"/><hkern k="27" u1="&#x2f;" u2="&#x31;"/><hkern k="14" u1="&#x2f;" u2="&#x2f;"/><hkern g2="registered.ss06" k="1" u1="&#x31;"/><hkern g2="one.ss05" k="2" u1="&#x31;"/><hkern g2="parenright.case" k="-19" u1="&#x31;"/><hkern k="-19" u1="&#x31;" u2="&#x2265;"/><hkern k="-18" u1="&#x31;" u2="&#x2264;"/><hkern k="-20" u1="&#x31;" u2="&#x2260;"/><hkern k="-20" u1="&#x31;" u2="&#x2248;"/><hkern k="-2" u1="&#x31;" u2="&#x221e;"/><hkern k="-18" u1="&#x31;" u2="&#x2215;"/><hkern k="-18" u1="&#x31;" u2="&#x2212;"/><hkern k="27" u1="&#x31;" u2="&#x2122;"/><hkern k="-8" u1="&#x31;" u2="&#xf7;"/><hkern k="-18" u1="&#x31;" u2="&#xd7;"/><hkern k="-9" u1="&#x31;" u2="&#xb7;"/><hkern k="-17" u1="&#x31;" u2="&#xb0;"/><hkern k="-9" u1="&#x31;" u2="&#xa1;"/><hkern k="-18" u1="&#x31;" u2="&#x7e;"/><hkern k="-1" u1="&#x31;" u2="&#x7c;"/><hkern k="-18" u1="&#x31;" u2="_"/><hkern k="9" u1="&#x31;" u2="&#x40;"/><hkern k="-30" u1="&#x31;" u2="&#x3e;"/><hkern k="-18" u1="&#x31;" u2="&#x3d;"/><hkern k="1" u1="&#x31;" u2="&#x3c;"/><hkern k="-5" u1="&#x31;" u2="&#x39;"/><hkern k="-14" u1="&#x31;" u2="&#x38;"/><hkern k="-19" u1="&#x31;" u2="&#x37;"/><hkern k="-14" u1="&#x31;" u2="&#x35;"/><hkern k="-14" u1="&#x31;" u2="&#x33;"/><hkern k="-19" u1="&#x31;" u2="&#x32;"/><hkern k="-18" u1="&#x31;" u2="&#x29;"/><hkern k="9" u1="&#x31;" u2="&#x26;"/><hkern k="1" u1="&#x31;" u2="&#x25;"/><hkern k="11" u1="&#x31;" u2="&#x23;"/><hkern g2="registered.ss06" k="-1" u1="&#x32;"/><hkern g2="one.ss05" k="-14" u1="&#x32;"/><hkern g2="parenright.case" k="5" u1="&#x32;"/><hkern g2="numbersign.tf" k="-9" u1="&#x32;"/><hkern k="-19" u1="&#x32;" u2="&#x2265;"/><hkern k="-18" u1="&#x32;" u2="&#x2264;"/><hkern k="-10" u1="&#x32;" u2="&#x2260;"/><hkern k="-20" u1="&#x32;" u2="&#x2248;"/><hkern k="-20" u1="&#x32;" u2="&#x2215;"/><hkern k="-9" u1="&#x32;" u2="&#x2212;"/><hkern k="36" u1="&#x32;" u2="&#x2122;"/><hkern k="-19" u1="&#x32;" u2="&#xd7;"/><hkern k="-10" u1="&#x32;" u2="&#xb1;"/><hkern k="-10" u1="&#x32;" u2="&#xb0;"/><hkern k="-9" u1="&#x32;" u2="&#xa4;"/><hkern k="-9" u1="&#x32;" u2="&#x7e;"/><hkern k="-19" u1="&#x32;" u2="_"/><hkern k="19" u1="&#x32;" u2="\"/><hkern k="-30" u1="&#x32;" u2="&#x3e;"/><hkern k="-9" u1="&#x32;" u2="&#x3d;"/><hkern k="11" u1="&#x32;" u2="&#x34;"/><hkern k="-5" u1="&#x32;" u2="&#x33;"/><hkern k="4" u1="&#x32;" u2="&#x31;"/><hkern k="-9" u1="&#x32;" u2="&#x2f;"/><hkern k="-9" u1="&#x32;" u2="&#x2a;"/><hkern k="9" u1="&#x32;" u2="&#x29;"/><hkern g2="one.ss05" k="-13" u1="&#x33;"/><hkern g2="florin.tf" k="32" u1="&#x33;"/><hkern k="-2" u1="&#x33;" u2="&#x2265;"/><hkern k="-11" u1="&#x33;" u2="&#x2248;"/><hkern k="9" u1="&#x33;" u2="&#x2215;"/><hkern k="-10" u1="&#x33;" u2="&#x2212;"/><hkern k="9" u1="&#x33;" u2="&#x2122;"/><hkern k="1" u1="&#x33;" u2="&#x2030;"/><hkern k="-9" u1="&#x33;" u2="&#xf7;"/><hkern k="-1" u1="&#x33;" u2="&#xd7;"/><hkern k="-1" u1="&#x33;" u2="&#xb1;"/><hkern k="-1" u1="&#x33;" u2="&#xb0;"/><hkern k="-2" u1="&#x33;" u2="&#xa6;"/><hkern k="-10" u1="&#x33;" u2="&#x7e;"/><hkern k="-2" u1="&#x33;" u2="&#x7c;"/><hkern k="10" u1="&#x33;" u2="_"/><hkern k="-21" u1="&#x33;" u2="&#x3e;"/><hkern k="-9" u1="&#x33;" u2="&#x3d;"/><hkern k="-6" u1="&#x33;" u2="&#x37;"/><hkern k="5" u1="&#x33;" u2="&#x32;"/><hkern k="14" u1="&#x33;" u2="&#x31;"/><hkern k="1" u1="&#x33;" u2="&#x2f;"/><hkern k="-10" u1="&#x33;" u2="&#x2a;"/><hkern k="1" u1="&#x33;" u2="&#x25;"/><hkern k="11" u1="&#x33;" u2="&#x23;"/><hkern g2="registered.ss06" k="2" u1="&#x34;"/><hkern g2="one.ss05" k="-4" u1="&#x34;"/><hkern g2="questiondown.case" k="-9" u1="&#x34;"/><hkern g2="florin.tf" k="4" u1="&#x34;"/><hkern g2="numbersign.tf" k="-36" u1="&#x34;"/><hkern g2="u.ordn" k="9" u1="&#x34;"/><hkern g2="t.ordn" k="41" u1="&#x34;"/><hkern g2="s.ordn" k="18" u1="&#x34;"/><hkern g2="q.ordn" k="36" u1="&#x34;"/><hkern g2="o.ordn" k="27" u1="&#x34;"/><hkern g2="f.ordn" k="27" u1="&#x34;"/><hkern g2="e.ordn" k="39" u1="&#x34;"/><hkern g2="a.ordn" k="27" u1="&#x34;"/><hkern k="-3" u1="&#x34;" u2="&#x2265;"/><hkern k="-3" u1="&#x34;" u2="&#x2264;"/><hkern k="-10" u1="&#x34;" u2="&#x2260;"/><hkern k="-12" u1="&#x34;" u2="&#x2248;"/><hkern k="-1" u1="&#x34;" u2="&#x221e;"/><hkern k="-1" u1="&#x34;" u2="&#x2215;"/><hkern k="-9" u1="&#x34;" u2="&#x2212;"/><hkern k="48" u1="&#x34;" u2="&#x2122;"/><hkern k="-9" u1="&#x34;" u2="&#x2113;"/><hkern k="9" u1="&#x34;" u2="&#x20ac;"/><hkern k="21" u1="&#x34;" u2="&#x2030;"/><hkern k="-9" u1="&#x34;" u2="&#xf7;"/><hkern k="-19" u1="&#x34;" u2="&#xd7;"/><hkern k="27" u1="&#x34;" u2="&#xba;"/><hkern k="-1" u1="&#x34;" u2="&#xb1;"/><hkern k="10" u1="&#x34;" u2="&#xb0;"/><hkern k="31" u1="&#x34;" u2="&#xaa;"/><hkern k="-1" u1="&#x34;" u2="&#xa6;"/><hkern k="-20" u1="&#x34;" u2="&#x7e;"/><hkern k="36" u1="&#x34;" u2="\"/><hkern k="9" u1="&#x34;" u2="&#x3f;"/><hkern k="-21" u1="&#x34;" u2="&#x3e;"/><hkern k="-20" u1="&#x34;" u2="&#x3d;"/><hkern k="-18" u1="&#x34;" u2="&#x3c;"/><hkern k="10" u1="&#x34;" u2="&#x37;"/><hkern k="23" u1="&#x34;" u2="&#x31;"/><hkern k="-9" u1="&#x34;" u2="&#x2b;"/><hkern k="11" u1="&#x34;" u2="&#x2a;"/><hkern k="1" u1="&#x34;" u2="&#x25;"/><hkern g2="registered.ss06" k="-1" u1="&#x35;"/><hkern g2="one.ss05" k="1" u1="&#x35;"/><hkern g2="questiondown.case" k="-19" u1="&#x35;"/><hkern g2="florin.tf" k="7" u1="&#x35;"/><hkern k="-2" u1="&#x35;" u2="&#x2265;"/><hkern k="-1" u1="&#x35;" u2="&#x2264;"/><hkern k="-1" u1="&#x35;" u2="&#x2260;"/><hkern k="-11" u1="&#x35;" u2="&#x2248;"/><hkern k="19" u1="&#x35;" u2="&#x2215;"/><hkern k="-10" u1="&#x35;" u2="&#x2212;"/><hkern k="36" u1="&#x35;" u2="&#x2122;"/><hkern k="10" u1="&#x35;" u2="&#x2030;"/><hkern k="-9" u1="&#x35;" u2="&#xf7;"/><hkern k="-1" u1="&#x35;" u2="&#xb0;"/><hkern k="14" u1="&#x35;" u2="&#xaa;"/><hkern k="-10" u1="&#x35;" u2="&#x7e;"/><hkern k="10" u1="&#x35;" u2="_"/><hkern k="9" u1="&#x35;" u2="\"/><hkern k="1" u1="&#x35;" u2="&#x40;"/><hkern k="-20" u1="&#x35;" u2="&#x3e;"/><hkern k="-10" u1="&#x35;" u2="&#x3d;"/><hkern k="-1" u1="&#x35;" u2="&#x3c;"/><hkern k="-1" u1="&#x35;" u2="&#x37;"/><hkern k="14" u1="&#x35;" u2="&#x31;"/><hkern k="2" u1="&#x35;" u2="&#x2f;"/><hkern k="9" u1="&#x35;" u2="&#x29;"/><hkern k="9" u1="&#x35;" u2="&#x26;"/><hkern k="1" u1="&#x35;" u2="&#x25;"/><hkern g2="one.ss05" k="-13" u1="&#x36;"/><hkern g2="questiondown.case" k="-10" u1="&#x36;"/><hkern g2="parenright.case" k="-9" u1="&#x36;"/><hkern g2="florin.tf" k="43" u1="&#x36;"/><hkern k="-10" u1="&#x36;" u2="&#x2248;"/><hkern k="9" u1="&#x36;" u2="&#x2215;"/><hkern k="-10" u1="&#x36;" u2="&#x2212;"/><hkern k="18" u1="&#x36;" u2="&#x2122;"/><hkern k="2" u1="&#x36;" u2="&#x2030;"/><hkern k="-9" u1="&#x36;" u2="&#xf7;"/><hkern k="-9" u1="&#x36;" u2="&#xa6;"/><hkern k="-10" u1="&#x36;" u2="&#x7e;"/><hkern k="11" u1="&#x36;" u2="_"/><hkern k="1" u1="&#x36;" u2="\"/><hkern k="1" u1="&#x36;" u2="&#x40;"/><hkern k="-19" u1="&#x36;" u2="&#x3e;"/><hkern k="-10" u1="&#x36;" u2="&#x3d;"/><hkern k="-10" u1="&#x36;" u2="&#x37;"/><hkern k="-5" u1="&#x36;" u2="&#x33;"/><hkern k="18" u1="&#x36;" u2="&#x31;"/><hkern k="-10" u1="&#x36;" u2="&#x2b;"/><hkern k="-9" u1="&#x36;" u2="&#x2a;"/><hkern k="-9" u1="&#x36;" u2="&#x29;"/><hkern k="2" u1="&#x36;" u2="&#x23;"/><hkern g2="registered.ss06" k="-3" u1="&#x37;"/><hkern g2="one.ss05" k="-21" u1="&#x37;"/><hkern g2="questiondown.case" k="49" u1="&#x37;"/><hkern g2="parenright.case" k="5" u1="&#x37;"/><hkern g2="perthousand.tf" k="-18" u1="&#x37;"/><hkern g2="florin.tf" k="52" u1="&#x37;"/><hkern g2="percent.tf" k="-18" u1="&#x37;"/><hkern g2="numbersign.tf" k="9" u1="&#x37;"/><hkern g2="t.ordn" k="-45" u1="&#x37;"/><hkern g2="o.ordn" k="-14" u1="&#x37;"/><hkern g2="e.ordn" k="-20" u1="&#x37;"/><hkern g2="a.ordn" k="-18" u1="&#x37;"/><hkern k="-2" u1="&#x37;" u2="&#x2265;"/><hkern k="1" u1="&#x37;" u2="&#x2260;"/><hkern k="37" u1="&#x37;" u2="&#x221e;"/><hkern k="-18" u1="&#x37;" u2="&#x221a;"/><hkern k="96" u1="&#x37;" u2="&#x2215;"/><hkern k="21" u1="&#x37;" u2="&#x2212;"/><hkern k="-27" u1="&#x37;" u2="&#x2211;"/><hkern k="36" u1="&#x37;" u2="&#x2206;"/><hkern k="-20" u1="&#x37;" u2="&#x2122;"/><hkern k="36" u1="&#x37;" u2="&#x20ac;"/><hkern k="-1" u1="&#x37;" u2="&#x2030;"/><hkern k="-5" u1="&#x37;" u2="&#x2021;"/><hkern k="-37" u1="&#x37;" u2="&#x2020;"/><hkern k="20" u1="&#x37;" u2="&#xf7;"/><hkern k="78" u1="&#x37;" u2="&#xbf;"/><hkern k="-18" u1="&#x37;" u2="&#xba;"/><hkern k="13" u1="&#x37;" u2="&#xb7;"/><hkern k="1" u1="&#x37;" u2="&#xb1;"/><hkern k="-39" u1="&#x37;" u2="&#xb0;"/><hkern k="-20" u1="&#x37;" u2="&#xa6;"/><hkern k="-36" u1="&#x37;" u2="&#xa5;"/><hkern k="9" u1="&#x37;" u2="&#xa1;"/><hkern k="31" u1="&#x37;" u2="&#x7e;"/><hkern k="-20" u1="&#x37;" u2="&#x7c;"/><hkern k="81" u1="&#x37;" u2="_"/><hkern k="-21" u1="&#x37;" u2="\"/><hkern k="10" u1="&#x37;" u2="&#x40;"/><hkern k="-20" u1="&#x37;" u2="&#x3f;"/><hkern k="-39" u1="&#x37;" u2="&#x3e;"/><hkern k="-1" u1="&#x37;" u2="&#x3d;"/><hkern k="39" u1="&#x37;" u2="&#x3c;"/><hkern k="-1" u1="&#x37;" u2="&#x39;"/><hkern k="-30" u1="&#x37;" u2="&#x37;"/><hkern k="6" u1="&#x37;" u2="&#x35;"/><hkern k="37" u1="&#x37;" u2="&#x34;"/><hkern k="-23" u1="&#x37;" u2="&#x33;"/><hkern k="-9" u1="&#x37;" u2="&#x32;"/><hkern k="-5" u1="&#x37;" u2="&#x31;"/><hkern k="78" u1="&#x37;" u2="&#x2f;"/><hkern k="41" u1="&#x37;" u2="&#x2b;"/><hkern k="-29" u1="&#x37;" u2="&#x2a;"/><hkern k="9" u1="&#x37;" u2="&#x29;"/><hkern k="9" u1="&#x37;" u2="&#x26;"/><hkern k="40" u1="&#x37;" u2="&#x23;"/><hkern k="-9" u1="&#x37;" u2="&#x21;"/><hkern g2="one.ss05" k="-8" u1="&#x38;"/><hkern g2="parenright.case" k="9" u1="&#x38;"/><hkern g2="florin.tf" k="42" u1="&#x38;"/><hkern g2="t.ordn" k="3" u1="&#x38;"/><hkern k="-1" u1="&#x38;" u2="&#x2265;"/><hkern k="-1" u1="&#x38;" u2="&#x2260;"/><hkern k="-11" u1="&#x38;" u2="&#x2248;"/><hkern k="9" u1="&#x38;" u2="&#x2215;"/><hkern k="-9" u1="&#x38;" u2="&#x2212;"/><hkern k="36" u1="&#x38;" u2="&#x2122;"/><hkern k="9" u1="&#x38;" u2="&#x20ac;"/><hkern k="-9" u1="&#x38;" u2="&#xf7;"/><hkern k="2" u1="&#x38;" u2="&#xa3;"/><hkern k="-9" u1="&#x38;" u2="&#x7e;"/><hkern k="19" u1="&#x38;" u2="_"/><hkern k="36" u1="&#x38;" u2="\"/><hkern k="-10" u1="&#x38;" u2="&#x3e;"/><hkern k="-9" u1="&#x38;" u2="&#x3d;"/><hkern k="5" u1="&#x38;" u2="&#x39;"/><hkern k="5" u1="&#x38;" u2="&#x35;"/><hkern k="5" u1="&#x38;" u2="&#x34;"/><hkern k="-5" u1="&#x38;" u2="&#x33;"/><hkern k="14" u1="&#x38;" u2="&#x31;"/><hkern k="-9" u1="&#x38;" u2="&#x2b;"/><hkern k="18" u1="&#x38;" u2="&#x29;"/><hkern k="19" u1="&#x38;" u2="&#x26;"/><hkern k="1" u1="&#x38;" u2="&#x25;"/><hkern k="1" u1="&#x38;" u2="&#x23;"/><hkern k="-18" u1="&#x3b;" u2="j"/><hkern g2="one.ss05" k="-19" u1="&#x3c;"/><hkern k="-9" u1="&#x3c;" u2="&#x141;"/><hkern k="-27" u1="&#x3c;" u2="v"/><hkern k="-9" u1="&#x3c;" u2="X"/><hkern k="-9" u1="&#x3c;" u2="V"/><hkern k="-21" u1="&#x3c;" u2="&#x39;"/><hkern k="-30" u1="&#x3c;" u2="&#x38;"/><hkern k="-52" u1="&#x3c;" u2="&#x37;"/><hkern k="-20" u1="&#x3c;" u2="&#x35;"/><hkern k="-18" u1="&#x3c;" u2="&#x34;"/><hkern k="-37" u1="&#x3c;" u2="&#x33;"/><hkern k="-30" u1="&#x3c;" u2="&#x32;"/><hkern k="-21" u1="&#x3c;" u2="&#x31;"/><hkern g2="one.ss05" k="4" u1="&#x3d;"/><hkern k="-9" u1="&#x3d;" u2="v"/><hkern k="19" u1="&#x3d;" u2="X"/><hkern k="37" u1="&#x3d;" u2="V"/><hkern k="1" u1="&#x3d;" u2="&#x37;"/><hkern k="-1" u1="&#x3d;" u2="&#x35;"/><hkern k="-9" u1="&#x3d;" u2="&#x34;"/><hkern k="18" u1="&#x3d;" u2="&#x31;"/><hkern g2="one.ss05" k="23" u1="&#x3e;"/><hkern k="-9" u1="&#x3e;" u2="&#x142;"/><hkern k="9" u1="&#x3e;" u2="x"/><hkern k="9" u1="&#x3e;" u2="v"/><hkern k="65" u1="&#x3e;" u2="X"/><hkern k="47" u1="&#x3e;" u2="V"/><hkern k="18" u1="&#x3e;" u2="&#x37;"/><hkern k="-9" u1="&#x3e;" u2="&#x34;"/><hkern k="2" u1="&#x3e;" u2="&#x33;"/><hkern k="1" u1="&#x3e;" u2="&#x32;"/><hkern k="28" u1="&#x3e;" u2="&#x31;"/><hkern g2="one.ss05" k="1" u1="&#x3f;"/><hkern k="9" u1="&#x3f;" u2="&#x141;"/><hkern k="-2" u1="&#x3f;" u2="v"/><hkern k="9" u1="&#x3f;" u2="X"/><hkern k="-21" u1="&#x3f;" u2="&#x37;"/><hkern k="2" u1="&#x3f;" u2="&#x34;"/><hkern k="-1" u1="&#x3f;" u2="&#x31;"/><hkern g2="one.ss05" k="12" u1="&#x40;"/><hkern k="9" u1="&#x40;" u2="&#x142;"/><hkern k="4" u1="&#x40;" u2="&#x141;"/><hkern k="-9" u1="&#x40;" u2="&#xee;"/><hkern k="18" u1="&#x40;" u2="&#xdf;"/><hkern k="4" u1="&#x40;" u2="x"/><hkern k="-2" u1="&#x40;" u2="v"/><hkern k="43" u1="&#x40;" u2="X"/><hkern k="38" u1="&#x40;" u2="V"/><hkern k="9" u1="&#x40;" u2="&#x38;"/><hkern k="8" u1="&#x40;" u2="&#x37;"/><hkern k="9" u1="&#x40;" u2="&#x35;"/><hkern k="10" u1="&#x40;" u2="&#x34;"/><hkern k="6" u1="&#x40;" u2="&#x33;"/><hkern k="14" u1="&#x40;" u2="&#x32;"/><hkern k="9" u1="&#x40;" u2="&#x31;"/><hkern g2="registered.ss06" k="-14" u1="B"/><hkern g2="questiondown.case" k="-15" u1="B"/><hkern g2="braceleft.case" k="20" u1="B"/><hkern k="19" u1="B" u2="&#x2122;"/><hkern k="1" u1="B" u2="&#x2086;"/><hkern k="1" u1="B" u2="&#x2085;"/><hkern k="4" u1="B" u2="&#x2084;"/><hkern k="3" u1="B" u2="&#x2077;"/><hkern k="18" u1="B" u2="&#x2075;"/><hkern k="18" u1="B" u2="&#x2074;"/><hkern k="9" u1="B" u2="&#x2070;"/><hkern k="1" u1="B" u2="&#x2022;"/><hkern k="-5" u1="B" u2="&#x2021;"/><hkern k="-5" u1="B" u2="&#x2020;"/><hkern k="-9" u1="B" u2="&#x141;"/><hkern k="-5" u1="B" u2="&#xef;"/><hkern k="-5" u1="B" u2="&#xee;"/><hkern k="15" u1="B" u2="&#xdf;"/><hkern k="21" u1="B" u2="&#xbf;"/><hkern k="1" u1="B" u2="&#xba;"/><hkern k="5" u1="B" u2="&#xb7;"/><hkern k="9" u1="B" u2="&#xb3;"/><hkern k="9" u1="B" u2="&#xb2;"/><hkern k="2" u1="B" u2="&#xaa;"/><hkern k="9" u1="B" u2="&#xa1;"/><hkern k="-5" u1="B" u2="&#x7e;"/><hkern k="18" u1="B" u2="&#x7b;"/><hkern k="1" u1="B" u2="v"/><hkern k="1" u1="B" u2="_"/><hkern k="39" u1="B" u2="\"/><hkern k="5" u1="B" u2="X"/><hkern k="9" u1="B" u2="V"/><hkern k="-15" u1="B" u2="&#x3e;"/><hkern k="-5" u1="B" u2="&#x3d;"/><hkern k="11" u1="B" u2="&#x2f;"/><hkern k="1" u1="B" u2="&#x2b;"/><hkern k="28" u1="B" u2="&#x26;"/><hkern k="-6" u1="C" u2="&#xee;"/><hkern k="-5" u1="D" u2="&#xef;"/><hkern k="-5" u1="D" u2="&#xee;"/><hkern k="-14" u1="E" u2="&#xef;"/><hkern k="-18" u1="E" u2="&#xee;"/><hkern k="-15" u1="E" u2="&#xec;"/><hkern g2="registered.ss06" k="-11" u1="F"/><hkern g2="questiondown.case" k="18" u1="F"/><hkern g2="braceleft.case" k="21" u1="F"/><hkern g2="parenright.case" k="-2" u1="F"/><hkern g2="parenleft.case" k="1" u1="F"/><hkern k="-7" u1="F" u2="&#x2122;"/><hkern k="77" u1="F" u2="&#x2089;"/><hkern k="78" u1="F" u2="&#x2088;"/><hkern k="59" u1="F" u2="&#x2087;"/><hkern k="106" u1="F" u2="&#x2086;"/><hkern k="87" u1="F" u2="&#x2085;"/><hkern k="127" u1="F" u2="&#x2084;"/><hkern k="68" u1="F" u2="&#x2083;"/><hkern k="67" u1="F" u2="&#x2082;"/><hkern k="67" u1="F" u2="&#x2081;"/><hkern k="68" u1="F" u2="&#x2080;"/><hkern k="-18" u1="F" u2="&#x2079;"/><hkern k="-9" u1="F" u2="&#x2078;"/><hkern k="-18" u1="F" u2="&#x2077;"/><hkern k="-9" u1="F" u2="&#x2076;"/><hkern k="-9" u1="F" u2="&#x2075;"/><hkern k="9" u1="F" u2="&#x2074;"/><hkern k="-18" u1="F" u2="&#x2070;"/><hkern k="19" u1="F" u2="&#x2022;"/><hkern k="-20" u1="F" u2="&#x2021;"/><hkern k="-20" u1="F" u2="&#x2020;"/><hkern k="61" u1="F" u2="&#x192;"/><hkern k="5" u1="F" u2="&#xf7;"/><hkern k="-43" u1="F" u2="&#xef;"/><hkern k="-30" u1="F" u2="&#xee;"/><hkern k="10" u1="F" u2="&#xed;"/><hkern k="-40" u1="F" u2="&#xec;"/><hkern k="31" u1="F" u2="&#xe3;"/><hkern k="25" u1="F" u2="&#xdf;"/><hkern k="9" u1="F" u2="&#xd7;"/><hkern k="79" u1="F" u2="&#xbf;"/><hkern k="-18" u1="F" u2="&#xb9;"/><hkern k="9" u1="F" u2="&#xb7;"/><hkern k="-2" u1="F" u2="&#xb6;"/><hkern k="-18" u1="F" u2="&#xb3;"/><hkern k="-18" u1="F" u2="&#xb2;"/><hkern k="2" u1="F" u2="&#xaa;"/><hkern k="-2" u1="F" u2="&#xa7;"/><hkern k="-2" u1="F" u2="&#xa6;"/><hkern k="18" u1="F" u2="&#xa1;"/><hkern k="20" u1="F" u2="&#x7e;"/><hkern k="-20" u1="F" u2="&#x7c;"/><hkern k="2" u1="F" u2="&#x7b;"/><hkern k="15" u1="F" u2="x"/><hkern k="16" u1="F" u2="v"/><hkern k="64" u1="F" u2="_"/><hkern k="-1" u1="F" u2="^"/><hkern k="-21" u1="F" u2="\"/><hkern k="-14" u1="F" u2="X"/><hkern k="-14" u1="F" u2="V"/><hkern k="10" u1="F" u2="&#x40;"/><hkern k="-10" u1="F" u2="&#x3f;"/><hkern k="-19" u1="F" u2="&#x3e;"/><hkern k="9" u1="F" u2="&#x3d;"/><hkern k="9" u1="F" u2="&#x3c;"/><hkern k="78" u1="F" u2="&#x2f;"/><hkern k="11" u1="F" u2="&#x2b;"/><hkern k="-2" u1="F" u2="&#x2a;"/><hkern k="-3" u1="F" u2="&#x29;"/><hkern k="46" u1="F" u2="&#x26;"/><hkern k="-1" u1="G" u2="&#xee;"/><hkern k="-18" u1="H" u2="&#xef;"/><hkern k="-9" u1="H" u2="&#xee;"/><hkern k="-18" u1="H" u2="&#xec;"/><hkern k="-18" u1="I" u2="&#xef;"/><hkern k="-9" u1="I" u2="&#xee;"/><hkern k="-18" u1="I" u2="&#xec;"/><hkern k="-9" u1="J" u2="&#xef;"/><hkern k="-10" u1="J" u2="&#xee;"/><hkern k="-9" u1="J" u2="&#xec;"/><hkern k="-54" u1="K" u2="&#xef;"/><hkern k="-23" u1="K" u2="&#xee;"/><hkern k="-12" u1="K" u2="&#xec;"/><hkern k="-18" u1="M" u2="&#xef;"/><hkern k="-9" u1="M" u2="&#xee;"/><hkern k="-18" u1="M" u2="&#xec;"/><hkern k="-18" u1="N" u2="&#xef;"/><hkern k="-9" u1="N" u2="&#xee;"/><hkern k="-18" u1="N" u2="&#xec;"/><hkern k="-5" u1="O" u2="&#xef;"/><hkern k="-5" u1="O" u2="&#xee;"/><hkern g2="registered.ss06" k="-21" u1="P"/><hkern g2="ampersand.ss04" k="9" u1="P"/><hkern g2="questiondown.case" k="28" u1="P"/><hkern g2="braceleft.case" k="10" u1="P"/><hkern g2="parenright.case" k="18" u1="P"/><hkern g2="z.ordn" k="-18" u1="P"/><hkern g2="y.ordn" k="-20" u1="P"/><hkern g2="x.ordn" k="-19" u1="P"/><hkern g2="w.ordn" k="-20" u1="P"/><hkern g2="v.ordn" k="-20" u1="P"/><hkern g2="u.ordn" k="-18" u1="P"/><hkern g2="t.ordn" k="-18" u1="P"/><hkern g2="j.ordn" k="20" u1="P"/><hkern k="9" u1="P" u2="&#x2122;"/><hkern k="98" u1="P" u2="&#x2089;"/><hkern k="98" u1="P" u2="&#x2088;"/><hkern k="69" u1="P" u2="&#x2087;"/><hkern k="107" u1="P" u2="&#x2086;"/><hkern k="80" u1="P" u2="&#x2085;"/><hkern k="119" u1="P" u2="&#x2084;"/><hkern k="79" u1="P" u2="&#x2083;"/><hkern k="98" u1="P" u2="&#x2082;"/><hkern k="71" u1="P" u2="&#x2081;"/><hkern k="89" u1="P" u2="&#x2080;"/><hkern k="-18" u1="P" u2="&#x2079;"/><hkern k="-18" u1="P" u2="&#x2078;"/><hkern k="-18" u1="P" u2="&#x2077;"/><hkern k="-18" u1="P" u2="&#x2076;"/><hkern k="-18" u1="P" u2="&#x2075;"/><hkern k="-18" u1="P" u2="&#x2074;"/><hkern k="-18" u1="P" u2="&#x2070;"/><hkern k="11" u1="P" u2="&#x2022;"/><hkern k="-20" u1="P" u2="&#x2021;"/><hkern k="-20" u1="P" u2="&#x2020;"/><hkern k="51" u1="P" u2="&#x192;"/><hkern k="11" u1="P" u2="&#x161;"/><hkern k="1" u1="P" u2="&#x142;"/><hkern k="20" u1="P" u2="&#xfc;"/><hkern k="20" u1="P" u2="&#xfb;"/><hkern k="20" u1="P" u2="&#xfa;"/><hkern k="20" u1="P" u2="&#xf9;"/><hkern k="1" u1="P" u2="&#xf7;"/><hkern k="-43" u1="P" u2="&#xef;"/><hkern k="-27" u1="P" u2="&#xee;"/><hkern k="-2" u1="P" u2="&#xec;"/><hkern k="24" u1="P" u2="&#xdf;"/><hkern k="-1" u1="P" u2="&#xd7;"/><hkern k="89" u1="P" u2="&#xbf;"/><hkern k="-18" u1="P" u2="&#xb9;"/><hkern k="11" u1="P" u2="&#xb7;"/><hkern k="-11" u1="P" u2="&#xb6;"/><hkern k="-17" u1="P" u2="&#xb3;"/><hkern k="-18" u1="P" u2="&#xb2;"/><hkern k="-1" u1="P" u2="&#xa7;"/><hkern k="20" u1="P" u2="&#x7e;"/><hkern k="20" u1="P" u2="&#x7b;"/><hkern k="-5" u1="P" u2="v"/><hkern k="81" u1="P" u2="_"/><hkern k="18" u1="P" u2="\"/><hkern k="19" u1="P" u2="X"/><hkern k="5" u1="P" u2="V"/><hkern k="1" u1="P" u2="&#x40;"/><hkern k="-20" u1="P" u2="&#x3f;"/><hkern k="-20" u1="P" u2="&#x3e;"/><hkern k="18" u1="P" u2="&#x3c;"/><hkern k="79" u1="P" u2="&#x2f;"/><hkern k="21" u1="P" u2="&#x2b;"/><hkern k="-10" u1="P" u2="&#x2a;"/><hkern k="9" u1="P" u2="&#x29;"/><hkern k="46" u1="P" u2="&#x26;"/><hkern g2="ampersand.ss04" k="1" u1="Q"/><hkern g2="parenright.case" k="28" u1="Q"/><hkern g2="j.ordn" k="9" u1="Q"/><hkern k="20" u1="Q" u2="&#x2122;"/><hkern k="1" u1="Q" u2="&#x2084;"/><hkern k="9" u1="Q" u2="&#x2080;"/><hkern k="1" u1="Q" u2="&#x2077;"/><hkern k="-9" u1="Q" u2="&#x2021;"/><hkern k="-9" u1="Q" u2="&#x2020;"/><hkern k="2" u1="Q" u2="&#x192;"/><hkern k="-1" u1="Q" u2="&#x141;"/><hkern k="-9" u1="Q" u2="&#xf7;"/><hkern k="-5" u1="Q" u2="&#xee;"/><hkern k="15" u1="Q" u2="&#xdf;"/><hkern k="-9" u1="Q" u2="&#xd7;"/><hkern k="9" u1="Q" u2="&#xa1;"/><hkern k="-9" u1="Q" u2="&#x7e;"/><hkern k="-9" u1="Q" u2="x"/><hkern k="-9" u1="Q" u2="^"/><hkern k="49" u1="Q" u2="\"/><hkern k="24" u1="Q" u2="X"/><hkern k="30" u1="Q" u2="V"/><hkern k="-11" u1="Q" u2="&#x3e;"/><hkern k="-9" u1="Q" u2="&#x3d;"/><hkern k="-10" u1="Q" u2="&#x3c;"/><hkern k="-9" u1="Q" u2="&#x2b;"/><hkern k="28" u1="Q" u2="&#x29;"/><hkern k="18" u1="Q" u2="&#x26;"/><hkern k="9" u1="Q" u2="&#x21;"/><hkern k="-14" u1="R" u2="&#xef;"/><hkern k="-19" u1="R" u2="&#xee;"/><hkern k="-9" u1="S" u2="&#xef;"/><hkern k="-9" u1="S" u2="&#xee;"/><hkern g2="adieresis.ss02" k="48" u1="T"/><hkern g2="atilde.ss02" k="60" u1="T"/><hkern g2="agrave.ss02" k="61" u1="T"/><hkern k="21" u1="T" u2="&#x17e;"/><hkern k="19" u1="T" u2="&#x161;"/><hkern k="8" u1="T" u2="&#x131;"/><hkern k="50" u1="T" u2="&#xff;"/><hkern k="48" u1="T" u2="&#xfc;"/><hkern k="65" u1="T" u2="&#xfb;"/><hkern k="-57" u1="T" u2="&#xef;"/><hkern k="-38" u1="T" u2="&#xee;"/><hkern k="19" u1="T" u2="&#xed;"/><hkern k="-40" u1="T" u2="&#xec;"/><hkern k="56" u1="T" u2="&#xe5;"/><hkern k="32" u1="T" u2="&#xe4;"/><hkern k="45" u1="T" u2="&#xe3;"/><hkern k="46" u1="T" u2="&#xe2;"/><hkern k="45" u1="T" u2="&#xe0;"/><hkern k="-9" u1="U" u2="&#xef;"/><hkern k="-10" u1="U" u2="&#xee;"/><hkern k="-9" u1="U" u2="&#xec;"/><hkern g2="ampersand.ss04" k="9" u1="V"/><hkern g2="questiondown.case" k="38" u1="V"/><hkern g2="exclamdown.case" k="-1" u1="V"/><hkern g2="braceleft.case" k="2" u1="V"/><hkern g2="parenright.case" k="-11" u1="V"/><hkern g2="z.ordn" k="-27" u1="V"/><hkern g2="y.ordn" k="-31" u1="V"/><hkern g2="x.ordn" k="-29" u1="V"/><hkern g2="w.ordn" k="-29" u1="V"/><hkern g2="v.ordn" k="-29" u1="V"/><hkern g2="j.ordn" k="9" u1="V"/><hkern k="-3" u1="V" u2="&#x2122;"/><hkern k="118" u1="V" u2="&#x2089;"/><hkern k="118" u1="V" u2="&#x2088;"/><hkern k="79" u1="V" u2="&#x2087;"/><hkern k="117" u1="V" u2="&#x2086;"/><hkern k="99" u1="V" u2="&#x2085;"/><hkern k="117" u1="V" u2="&#x2084;"/><hkern k="87" u1="V" u2="&#x2083;"/><hkern k="88" u1="V" u2="&#x2082;"/><hkern k="87" u1="V" u2="&#x2081;"/><hkern k="109" u1="V" u2="&#x2080;"/><hkern k="-20" u1="V" u2="&#x2079;"/><hkern k="-2" u1="V" u2="&#x2078;"/><hkern k="-20" u1="V" u2="&#x2077;"/><hkern k="-9" u1="V" u2="&#x2075;"/><hkern k="2" u1="V" u2="&#x2074;"/><hkern k="-18" u1="V" u2="&#x2070;"/><hkern k="49" u1="V" u2="&#x2022;"/><hkern k="-10" u1="V" u2="&#x2021;"/><hkern k="-11" u1="V" u2="&#x2020;"/><hkern k="78" u1="V" u2="&#x192;"/><hkern k="18" u1="V" u2="&#x142;"/><hkern k="22" u1="V" u2="&#xf7;"/><hkern k="-69" u1="V" u2="&#xef;"/><hkern k="-19" u1="V" u2="&#xee;"/><hkern k="18" u1="V" u2="&#xed;"/><hkern k="-31" u1="V" u2="&#xec;"/><hkern k="60" u1="V" u2="&#xe8;"/><hkern k="42" u1="V" u2="&#xe3;"/><hkern k="42" u1="V" u2="&#xdf;"/><hkern k="10" u1="V" u2="&#xd7;"/><hkern k="96" u1="V" u2="&#xbf;"/><hkern k="5" u1="V" u2="&#xba;"/><hkern k="-37" u1="V" u2="&#xb9;"/><hkern k="39" u1="V" u2="&#xb7;"/><hkern k="-19" u1="V" u2="&#xb3;"/><hkern k="-19" u1="V" u2="&#xb2;"/><hkern k="18" u1="V" u2="&#xaa;"/><hkern k="9" u1="V" u2="&#xa7;"/><hkern k="-11" u1="V" u2="&#xa6;"/><hkern k="47" u1="V" u2="&#xa1;"/><hkern k="49" u1="V" u2="&#x7e;"/><hkern k="-11" u1="V" u2="&#x7c;"/><hkern k="30" u1="V" u2="&#x7b;"/><hkern k="14" u1="V" u2="x"/><hkern k="28" u1="V" u2="v"/><hkern k="77" u1="V" u2="_"/><hkern k="9" u1="V" u2="^"/><hkern k="-20" u1="V" u2="\"/><hkern k="-10" u1="V" u2="X"/><hkern k="-10" u1="V" u2="V"/><hkern k="38" u1="V" u2="&#x40;"/><hkern k="-1" u1="V" u2="&#x3f;"/><hkern k="37" u1="V" u2="&#x3d;"/><hkern k="47" u1="V" u2="&#x3c;"/><hkern k="96" u1="V" u2="&#x2f;"/><hkern k="49" u1="V" u2="&#x2b;"/><hkern k="-20" u1="V" u2="&#x2a;"/><hkern k="-11" u1="V" u2="&#x29;"/><hkern k="32" u1="V" u2="&#x26;"/><hkern k="-65" u1="W" u2="&#xef;"/><hkern k="-28" u1="W" u2="&#xee;"/><hkern k="14" u1="W" u2="&#xed;"/><hkern k="-39" u1="W" u2="&#xec;"/><hkern g2="registered.ss06" k="36" u1="X"/><hkern g2="ampersand.ss04" k="18" u1="X"/><hkern g2="questiondown.case" k="9" u1="X"/><hkern g2="braceleft.case" k="30" u1="X"/><hkern g2="parenright.case" k="-2" u1="X"/><hkern g2="j.ordn" k="18" u1="X"/><hkern k="19" u1="X" u2="&#x2089;"/><hkern k="8" u1="X" u2="&#x2088;"/><hkern k="27" u1="X" u2="&#x2087;"/><hkern k="-1" u1="X" u2="&#x2086;"/><hkern k="-1" u1="X" u2="&#x2085;"/><hkern k="-1" u1="X" u2="&#x2084;"/><hkern k="-1" u1="X" u2="&#x2082;"/><hkern k="-1" u1="X" u2="&#x2077;"/><hkern k="1" u1="X" u2="&#x2074;"/><hkern k="48" u1="X" u2="&#x2022;"/><hkern k="9" u1="X" u2="&#x142;"/><hkern k="28" u1="X" u2="&#xf7;"/><hkern k="-24" u1="X" u2="&#xef;"/><hkern k="-10" u1="X" u2="&#xee;"/><hkern k="-22" u1="X" u2="&#xec;"/><hkern k="18" u1="X" u2="&#xdf;"/><hkern k="19" u1="X" u2="&#xd7;"/><hkern k="27" u1="X" u2="&#xba;"/><hkern k="39" u1="X" u2="&#xb7;"/><hkern k="9" u1="X" u2="&#xb6;"/><hkern k="27" u1="X" u2="&#xaa;"/><hkern k="-10" u1="X" u2="&#xa6;"/><hkern k="9" u1="X" u2="&#xa1;"/><hkern k="29" u1="X" u2="&#x7e;"/><hkern k="-11" u1="X" u2="&#x7c;"/><hkern k="30" u1="X" u2="&#x7b;"/><hkern k="-7" u1="X" u2="x"/><hkern k="42" u1="X" u2="v"/><hkern k="-20" u1="X" u2="_"/><hkern k="18" u1="X" u2="^"/><hkern k="-10" u1="X" u2="\"/><hkern k="-10" u1="X" u2="V"/><hkern k="34" u1="X" u2="&#x40;"/><hkern k="9" u1="X" u2="&#x3f;"/><hkern k="19" u1="X" u2="&#x3d;"/><hkern k="38" u1="X" u2="&#x3c;"/><hkern k="-10" u1="X" u2="&#x2f;"/><hkern k="33" u1="X" u2="&#x2b;"/><hkern k="9" u1="X" u2="&#x2a;"/><hkern k="-2" u1="X" u2="&#x29;"/><hkern k="37" u1="X" u2="&#x26;"/><hkern g2="aring.ss02" k="83" u1="Y"/><hkern g2="adieresis.ss02" k="82" u1="Y"/><hkern g2="atilde.ss02" k="83" u1="Y"/><hkern g2="agrave.ss02" k="82" u1="Y"/><hkern k="36" u1="Y" u2="&#x161;"/><hkern k="59" u1="Y" u2="&#xf6;"/><hkern k="103" u1="Y" u2="&#xf5;"/><hkern k="84" u1="Y" u2="&#xf2;"/><hkern k="102" u1="Y" u2="&#xf0;"/><hkern k="-37" u1="Y" u2="&#xef;"/><hkern k="-9" u1="Y" u2="&#xee;"/><hkern k="48" u1="Y" u2="&#xed;"/><hkern k="-40" u1="Y" u2="&#xec;"/><hkern k="81" u1="Y" u2="&#xeb;"/><hkern k="81" u1="Y" u2="&#xe8;"/><hkern k="56" u1="Y" u2="&#xe4;"/><hkern k="70" u1="Y" u2="&#xe2;"/><hkern k="79" u1="Y" u2="&#xe0;"/><hkern k="-28" u1="Z" u2="&#xef;"/><hkern k="-28" u1="Z" u2="&#xee;"/><hkern k="-29" u1="Z" u2="&#xec;"/><hkern k="-45" u1="[" u2="&#xef;"/><hkern k="-18" u1="[" u2="&#xee;"/><hkern k="-58" u1="[" u2="&#xec;"/><hkern k="27" u1="[" u2="&#x36;"/><hkern g2="one.ss05" k="-11" u1="\"/><hkern k="9" u1="\" u2="&#x141;"/><hkern k="-10" u1="\" u2="x"/><hkern k="37" u1="\" u2="v"/><hkern k="-10" u1="\" u2="X"/><hkern k="96" u1="\" u2="V"/><hkern k="11" u1="\" u2="&#x38;"/><hkern k="9" u1="\" u2="&#x37;"/><hkern k="9" u1="\" u2="&#x35;"/><hkern k="19" u1="\" u2="&#x34;"/><hkern k="1" u1="\" u2="&#x33;"/><hkern k="-10" u1="\" u2="&#x32;"/><hkern k="18" u1="\" u2="&#x31;"/><hkern g2="one.ss05" k="4" u1="^"/><hkern k="-18" u1="^" u2="v"/><hkern k="18" u1="^" u2="X"/><hkern k="9" u1="^" u2="V"/><hkern g2="one.ss05" k="-11" u1="_"/><hkern k="-69" u1="_" u2="&#x192;"/><hkern k="18" u1="_" u2="&#x142;"/><hkern k="-21" u1="_" u2="x"/><hkern k="40" u1="_" u2="v"/><hkern k="-20" u1="_" u2="X"/><hkern k="77" u1="_" u2="V"/><hkern k="18" u1="_" u2="&#x39;"/><hkern k="19" u1="_" u2="&#x38;"/><hkern k="18" u1="_" u2="&#x35;"/><hkern k="77" u1="_" u2="&#x34;"/><hkern k="9" u1="_" u2="&#x33;"/><hkern k="45" u1="_" u2="&#x31;"/><hkern k="-52" u1="f" u2="&#xef;"/><hkern k="-47" u1="f" u2="&#xee;"/><hkern k="-57" u1="f" u2="&#xec;"/><hkern k="-12" u1="f" u2="j"/><hkern k="-9" u1="f" u2="i"/><hkern k="-9" u1="i" u2="&#xef;"/><hkern k="-9" u1="i" u2="&#xee;"/><hkern k="-9" u1="i" u2="&#xec;"/><hkern k="-5" u1="l" u2="&#xef;"/><hkern k="-5" u1="l" u2="&#xee;"/><hkern k="45" u1="q" u2="&#x2122;"/><hkern g2="registered.ss06" k="-11" u1="v"/><hkern g2="questiondown.case" k="9" u1="v"/><hkern k="9" u1="v" u2="&#x2122;"/><hkern k="57" u1="v" u2="&#x2089;"/><hkern k="57" u1="v" u2="&#x2088;"/><hkern k="39" u1="v" u2="&#x2087;"/><hkern k="57" u1="v" u2="&#x2086;"/><hkern k="76" u1="v" u2="&#x2085;"/><hkern k="86" u1="v" u2="&#x2084;"/><hkern k="48" u1="v" u2="&#x2083;"/><hkern k="48" u1="v" u2="&#x2082;"/><hkern k="48" u1="v" u2="&#x2081;"/><hkern k="57" u1="v" u2="&#x2080;"/><hkern k="-30" u1="v" u2="&#x2021;"/><hkern k="-30" u1="v" u2="&#x2020;"/><hkern k="18" u1="v" u2="&#x192;"/><hkern k="-1" u1="v" u2="&#x142;"/><hkern k="9" u1="v" u2="&#xf7;"/><hkern k="-18" u1="v" u2="&#xd7;"/><hkern k="38" u1="v" u2="&#xbf;"/><hkern k="-14" u1="v" u2="&#xba;"/><hkern k="-29" u1="v" u2="&#xb6;"/><hkern k="-18" u1="v" u2="&#xa7;"/><hkern k="-20" u1="v" u2="&#xa6;"/><hkern k="9" u1="v" u2="&#xa1;"/><hkern k="-18" u1="v" u2="&#x7c;"/><hkern k="-1" u1="v" u2="x"/><hkern k="-1" u1="v" u2="v"/><hkern k="40" u1="v" u2="_"/><hkern k="-18" u1="v" u2="^"/><hkern k="20" u1="v" u2="\"/><hkern k="-1" u1="v" u2="&#x40;"/><hkern k="-20" u1="v" u2="&#x3f;"/><hkern k="-27" u1="v" u2="&#x3e;"/><hkern k="-9" u1="v" u2="&#x3d;"/><hkern k="9" u1="v" u2="&#x3c;"/><hkern k="38" u1="v" u2="&#x2f;"/><hkern k="-28" u1="v" u2="&#x2a;"/><hkern k="25" u1="v" u2="&#x29;"/><hkern k="27" u1="v" u2="&#x26;"/><hkern g2="registered.ss06" k="-10" u1="x"/><hkern g2="seven.numr" k="27" u1="x"/><hkern k="10" u1="x" u2="&#x2122;"/><hkern k="2" u1="x" u2="&#x2087;"/><hkern k="9" u1="x" u2="&#x2022;"/><hkern k="-19" u1="x" u2="&#x2021;"/><hkern k="-19" u1="x" u2="&#x2020;"/><hkern k="9" u1="x" u2="&#xf7;"/><hkern k="-9" u1="x" u2="&#xbf;"/><hkern k="10" u1="x" u2="&#xb7;"/><hkern k="-9" u1="x" u2="&#xb6;"/><hkern k="9" u1="x" u2="&#xaa;"/><hkern k="-9" u1="x" u2="&#xa6;"/><hkern k="-1" u1="x" u2="&#xa1;"/><hkern k="9" u1="x" u2="&#x7e;"/><hkern k="-11" u1="x" u2="&#x7c;"/><hkern k="-1" u1="x" u2="x"/><hkern k="-1" u1="x" u2="v"/><hkern k="-21" u1="x" u2="_"/><hkern k="56" u1="x" u2="\"/><hkern k="-1" u1="x" u2="&#x40;"/><hkern k="-18" u1="x" u2="&#x3f;"/><hkern k="9" u1="x" u2="&#x3c;"/><hkern k="-20" u1="x" u2="&#x2f;"/><hkern k="-9" u1="x" u2="&#x2a;"/><hkern k="9" u1="x" u2="&#x29;"/><hkern k="19" u1="x" u2="&#x26;"/><hkern k="-45" u1="&#x7b;" u2="&#xef;"/><hkern k="-18" u1="&#x7b;" u2="&#xee;"/><hkern k="-58" u1="&#x7b;" u2="&#xec;"/><hkern k="5" u1="&#x7b;" u2="&#xe6;"/><hkern k="5" u1="&#x7b;" u2="&#xe5;"/><hkern k="5" u1="&#x7b;" u2="&#xe4;"/><hkern k="5" u1="&#x7b;" u2="&#xe3;"/><hkern k="5" u1="&#x7b;" u2="&#xe2;"/><hkern k="5" u1="&#x7b;" u2="&#xe1;"/><hkern k="5" u1="&#x7b;" u2="&#xe0;"/><hkern k="5" u1="&#x7b;" u2="a"/><hkern k="27" u1="&#x7b;" u2="&#x36;"/><hkern g2="one.ss05" k="2" u1="&#x7c;"/><hkern k="-11" u1="&#x7c;" u2="x"/><hkern k="-18" u1="&#x7c;" u2="v"/><hkern k="-11" u1="&#x7c;" u2="X"/><hkern k="-11" u1="&#x7c;" u2="V"/><hkern k="-20" u1="&#x7c;" u2="&#x37;"/><hkern k="-1" u1="&#x7d;" u2="&#x141;"/><hkern k="1" u1="&#x7d;" u2="X"/><hkern k="1" u1="&#x7d;" u2="V"/><hkern g2="one.ss05" k="17" u1="&#x7e;"/><hkern k="9" u1="&#x7e;" u2="x"/><hkern k="29" u1="&#x7e;" u2="X"/><hkern k="49" u1="&#x7e;" u2="V"/><hkern k="29" u1="&#x7e;" u2="&#x37;"/><hkern k="1" u1="&#x7e;" u2="&#x32;"/><hkern k="10" u1="&#x7e;" u2="&#x31;"/><hkern k="9" u1="&#xa1;" u2="&#xdf;"/><hkern k="-1" u1="&#xa1;" u2="x"/><hkern k="9" u1="&#xa1;" u2="v"/><hkern k="9" u1="&#xa1;" u2="X"/><hkern k="47" u1="&#xa1;" u2="V"/><hkern k="8" u1="&#xa1;" u2="&#x37;"/><hkern k="9" u1="&#xa1;" u2="&#x31;"/><hkern g2="one.ss05" k="3" u1="&#xa2;"/><hkern k="1" u1="&#xa2;" u2="&#x33;"/><hkern k="9" u1="&#xa2;" u2="&#x31;"/><hkern k="-18" u1="&#xa3;" u2="&#x34;"/><hkern k="-18" u1="&#xa3;" u2="&#x33;"/><hkern k="-9" u1="&#xa3;" u2="&#x32;"/><hkern g2="one.ss05" k="2" u1="&#xa4;"/><hkern g2="one.ss05" k="3" u1="&#xa5;"/><hkern k="-21" u1="&#xa5;" u2="&#x37;"/><hkern k="1" u1="&#xa5;" u2="&#x34;"/><hkern k="-9" u1="&#xa6;" u2="x"/><hkern k="-20" u1="&#xa6;" u2="v"/><hkern k="-10" u1="&#xa6;" u2="X"/><hkern k="-11" u1="&#xa6;" u2="V"/><hkern k="1" u1="&#xa6;" u2="&#x38;"/><hkern k="-1" u1="&#xa6;" u2="&#x37;"/><hkern g2="one.ss05" k="2" u1="&#xa7;"/><hkern k="9" u1="&#xa7;" u2="V"/><hkern k="1" u1="&#xac;" u2="&#x33;"/><hkern g2="one.ss05" k="-16" u1="&#xb0;"/><hkern k="-31" u1="&#xb0;" u2="&#x37;"/><hkern k="4" u1="&#xb0;" u2="&#x34;"/><hkern k="6" u1="&#xb0;" u2="&#x31;"/><hkern k="1" u1="&#xb1;" u2="&#x37;"/><hkern k="1" u1="&#xb1;" u2="&#x33;"/><hkern k="18" u1="&#xb1;" u2="&#x31;"/><hkern g2="one.ss05" k="3" u1="&#xb6;"/><hkern g2="one.ss05" k="14" u1="&#xb7;"/><hkern k="10" u1="&#xb7;" u2="x"/><hkern k="39" u1="&#xb7;" u2="X"/><hkern k="39" u1="&#xb7;" u2="V"/><hkern k="11" u1="&#xb7;" u2="&#x37;"/><hkern k="12" u1="&#xb7;" u2="&#x31;"/><hkern k="-18" u1="&#xba;" u2="&#x37;"/><hkern g2="one.ss05" k="1" u1="&#xbf;"/><hkern k="-54" u1="&#xbf;" u2="&#x192;"/><hkern k="18" u1="&#xbf;" u2="&#x142;"/><hkern k="9" u1="&#xbf;" u2="&#x141;"/><hkern k="27" u1="&#xbf;" u2="&#xdf;"/><hkern k="44" u1="&#xbf;" u2="v"/><hkern k="110" u1="&#xbf;" u2="V"/><hkern k="27" u1="&#xbf;" u2="&#x39;"/><hkern k="36" u1="&#xbf;" u2="&#x38;"/><hkern k="18" u1="&#xbf;" u2="&#x37;"/><hkern k="27" u1="&#xbf;" u2="&#x35;"/><hkern k="56" u1="&#xbf;" u2="&#x34;"/><hkern k="28" u1="&#xbf;" u2="&#x33;"/><hkern k="46" u1="&#xbf;" u2="&#x31;"/><hkern k="-14" u1="&#xc6;" u2="&#xef;"/><hkern k="-18" u1="&#xc6;" u2="&#xee;"/><hkern k="-15" u1="&#xc6;" u2="&#xec;"/><hkern k="-6" u1="&#xc7;" u2="&#xee;"/><hkern k="-14" u1="&#xc8;" u2="&#xef;"/><hkern k="-18" u1="&#xc8;" u2="&#xee;"/><hkern k="-15" u1="&#xc8;" u2="&#xec;"/><hkern k="-14" u1="&#xc9;" u2="&#xef;"/><hkern k="-18" u1="&#xc9;" u2="&#xee;"/><hkern k="-15" u1="&#xc9;" u2="&#xec;"/><hkern k="-14" u1="&#xca;" u2="&#xef;"/><hkern k="-18" u1="&#xca;" u2="&#xee;"/><hkern k="-15" u1="&#xca;" u2="&#xec;"/><hkern k="-14" u1="&#xcb;" u2="&#xef;"/><hkern k="-18" u1="&#xcb;" u2="&#xee;"/><hkern k="-15" u1="&#xcb;" u2="&#xec;"/><hkern k="-18" u1="&#xcc;" u2="&#xef;"/><hkern k="-9" u1="&#xcc;" u2="&#xee;"/><hkern k="-18" u1="&#xcc;" u2="&#xec;"/><hkern k="-18" u1="&#xcd;" u2="&#xef;"/><hkern k="-9" u1="&#xcd;" u2="&#xee;"/><hkern k="-18" u1="&#xcd;" u2="&#xec;"/><hkern k="-5" u1="&#xcd;" u2="&#xcf;"/><hkern k="-9" u1="&#xcd;" u2="&#xcc;"/><hkern k="-18" u1="&#xce;" u2="&#xef;"/><hkern k="-9" u1="&#xce;" u2="&#xee;"/><hkern k="-18" u1="&#xce;" u2="&#xec;"/><hkern k="-4" u1="&#xce;" u2="&#xcf;"/><hkern k="-6" u1="&#xce;" u2="&#xce;"/><hkern k="-3" u1="&#xce;" u2="&#xcc;"/><hkern k="-18" u1="&#xcf;" u2="&#xef;"/><hkern k="-9" u1="&#xcf;" u2="&#xee;"/><hkern k="-18" u1="&#xcf;" u2="&#xec;"/><hkern k="-2" u1="&#xcf;" u2="&#xcf;"/><hkern k="-4" u1="&#xcf;" u2="&#xce;"/><hkern k="-4" u1="&#xcf;" u2="&#xcc;"/><hkern k="-5" u1="&#xd0;" u2="&#xef;"/><hkern k="-5" u1="&#xd0;" u2="&#xee;"/><hkern k="-18" u1="&#xd1;" u2="&#xef;"/><hkern k="-9" u1="&#xd1;" u2="&#xee;"/><hkern k="-18" u1="&#xd1;" u2="&#xec;"/><hkern k="-5" u1="&#xd2;" u2="&#xef;"/><hkern k="-5" u1="&#xd2;" u2="&#xee;"/><hkern k="-5" u1="&#xd3;" u2="&#xef;"/><hkern k="-5" u1="&#xd3;" u2="&#xee;"/><hkern k="-5" u1="&#xd4;" u2="&#xef;"/><hkern k="-5" u1="&#xd4;" u2="&#xee;"/><hkern k="-5" u1="&#xd5;" u2="&#xef;"/><hkern k="-5" u1="&#xd5;" u2="&#xee;"/><hkern k="-5" u1="&#xd6;" u2="&#xef;"/><hkern k="-5" u1="&#xd6;" u2="&#xee;"/><hkern k="-18" u1="&#xd7;" u2="v"/><hkern k="19" u1="&#xd7;" u2="X"/><hkern k="10" u1="&#xd7;" u2="V"/><hkern k="18" u1="&#xd7;" u2="&#x31;"/><hkern k="-9" u1="&#xd8;" u2="&#xef;"/><hkern k="-9" u1="&#xd8;" u2="&#xee;"/><hkern k="-9" u1="&#xd9;" u2="&#xef;"/><hkern k="-10" u1="&#xd9;" u2="&#xee;"/><hkern k="-9" u1="&#xd9;" u2="&#xec;"/><hkern k="-9" u1="&#xda;" u2="&#xef;"/><hkern k="-10" u1="&#xda;" u2="&#xee;"/><hkern k="-9" u1="&#xda;" u2="&#xec;"/><hkern k="-9" u1="&#xdb;" u2="&#xef;"/><hkern k="-10" u1="&#xdb;" u2="&#xee;"/><hkern k="-9" u1="&#xdb;" u2="&#xec;"/><hkern k="-9" u1="&#xdc;" u2="&#xef;"/><hkern k="-10" u1="&#xdc;" u2="&#xee;"/><hkern k="-9" u1="&#xdc;" u2="&#xec;"/><hkern g2="aring.ss02" k="83" u1="&#xdd;"/><hkern g2="adieresis.ss02" k="82" u1="&#xdd;"/><hkern g2="atilde.ss02" k="83" u1="&#xdd;"/><hkern g2="agrave.ss02" k="82" u1="&#xdd;"/><hkern k="36" u1="&#xdd;" u2="&#x161;"/><hkern k="59" u1="&#xdd;" u2="&#xf6;"/><hkern k="103" u1="&#xdd;" u2="&#xf5;"/><hkern k="84" u1="&#xdd;" u2="&#xf2;"/><hkern k="102" u1="&#xdd;" u2="&#xf0;"/><hkern k="-37" u1="&#xdd;" u2="&#xef;"/><hkern k="-9" u1="&#xdd;" u2="&#xee;"/><hkern k="12" u1="&#xdd;" u2="&#xed;"/><hkern k="-40" u1="&#xdd;" u2="&#xec;"/><hkern k="81" u1="&#xdd;" u2="&#xeb;"/><hkern k="81" u1="&#xdd;" u2="&#xe8;"/><hkern k="56" u1="&#xdd;" u2="&#xe4;"/><hkern k="70" u1="&#xdd;" u2="&#xe2;"/><hkern k="79" u1="&#xdd;" u2="&#xe0;"/><hkern g2="ampersand.ss04" k="9" u1="&#xde;"/><hkern g2="parenright.case" k="19" u1="&#xde;"/><hkern k="39" u1="&#xde;" u2="&#x2122;"/><hkern k="27" u1="&#xde;" u2="&#x2089;"/><hkern k="18" u1="&#xde;" u2="&#x2088;"/><hkern k="37" u1="&#xde;" u2="&#x2086;"/><hkern k="36" u1="&#xde;" u2="&#x2085;"/><hkern k="76" u1="&#xde;" u2="&#x2084;"/><hkern k="27" u1="&#xde;" u2="&#x2083;"/><hkern k="36" u1="&#xde;" u2="&#x2082;"/><hkern k="9" u1="&#xde;" u2="&#x2081;"/><hkern k="18" u1="&#xde;" u2="&#x2080;"/><hkern k="-9" u1="&#xde;" u2="&#x2022;"/><hkern k="-10" u1="&#xde;" u2="&#x2021;"/><hkern k="-10" u1="&#xde;" u2="&#x2020;"/><hkern k="49" u1="&#xde;" u2="&#x192;"/><hkern k="-11" u1="&#xde;" u2="&#x142;"/><hkern k="-2" u1="&#xde;" u2="&#x141;"/><hkern k="-9" u1="&#xde;" u2="&#xf7;"/><hkern k="11" u1="&#xde;" u2="&#xdf;"/><hkern k="-9" u1="&#xde;" u2="&#xd7;"/><hkern k="56" u1="&#xde;" u2="&#xbf;"/><hkern k="-1" u1="&#xde;" u2="&#xb7;"/><hkern k="-10" u1="&#xde;" u2="&#x7e;"/><hkern k="-1" u1="&#xde;" u2="x"/><hkern k="-1" u1="&#xde;" u2="v"/><hkern k="48" u1="&#xde;" u2="_"/><hkern k="-9" u1="&#xde;" u2="^"/><hkern k="58" u1="&#xde;" u2="\"/><hkern k="38" u1="&#xde;" u2="X"/><hkern k="20" u1="&#xde;" u2="V"/><hkern k="-1" u1="&#xde;" u2="&#x40;"/><hkern k="-1" u1="&#xde;" u2="&#x3f;"/><hkern k="-10" u1="&#xde;" u2="&#x3e;"/><hkern k="-9" u1="&#xde;" u2="&#x3d;"/><hkern k="-9" u1="&#xde;" u2="&#x3c;"/><hkern k="67" u1="&#xde;" u2="&#x2f;"/><hkern k="-9" u1="&#xde;" u2="&#x2b;"/><hkern k="19" u1="&#xde;" u2="&#x29;"/><hkern k="19" u1="&#xde;" u2="&#x26;"/><hkern g2="registered.ss06" k="2" u1="&#xdf;"/><hkern g2="questiondown.case" k="-18" u1="&#xdf;"/><hkern k="27" u1="&#xdf;" u2="&#x2122;"/><hkern k="-9" u1="&#xdf;" u2="&#x2021;"/><hkern k="9" u1="&#xdf;" u2="&#x192;"/><hkern k="-5" u1="&#xdf;" u2="&#x142;"/><hkern k="-9" u1="&#xdf;" u2="&#xf7;"/><hkern k="-5" u1="&#xdf;" u2="&#xef;"/><hkern k="-5" u1="&#xdf;" u2="&#xee;"/><hkern k="2" u1="&#xdf;" u2="&#xdf;"/><hkern k="10" u1="&#xdf;" u2="&#xba;"/><hkern k="20" u1="&#xdf;" u2="&#xaa;"/><hkern k="9" u1="&#xdf;" u2="v"/><hkern k="-18" u1="&#xdf;" u2="_"/><hkern k="37" u1="&#xdf;" u2="\"/><hkern k="-45" u1="&#xdf;" u2="&#x3e;"/><hkern k="-9" u1="&#xdf;" u2="&#x3d;"/><hkern k="-1" u1="&#xdf;" u2="&#x2f;"/><hkern k="-2" u1="&#xdf;" u2="&#x29;"/><hkern k="9" u1="&#xdf;" u2="&#x26;"/><hkern k="61" u1="&#xe9;" u2="\"/><hkern k="61" u1="&#xeb;" u2="\"/><hkern k="27" u1="&#xec;" u2="&#x2122;"/><hkern k="29" u1="&#xec;" u2="\"/><hkern g2="i.ordn" k="-18" u1="&#xed;"/><hkern g2="h.ordn" k="-18" u1="&#xed;"/><hkern g2="f.ordn" k="-36" u1="&#xed;"/><hkern g2="e.ordn" k="-18" u1="&#xed;"/><hkern g2="b.ordn" k="-27" u1="&#xed;"/><hkern k="-36" u1="&#xed;" u2="&#x2122;"/><hkern k="-42" u1="&#xed;" u2="&#x201d;"/><hkern k="-21" u1="&#xed;" u2="&#x201c;"/><hkern k="-42" u1="&#xed;" u2="&#x2019;"/><hkern k="-21" u1="&#xed;" u2="&#x2018;"/><hkern k="-5" u1="&#xed;" u2="&#x161;"/><hkern k="-72" u1="&#xed;" u2="&#xef;"/><hkern k="-40" u1="&#xed;" u2="&#xee;"/><hkern k="-53" u1="&#xed;" u2="&#xec;"/><hkern k="-58" u1="&#xed;" u2="&#x7d;"/><hkern k="-27" u1="&#xed;" u2="&#x7c;"/><hkern k="-9" u1="&#xed;" u2="i"/><hkern k="-58" u1="&#xed;" u2="]"/><hkern k="-52" u1="&#xed;" u2="\"/><hkern k="6" u1="&#xed;" u2="&#x3f;"/><hkern k="-9" u1="&#xed;" u2="&#x2a;"/><hkern k="-39" u1="&#xed;" u2="&#x29;"/><hkern k="-4" u1="&#xed;" u2="&#x27;"/><hkern k="-4" u1="&#xed;" u2="&#x22;"/><hkern k="-9" u1="&#xed;" u2="&#x21;"/><hkern g2="registered.ss06" k="-54" u1="&#xee;"/><hkern g2="i.ordn" k="-18" u1="&#xee;"/><hkern g2="h.ordn" k="-18" u1="&#xee;"/><hkern g2="g.ordn" k="-27" u1="&#xee;"/><hkern g2="f.ordn" k="-36" u1="&#xee;"/><hkern g2="e.ordn" k="-36" u1="&#xee;"/><hkern g2="d.ordn" k="-27" u1="&#xee;"/><hkern g2="c.ordn" k="-27" u1="&#xee;"/><hkern g2="b.ordn" k="-27" u1="&#xee;"/><hkern g2="a.ordn" k="-18" u1="&#xee;"/><hkern k="-18" u1="&#xee;" u2="&#x2122;"/><hkern k="-54" u1="&#xee;" u2="&#x2020;"/><hkern k="-39" u1="&#xee;" u2="&#x201d;"/><hkern k="-49" u1="&#xee;" u2="&#x201c;"/><hkern k="-39" u1="&#xee;" u2="&#x2019;"/><hkern k="-49" u1="&#xee;" u2="&#x2018;"/><hkern k="-5" u1="&#xee;" u2="&#x161;"/><hkern k="-73" u1="&#xee;" u2="&#xef;"/><hkern k="-25" u1="&#xee;" u2="&#xee;"/><hkern k="-27" u1="&#xee;" u2="&#xec;"/><hkern k="-18" u1="&#xee;" u2="&#x7d;"/><hkern k="-18" u1="&#xee;" u2="&#x7c;"/><hkern k="-9" u1="&#xee;" u2="j"/><hkern k="-14" u1="&#xee;" u2="i"/><hkern k="-18" u1="&#xee;" u2="]"/><hkern k="-29" u1="&#xee;" u2="\"/><hkern k="-49" u1="&#xee;" u2="&#x3f;"/><hkern k="-39" u1="&#xee;" u2="&#x2a;"/><hkern k="-18" u1="&#xee;" u2="&#x29;"/><hkern k="-4" u1="&#xee;" u2="&#x27;"/><hkern k="9" u1="&#xee;" u2="&#x26;"/><hkern k="-4" u1="&#xee;" u2="&#x22;"/><hkern k="-9" u1="&#xee;" u2="&#x21;"/><hkern g2="registered.ss06" k="-45" u1="&#xef;"/><hkern g2="i.ordn" k="-18" u1="&#xef;"/><hkern g2="h.ordn" k="-18" u1="&#xef;"/><hkern g2="g.ordn" k="-18" u1="&#xef;"/><hkern g2="f.ordn" k="-36" u1="&#xef;"/><hkern g2="e.ordn" k="-36" u1="&#xef;"/><hkern g2="d.ordn" k="-27" u1="&#xef;"/><hkern g2="c.ordn" k="-18" u1="&#xef;"/><hkern g2="b.ordn" k="-27" u1="&#xef;"/><hkern g2="a.ordn" k="-18" u1="&#xef;"/><hkern k="-36" u1="&#xef;" u2="&#x2122;"/><hkern k="-36" u1="&#xef;" u2="&#x2020;"/><hkern k="-48" u1="&#xef;" u2="&#x201d;"/><hkern k="-48" u1="&#xef;" u2="&#x201c;"/><hkern k="-48" u1="&#xef;" u2="&#x2019;"/><hkern k="-48" u1="&#xef;" u2="&#x2018;"/><hkern k="-5" u1="&#xef;" u2="&#x161;"/><hkern k="-85" u1="&#xef;" u2="&#xef;"/><hkern k="-67" u1="&#xef;" u2="&#xee;"/><hkern k="-76" u1="&#xef;" u2="&#xec;"/><hkern k="-9" u1="&#xef;" u2="&#xe3;"/><hkern k="-45" u1="&#xef;" u2="&#x7d;"/><hkern k="-18" u1="&#xef;" u2="&#x7c;"/><hkern k="-18" u1="&#xef;" u2="j"/><hkern k="-18" u1="&#xef;" u2="i"/><hkern k="-45" u1="&#xef;" u2="]"/><hkern k="-46" u1="&#xef;" u2="\"/><hkern k="-48" u1="&#xef;" u2="&#x3f;"/><hkern k="-39" u1="&#xef;" u2="&#x2a;"/><hkern k="-27" u1="&#xef;" u2="&#x29;"/><hkern k="-4" u1="&#xef;" u2="&#x27;"/><hkern k="9" u1="&#xef;" u2="&#x26;"/><hkern k="-4" u1="&#xef;" u2="&#x22;"/><hkern k="-9" u1="&#xef;" u2="&#x21;"/><hkern k="22" u1="&#xf0;" u2="&#x201d;"/><hkern k="22" u1="&#xf0;" u2="&#x201c;"/><hkern k="22" u1="&#xf0;" u2="&#x2019;"/><hkern k="22" u1="&#xf0;" u2="&#x2018;"/><hkern k="58" u1="&#xf0;" u2="\"/><hkern k="82" u1="&#xf3;" u2="\"/><hkern k="82" u1="&#xf5;" u2="\"/><hkern k="87" u1="&#xf6;" u2="\"/><hkern g2="one.ss05" k="23" u1="&#xf7;"/><hkern k="9" u1="&#xf7;" u2="x"/><hkern k="9" u1="&#xf7;" u2="v"/><hkern k="28" u1="&#xf7;" u2="X"/><hkern k="22" u1="&#xf7;" u2="V"/><hkern k="18" u1="&#xf7;" u2="&#x37;"/><hkern k="1" u1="&#xf7;" u2="&#x34;"/><hkern k="11" u1="&#xf7;" u2="&#x33;"/><hkern k="28" u1="&#xf7;" u2="&#x31;"/><hkern g2="registered.ss06" k="-29" u1="&#x142;"/><hkern g2="ampersand.ss04" k="9" u1="&#x142;"/><hkern k="18" u1="&#x142;" u2="&#x2089;"/><hkern k="18" u1="&#x142;" u2="&#x2088;"/><hkern k="18" u1="&#x142;" u2="&#x2087;"/><hkern k="18" u1="&#x142;" u2="&#x2086;"/><hkern k="18" u1="&#x142;" u2="&#x2085;"/><hkern k="20" u1="&#x142;" u2="&#x2084;"/><hkern k="18" u1="&#x142;" u2="&#x2083;"/><hkern k="18" u1="&#x142;" u2="&#x2082;"/><hkern k="18" u1="&#x142;" u2="&#x2081;"/><hkern k="18" u1="&#x142;" u2="&#x2080;"/><hkern k="-18" u1="&#x142;" u2="&#x2079;"/><hkern k="-18" u1="&#x142;" u2="&#x2078;"/><hkern k="-18" u1="&#x142;" u2="&#x2077;"/><hkern k="-18" u1="&#x142;" u2="&#x2076;"/><hkern k="-18" u1="&#x142;" u2="&#x2075;"/><hkern k="-18" u1="&#x142;" u2="&#x2074;"/><hkern k="-18" u1="&#x142;" u2="&#x2070;"/><hkern k="-29" u1="&#x142;" u2="&#x2021;"/><hkern k="-27" u1="&#x142;" u2="&#x2020;"/><hkern k="9" u1="&#x142;" u2="&#x192;"/><hkern k="-1" u1="&#x142;" u2="&#x142;"/><hkern k="9" u1="&#x142;" u2="&#xbf;"/><hkern k="-9" u1="&#x142;" u2="&#xba;"/><hkern k="-18" u1="&#x142;" u2="&#xb9;"/><hkern k="-1" u1="&#x142;" u2="&#xb7;"/><hkern k="-2" u1="&#x142;" u2="&#xb6;"/><hkern k="-18" u1="&#x142;" u2="&#xb3;"/><hkern k="-18" u1="&#x142;" u2="&#xb2;"/><hkern k="-25" u1="&#x142;" u2="x"/><hkern k="-12" u1="&#x142;" u2="v"/><hkern k="-3" u1="&#x142;" u2="&#x40;"/><hkern k="-9" u1="&#x142;" u2="&#x3f;"/><hkern k="-36" u1="&#x142;" u2="&#x3e;"/><hkern k="-18" u1="&#x142;" u2="&#x3d;"/><hkern k="-1" u1="&#x142;" u2="&#x2f;"/><hkern k="-18" u1="&#x142;" u2="&#x2a;"/><hkern k="18" u1="&#x142;" u2="&#x26;"/><hkern k="-14" u1="&#x152;" u2="&#xef;"/><hkern k="-18" u1="&#x152;" u2="&#xee;"/><hkern k="-15" u1="&#x152;" u2="&#xec;"/><hkern k="-9" u1="&#x160;" u2="&#xef;"/><hkern k="-9" u1="&#x160;" u2="&#xee;"/><hkern k="33" u1="&#x161;" u2="\"/><hkern g2="aring.ss02" k="83" u1="&#x178;"/><hkern g2="adieresis.ss02" k="82" u1="&#x178;"/><hkern g2="atilde.ss02" k="83" u1="&#x178;"/><hkern g2="agrave.ss02" k="82" u1="&#x178;"/><hkern k="36" u1="&#x178;" u2="&#x161;"/><hkern k="59" u1="&#x178;" u2="&#xf6;"/><hkern k="103" u1="&#x178;" u2="&#xf5;"/><hkern k="84" u1="&#x178;" u2="&#xf2;"/><hkern k="102" u1="&#x178;" u2="&#xf0;"/><hkern k="-37" u1="&#x178;" u2="&#xef;"/><hkern k="-9" u1="&#x178;" u2="&#xee;"/><hkern k="12" u1="&#x178;" u2="&#xed;"/><hkern k="-40" u1="&#x178;" u2="&#xec;"/><hkern k="81" u1="&#x178;" u2="&#xeb;"/><hkern k="81" u1="&#x178;" u2="&#xe8;"/><hkern k="56" u1="&#x178;" u2="&#xe4;"/><hkern k="70" u1="&#x178;" u2="&#xe2;"/><hkern k="79" u1="&#x178;" u2="&#xe0;"/><hkern k="-28" u1="&#x17d;" u2="&#xef;"/><hkern k="-28" u1="&#x17d;" u2="&#xee;"/><hkern k="-29" u1="&#x17d;" u2="&#xec;"/><hkern k="22" u1="&#x17e;" u2="\"/><hkern g2="registered.ss06" k="-38" u1="&#x192;"/><hkern g2="ampersand.ss04" k="1" u1="&#x192;"/><hkern g2="s.ordn" k="-27" u1="&#x192;"/><hkern g2="r.ordn" k="-27" u1="&#x192;"/><hkern g2="q.ordn" k="-27" u1="&#x192;"/><hkern g2="p.ordn" k="-27" u1="&#x192;"/><hkern g2="o.ordn" k="-27" u1="&#x192;"/><hkern g2="n.ordn" k="-27" u1="&#x192;"/><hkern g2="m.ordn" k="-27" u1="&#x192;"/><hkern g2="l.ordn" k="-27" u1="&#x192;"/><hkern g2="k.ordn" k="-27" u1="&#x192;"/><hkern g2="i.ordn" k="-27" u1="&#x192;"/><hkern g2="h.ordn" k="-27" u1="&#x192;"/><hkern g2="g.ordn" k="-27" u1="&#x192;"/><hkern g2="f.ordn" k="-27" u1="&#x192;"/><hkern g2="e.ordn" k="-27" u1="&#x192;"/><hkern g2="d.ordn" k="-27" u1="&#x192;"/><hkern g2="c.ordn" k="-27" u1="&#x192;"/><hkern g2="b.ordn" k="-27" u1="&#x192;"/><hkern g2="a.ordn" k="-27" u1="&#x192;"/><hkern k="-18" u1="&#x192;" u2="&#x2122;"/><hkern k="8" u1="&#x192;" u2="&#x2089;"/><hkern k="9" u1="&#x192;" u2="&#x2088;"/><hkern k="6" u1="&#x192;" u2="&#x2087;"/><hkern k="8" u1="&#x192;" u2="&#x2086;"/><hkern k="5" u1="&#x192;" u2="&#x2085;"/><hkern k="10" u1="&#x192;" u2="&#x2084;"/><hkern k="7" u1="&#x192;" u2="&#x2083;"/><hkern k="6" u1="&#x192;" u2="&#x2082;"/><hkern k="4" u1="&#x192;" u2="&#x2081;"/><hkern k="8" u1="&#x192;" u2="&#x2080;"/><hkern k="-1" u1="&#x192;" u2="&#x2079;"/><hkern k="-1" u1="&#x192;" u2="&#x2078;"/><hkern k="-2" u1="&#x192;" u2="&#x2077;"/><hkern k="-1" u1="&#x192;" u2="&#x2075;"/><hkern k="-2" u1="&#x192;" u2="&#x2021;"/><hkern k="-2" u1="&#x192;" u2="&#x2020;"/><hkern k="72" u1="&#x192;" u2="&#x192;"/><hkern k="21" u1="&#x192;" u2="&#xf7;"/><hkern k="-63" u1="&#x192;" u2="&#xef;"/><hkern k="-45" u1="&#x192;" u2="&#xee;"/><hkern k="-63" u1="&#x192;" u2="&#xec;"/><hkern k="-9" u1="&#x192;" u2="&#xd7;"/><hkern k="-9" u1="&#x192;" u2="&#xba;"/><hkern k="-3" u1="&#x192;" u2="&#xb9;"/><hkern k="-17" u1="&#x192;" u2="&#xb6;"/><hkern k="-1" u1="&#x192;" u2="&#xb3;"/><hkern k="-1" u1="&#x192;" u2="&#xb2;"/><hkern k="-9" u1="&#x192;" u2="&#xaa;"/><hkern k="-18" u1="&#x192;" u2="&#xa6;"/><hkern k="29" u1="&#x192;" u2="&#x7e;"/><hkern k="1" u1="&#x192;" u2="x"/><hkern k="1" u1="&#x192;" u2="v"/><hkern k="7" u1="&#x192;" u2="_"/><hkern k="-40" u1="&#x192;" u2="\"/><hkern k="2" u1="&#x192;" u2="&#x40;"/><hkern k="-18" u1="&#x192;" u2="&#x3f;"/><hkern k="-40" u1="&#x192;" u2="&#x3e;"/><hkern k="10" u1="&#x192;" u2="&#x3d;"/><hkern k="38" u1="&#x192;" u2="&#x3c;"/><hkern k="-27" u1="&#x192;" u2="&#x37;"/><hkern k="76" u1="&#x192;" u2="&#x2f;"/><hkern k="38" u1="&#x192;" u2="&#x2b;"/><hkern k="-30" u1="&#x192;" u2="&#x2a;"/><hkern k="-10" u1="&#x192;" u2="&#x29;"/><hkern k="29" u1="&#x192;" u2="&#x26;"/><hkern k="45" u1="&#x2018;" u2="&#xf0;"/><hkern k="-48" u1="&#x2018;" u2="&#xef;"/><hkern k="-39" u1="&#x2018;" u2="&#xee;"/><hkern k="-40" u1="&#x2018;" u2="&#xec;"/><hkern k="87" u1="&#x2019;" u2="&#xf0;"/><hkern k="-3" u1="&#x2019;" u2="&#xef;"/><hkern k="-4" u1="&#x2019;" u2="&#xee;"/><hkern k="-3" u1="&#x2019;" u2="&#xec;"/><hkern k="-10" u1="&#x201a;" u2="g"/><hkern k="45" u1="&#x201c;" u2="&#xf0;"/><hkern k="-48" u1="&#x201c;" u2="&#xef;"/><hkern k="-39" u1="&#x201c;" u2="&#xee;"/><hkern k="-40" u1="&#x201c;" u2="&#xec;"/><hkern k="87" u1="&#x201d;" u2="&#xf0;"/><hkern k="-3" u1="&#x201d;" u2="&#xef;"/><hkern k="-4" u1="&#x201d;" u2="&#xee;"/><hkern k="-3" u1="&#x201d;" u2="&#xec;"/><hkern k="-10" u1="&#x201e;" u2="g"/><hkern g2="one.ss05" k="-9" u1="&#x2020;"/><hkern k="-19" u1="&#x2020;" u2="x"/><hkern k="-30" u1="&#x2020;" u2="v"/><hkern k="-11" u1="&#x2020;" u2="V"/><hkern g2="one.ss05" k="2" u1="&#x2021;"/><hkern k="-9" u1="&#x2021;" u2="&#x142;"/><hkern k="-18" u1="&#x2021;" u2="&#x141;"/><hkern k="-19" u1="&#x2021;" u2="x"/><hkern k="-30" u1="&#x2021;" u2="v"/><hkern k="-10" u1="&#x2021;" u2="V"/><hkern g2="one.ss05" k="33" u1="&#x2022;"/><hkern k="9" u1="&#x2022;" u2="x"/><hkern k="48" u1="&#x2022;" u2="X"/><hkern k="49" u1="&#x2022;" u2="V"/><hkern k="19" u1="&#x2026;" u2="g"/><hkern g2="nine.dnom" k="239" u1="&#x2044;"/><hkern g2="eight.dnom" k="239" u1="&#x2044;"/><hkern g2="seven.dnom" k="191" u1="&#x2044;"/><hkern g2="six.dnom" k="259" u1="&#x2044;"/><hkern g2="five.dnom" k="235" u1="&#x2044;"/><hkern g2="four.dnom" k="289" u1="&#x2044;"/><hkern g2="three.dnom" k="215" u1="&#x2044;"/><hkern g2="two.dnom" k="240" u1="&#x2044;"/><hkern g2="one.dnom" k="209" u1="&#x2044;"/><hkern g2="zero.dnom" k="249" u1="&#x2044;"/><hkern k="75" u1="&#x2080;" u2="v"/><hkern k="109" u1="&#x2080;" u2="V"/><hkern k="57" u1="&#x2081;" u2="v"/><hkern k="87" u1="&#x2081;" u2="V"/><hkern k="57" u1="&#x2082;" u2="v"/><hkern k="89" u1="&#x2082;" u2="V"/><hkern k="57" u1="&#x2083;" u2="v"/><hkern k="89" u1="&#x2083;" u2="V"/><hkern k="75" u1="&#x2084;" u2="v"/><hkern k="117" u1="&#x2084;" u2="V"/><hkern k="57" u1="&#x2085;" u2="v"/><hkern k="99" u1="&#x2085;" u2="V"/><hkern k="66" u1="&#x2086;" u2="v"/><hkern k="99" u1="&#x2086;" u2="V"/><hkern k="2" u1="&#x2087;" u2="x"/><hkern k="48" u1="&#x2087;" u2="v"/><hkern k="18" u1="&#x2087;" u2="X"/><hkern k="99" u1="&#x2087;" u2="V"/><hkern k="58" u1="&#x2088;" u2="v"/><hkern k="9" u1="&#x2088;" u2="X"/><hkern k="118" u1="&#x2088;" u2="V"/><hkern k="75" u1="&#x2089;" u2="v"/><hkern k="18" u1="&#x2089;" u2="X"/><hkern k="118" u1="&#x2089;" u2="V"/><hkern g2="one.ss05" k="-9" u1="&#x20ac;"/><hkern k="-28" u1="&#x20ac;" u2="&#x37;"/><hkern k="2" u1="&#x20ac;" u2="&#x34;"/><hkern k="-9" u1="&#x20ac;" u2="&#x33;"/><hkern k="-9" u1="&#x20ac;" u2="&#x32;"/><hkern k="-1" u1="&#x2113;" u2="&#x39;"/><hkern k="-1" u1="&#x2113;" u2="&#x38;"/><hkern k="-4" u1="&#x2113;" u2="&#x37;"/><hkern g2="one.ss05" k="1" u1="&#x2122;"/><hkern g2="one.ss05" k="2" u1="&#x2202;"/><hkern g2="one.ss05" k="-11" u1="&#x2206;"/><hkern g2="one.ss05" k="2" u1="&#x220f;"/><hkern g2="one.ss05" k="-9" u1="&#x2211;"/><hkern g2="one.ss05" k="4" u1="&#x2212;"/><hkern k="9" u1="&#x2212;" u2="&#x37;"/><hkern k="2" u1="&#x2212;" u2="&#x33;"/><hkern k="1" u1="&#x2212;" u2="&#x32;"/><hkern k="18" u1="&#x2212;" u2="&#x31;"/><hkern k="40" u1="&#x2212;" u2="&#x29;"/><hkern k="9" u1="&#x2215;" u2="&#x39;"/><hkern k="18" u1="&#x2215;" u2="&#x38;"/><hkern k="-40" u1="&#x2215;" u2="&#x37;"/><hkern k="20" u1="&#x2215;" u2="&#x35;"/><hkern k="59" u1="&#x2215;" u2="&#x34;"/><hkern k="-9" u1="&#x2215;" u2="&#x33;"/><hkern k="18" u1="&#x2215;" u2="&#x32;"/><hkern k="18" u1="&#x2215;" u2="&#x31;"/><hkern g2="one.ss05" k="119" u1="&#x221a;"/><hkern k="76" u1="&#x221a;" u2="&#x39;"/><hkern k="76" u1="&#x221a;" u2="&#x38;"/><hkern k="36" u1="&#x221a;" u2="&#x37;"/><hkern k="77" u1="&#x221a;" u2="&#x35;"/><hkern k="91" u1="&#x221a;" u2="&#x34;"/><hkern k="31" u1="&#x221a;" u2="&#x33;"/><hkern k="68" u1="&#x221a;" u2="&#x32;"/><hkern k="75" u1="&#x221a;" u2="&#x31;"/><hkern g2="one.ss05" k="33" u1="&#x221e;"/><hkern k="19" u1="&#x221e;" u2="&#x37;"/><hkern k="9" u1="&#x221e;" u2="&#x33;"/><hkern k="9" u1="&#x221e;" u2="&#x32;"/><hkern k="36" u1="&#x221e;" u2="&#x31;"/><hkern k="-20" u1="&#x222b;" u2="&#x39;"/><hkern k="-10" u1="&#x222b;" u2="&#x38;"/><hkern k="-72" u1="&#x222b;" u2="&#x37;"/><hkern k="-2" u1="&#x222b;" u2="&#x35;"/><hkern k="-49" u1="&#x222b;" u2="&#x33;"/><hkern k="-22" u1="&#x222b;" u2="&#x32;"/><hkern k="-13" u1="&#x222b;" u2="&#x31;"/><hkern g2="one.ss05" k="4" u1="&#x2248;"/><hkern k="-2" u1="&#x2248;" u2="&#x38;"/><hkern k="2" u1="&#x2248;" u2="&#x37;"/><hkern k="-1" u1="&#x2248;" u2="&#x34;"/><hkern k="18" u1="&#x2248;" u2="&#x31;"/><hkern k="-1" u1="&#x2260;" u2="&#x38;"/><hkern k="18" u1="&#x2260;" u2="&#x31;"/><hkern g2="one.ss05" k="2" u1="&#x2264;"/><hkern k="9" u1="&#x2264;" u2="&#x31;"/><hkern g2="one.ss05" k="3" u1="&#x2265;"/><hkern k="19" u1="&#x2265;" u2="&#x31;"/><hkern k="-52" u1="&#xfb00;" u2="&#xef;"/><hkern k="-47" u1="&#xfb00;" u2="&#xee;"/><hkern k="-57" u1="&#xfb00;" u2="&#xec;"/><hkern k="-12" u1="&#xfb00;" u2="j"/><hkern k="-9" u1="&#xfb00;" u2="i"/><hkern g1="zero.numr" g2="eight.numr" k="5"/><hkern g1="zero.numr" g2="seven.numr" k="9"/><hkern g1="zero.numr" g2="six.numr" k="1"/><hkern g1="zero.numr" g2="five.numr" k="1"/><hkern g1="zero.numr" g2="three.numr" k="1"/><hkern g1="zero.numr" g2="one.numr" k="1"/><hkern g1="zero.numr" g2="zero.numr" k="1"/><hkern g1="zero.numr" k="249" u2="&#x2044;"/><hkern g1="one.numr" g2="seven.numr" k="-1"/><hkern g1="one.numr" g2="six.numr" k="-1"/><hkern g1="one.numr" g2="two.numr" k="-1"/><hkern g1="one.numr" g2="zero.numr" k="-1"/><hkern g1="one.numr" k="204" u2="&#x2044;"/><hkern g1="two.numr" g2="four.numr" k="1"/><hkern g1="two.numr" k="200" u2="&#x2044;"/><hkern g1="three.numr" g2="seven.numr" k="-1"/><hkern g1="three.numr" g2="one.numr" k="-4"/><hkern g1="three.numr" k="239" u2="&#x2044;"/><hkern g1="four.numr" g2="three.numr" k="5"/><hkern g1="four.numr" g2="two.numr" k="5"/><hkern g1="four.numr" k="230" u2="&#x2044;"/><hkern g1="five.numr" g2="seven.numr" k="-1"/><hkern g1="five.numr" k="241" u2="&#x2044;"/><hkern g1="six.numr" g2="seven.numr" k="-1"/><hkern g1="six.numr" g2="three.numr" k="-9"/><hkern g1="six.numr" k="230" u2="&#x2044;"/><hkern g1="seven.numr" g2="nine.numr" k="-1"/><hkern g1="seven.numr" g2="seven.numr" k="-3"/><hkern g1="seven.numr" g2="six.numr" k="10"/><hkern g1="seven.numr" g2="five.numr" k="5"/><hkern g1="seven.numr" g2="four.numr" k="22"/><hkern g1="seven.numr" g2="one.numr" k="-1"/><hkern g1="seven.numr" g2="zero.numr" k="15"/><hkern g1="seven.numr" k="289" u2="&#x2044;"/><hkern g1="seven.numr" k="18" u2="x"/><hkern g1="eight.numr" g2="seven.numr" k="5"/><hkern g1="eight.numr" g2="six.numr" k="1"/><hkern g1="eight.numr" g2="three.numr" k="9"/><hkern g1="eight.numr" g2="zero.numr" k="1"/><hkern g1="eight.numr" k="243" u2="&#x2044;"/><hkern g1="nine.numr" g2="eight.numr" k="1"/><hkern g1="nine.numr" g2="six.numr" k="1"/><hkern g1="nine.numr" g2="five.numr" k="1"/><hkern g1="nine.numr" g2="three.numr" k="1"/><hkern g1="nine.numr" g2="two.numr" k="5"/><hkern g1="nine.numr" g2="one.numr" k="6"/><hkern g1="nine.numr" g2="zero.numr" k="1"/><hkern g1="nine.numr" k="268" u2="&#x2044;"/><hkern g1="zero.dnom" g2="eight.dnom" k="5"/><hkern g1="zero.dnom" g2="seven.dnom" k="9"/><hkern g1="zero.dnom" g2="six.dnom" k="1"/><hkern g1="zero.dnom" g2="five.dnom" k="1"/><hkern g1="zero.dnom" g2="three.dnom" k="1"/><hkern g1="zero.dnom" g2="one.dnom" k="1"/><hkern g1="zero.dnom" g2="zero.dnom" k="1"/><hkern g1="one.dnom" g2="seven.dnom" k="-1"/><hkern g1="one.dnom" g2="six.dnom" k="-1"/><hkern g1="one.dnom" g2="two.dnom" k="-1"/><hkern g1="one.dnom" g2="zero.dnom" k="-1"/><hkern g1="two.dnom" g2="four.dnom" k="1"/><hkern g1="three.dnom" g2="seven.dnom" k="-1"/><hkern g1="three.dnom" g2="one.dnom" k="-4"/><hkern g1="four.dnom" g2="three.dnom" k="5"/><hkern g1="four.dnom" g2="two.dnom" k="5"/><hkern g1="five.dnom" g2="seven.dnom" k="-1"/><hkern g1="six.dnom" g2="seven.dnom" k="-1"/><hkern g1="six.dnom" g2="three.dnom" k="-9"/><hkern g1="seven.dnom" g2="nine.dnom" k="-1"/><hkern g1="seven.dnom" g2="seven.dnom" k="-3"/><hkern g1="seven.dnom" g2="six.dnom" k="10"/><hkern g1="seven.dnom" g2="five.dnom" k="5"/><hkern g1="seven.dnom" g2="four.dnom" k="22"/><hkern g1="seven.dnom" g2="one.dnom" k="-1"/><hkern g1="seven.dnom" g2="zero.dnom" k="15"/><hkern g1="eight.dnom" g2="seven.dnom" k="5"/><hkern g1="eight.dnom" g2="six.dnom" k="1"/><hkern g1="eight.dnom" g2="three.dnom" k="9"/><hkern g1="eight.dnom" g2="zero.dnom" k="1"/><hkern g1="nine.dnom" g2="eight.dnom" k="1"/><hkern g1="nine.dnom" g2="six.dnom" k="1"/><hkern g1="nine.dnom" g2="five.dnom" k="1"/><hkern g1="nine.dnom" g2="three.dnom" k="1"/><hkern g1="nine.dnom" g2="two.dnom" k="5"/><hkern g1="nine.dnom" g2="one.dnom" k="6"/><hkern g1="nine.dnom" g2="zero.dnom" k="1"/><hkern g1="a.ordn" g2="u.ordn" k="1"/><hkern g1="b.ordn" g2="z.ordn" k="1"/><hkern g1="b.ordn" g2="y.ordn" k="2"/><hkern g1="b.ordn" g2="x.ordn" k="1"/><hkern g1="b.ordn" g2="w.ordn" k="1"/><hkern g1="b.ordn" g2="v.ordn" k="2"/><hkern g1="b.ordn" g2="u.ordn" k="1"/><hkern g1="b.ordn" g2="t.ordn" k="1"/><hkern g1="c.ordn" g2="y.ordn" k="1"/><hkern g1="c.ordn" g2="x.ordn" k="1"/><hkern g1="c.ordn" g2="w.ordn" k="1"/><hkern g1="c.ordn" g2="v.ordn" k="1"/><hkern g1="c.ordn" g2="u.ordn" k="1"/><hkern g1="c.ordn" g2="q.ordn" k="1"/><hkern g1="c.ordn" g2="o.ordn" k="1"/><hkern g1="c.ordn" g2="g.ordn" k="1"/><hkern g1="c.ordn" g2="e.ordn" k="1"/><hkern g1="c.ordn" g2="d.ordn" k="1"/><hkern g1="c.ordn" g2="c.ordn" k="1"/><hkern g1="c.ordn" g2="a.ordn" k="1"/><hkern g1="e.ordn" g2="y.ordn" k="1"/><hkern g1="e.ordn" g2="x.ordn" k="1"/><hkern g1="e.ordn" g2="w.ordn" k="1"/><hkern g1="e.ordn" g2="v.ordn" k="1"/><hkern g1="e.ordn" g2="u.ordn" k="2"/><hkern g1="f.ordn" g2="z.ordn" k="-1"/><hkern g1="f.ordn" g2="y.ordn" k="-2"/><hkern g1="f.ordn" g2="x.ordn" k="-1"/><hkern g1="f.ordn" g2="w.ordn" k="-1"/><hkern g1="f.ordn" g2="v.ordn" k="-1"/><hkern g1="f.ordn" g2="u.ordn" k="1"/><hkern g1="f.ordn" g2="q.ordn" k="2"/><hkern g1="f.ordn" g2="o.ordn" k="2"/><hkern g1="f.ordn" g2="j.ordn" k="-2"/><hkern g1="f.ordn" g2="g.ordn" k="2"/><hkern g1="f.ordn" g2="f.ordn" k="1"/><hkern g1="f.ordn" g2="e.ordn" k="2"/><hkern g1="f.ordn" g2="d.ordn" k="2"/><hkern g1="f.ordn" g2="c.ordn" k="2"/><hkern g1="f.ordn" g2="a.ordn" k="2"/><hkern g1="g.ordn" g2="y.ordn" k="1"/><hkern g1="g.ordn" g2="w.ordn" k="1"/><hkern g1="g.ordn" g2="u.ordn" k="1"/><hkern g1="g.ordn" g2="a.ordn" k="1"/><hkern g1="h.ordn" g2="u.ordn" k="1"/><hkern g1="k.ordn" g2="z.ordn" k="-1"/><hkern g1="k.ordn" g2="u.ordn" k="1"/><hkern g1="k.ordn" g2="q.ordn" k="2"/><hkern g1="k.ordn" g2="o.ordn" k="2"/><hkern g1="k.ordn" g2="g.ordn" k="2"/><hkern g1="k.ordn" g2="e.ordn" k="2"/><hkern g1="k.ordn" g2="d.ordn" k="2"/><hkern g1="k.ordn" g2="c.ordn" k="2"/><hkern g1="k.ordn" g2="a.ordn" k="1"/><hkern g1="m.ordn" g2="u.ordn" k="1"/><hkern g1="n.ordn" g2="u.ordn" k="1"/><hkern g1="o.ordn" g2="z.ordn" k="1"/><hkern g1="o.ordn" g2="y.ordn" k="2"/><hkern g1="o.ordn" g2="x.ordn" k="1"/><hkern g1="o.ordn" g2="w.ordn" k="1"/><hkern g1="o.ordn" g2="v.ordn" k="2"/><hkern g1="o.ordn" g2="u.ordn" k="1"/><hkern g1="o.ordn" g2="t.ordn" k="1"/><hkern g1="p.ordn" g2="z.ordn" k="1"/><hkern g1="p.ordn" g2="y.ordn" k="2"/><hkern g1="p.ordn" g2="x.ordn" k="1"/><hkern g1="p.ordn" g2="w.ordn" k="1"/><hkern g1="p.ordn" g2="v.ordn" k="2"/><hkern g1="p.ordn" g2="u.ordn" k="1"/><hkern g1="p.ordn" g2="t.ordn" k="1"/><hkern g1="r.ordn" g2="z.ordn" k="-1"/><hkern g1="r.ordn" g2="y.ordn" k="-2"/><hkern g1="r.ordn" g2="x.ordn" k="-1"/><hkern g1="r.ordn" g2="w.ordn" k="-1"/><hkern g1="r.ordn" g2="v.ordn" k="-2"/><hkern g1="r.ordn" g2="t.ordn" k="-1"/><hkern g1="r.ordn" g2="q.ordn" k="1"/><hkern g1="r.ordn" g2="o.ordn" k="1"/><hkern g1="r.ordn" g2="g.ordn" k="1"/><hkern g1="r.ordn" g2="f.ordn" k="-2"/><hkern g1="r.ordn" g2="e.ordn" k="1"/><hkern g1="r.ordn" g2="d.ordn" k="1"/><hkern g1="r.ordn" g2="c.ordn" k="1"/><hkern g1="r.ordn" k="-67" u2="&#x37;"/><hkern g1="s.ordn" g2="y.ordn" k="1"/><hkern g1="s.ordn" g2="x.ordn" k="1"/><hkern g1="s.ordn" g2="w.ordn" k="1"/><hkern g1="s.ordn" g2="v.ordn" k="1"/><hkern g1="s.ordn" g2="u.ordn" k="1"/><hkern g1="s.ordn" g2="q.ordn" k="1"/><hkern g1="s.ordn" g2="o.ordn" k="1"/><hkern g1="s.ordn" g2="g.ordn" k="1"/><hkern g1="s.ordn" g2="e.ordn" k="1"/><hkern g1="s.ordn" g2="d.ordn" k="1"/><hkern g1="s.ordn" g2="c.ordn" k="1"/><hkern g1="s.ordn" g2="a.ordn" k="1"/><hkern g1="t.ordn" g2="z.ordn" k="-2"/><hkern g1="t.ordn" g2="y.ordn" k="-1"/><hkern g1="t.ordn" g2="x.ordn" k="-2"/><hkern g1="t.ordn" g2="w.ordn" k="-1"/><hkern g1="t.ordn" g2="v.ordn" k="-1"/><hkern g1="t.ordn" g2="s.ordn" k="-1"/><hkern g1="u.ordn" g2="u.ordn" k="1"/><hkern g1="u.ordn" g2="q.ordn" k="1"/><hkern g1="u.ordn" g2="o.ordn" k="1"/><hkern g1="u.ordn" g2="g.ordn" k="1"/><hkern g1="u.ordn" g2="e.ordn" k="1"/><hkern g1="u.ordn" g2="d.ordn" k="1"/><hkern g1="u.ordn" g2="c.ordn" k="1"/><hkern g1="v.ordn" g2="z.ordn" k="-1"/><hkern g1="v.ordn" g2="y.ordn" k="-1"/><hkern g1="v.ordn" g2="x.ordn" k="-1"/><hkern g1="v.ordn" g2="w.ordn" k="-1"/><hkern g1="v.ordn" g2="v.ordn" k="-1"/><hkern g1="v.ordn" g2="t.ordn" k="-1"/><hkern g1="v.ordn" g2="q.ordn" k="2"/><hkern g1="v.ordn" g2="o.ordn" k="2"/><hkern g1="v.ordn" g2="g.ordn" k="2"/><hkern g1="v.ordn" g2="f.ordn" k="-2"/><hkern g1="v.ordn" g2="e.ordn" k="2"/><hkern g1="v.ordn" g2="d.ordn" k="2"/><hkern g1="v.ordn" g2="c.ordn" k="2"/><hkern g1="v.ordn" g2="a.ordn" k="1"/><hkern g1="w.ordn" g2="z.ordn" k="-1"/><hkern g1="w.ordn" g2="y.ordn" k="-1"/><hkern g1="w.ordn" g2="x.ordn" k="-1"/><hkern g1="w.ordn" g2="v.ordn" k="-1"/><hkern g1="w.ordn" g2="t.ordn" k="-1"/><hkern g1="w.ordn" g2="q.ordn" k="1"/><hkern g1="w.ordn" g2="o.ordn" k="1"/><hkern g1="w.ordn" g2="g.ordn" k="1"/><hkern g1="w.ordn" g2="f.ordn" k="-2"/><hkern g1="w.ordn" g2="e.ordn" k="1"/><hkern g1="w.ordn" g2="d.ordn" k="1"/><hkern g1="w.ordn" g2="c.ordn" k="1"/><hkern g1="w.ordn" g2="a.ordn" k="1"/><hkern g1="x.ordn" g2="z.ordn" k="-2"/><hkern g1="x.ordn" g2="y.ordn" k="-1"/><hkern g1="x.ordn" g2="x.ordn" k="-1"/><hkern g1="x.ordn" g2="w.ordn" k="-1"/><hkern g1="x.ordn" g2="v.ordn" k="-1"/><hkern g1="x.ordn" g2="u.ordn" k="1"/><hkern g1="x.ordn" g2="t.ordn" k="-1"/><hkern g1="x.ordn" g2="q.ordn" k="1"/><hkern g1="x.ordn" g2="o.ordn" k="1"/><hkern g1="x.ordn" g2="g.ordn" k="1"/><hkern g1="x.ordn" g2="f.ordn" k="-2"/><hkern g1="x.ordn" g2="e.ordn" k="1"/><hkern g1="x.ordn" g2="d.ordn" k="1"/><hkern g1="x.ordn" g2="c.ordn" k="1"/><hkern g1="y.ordn" g2="y.ordn" k="-1"/><hkern g1="y.ordn" g2="v.ordn" k="-1"/><hkern g1="y.ordn" g2="u.ordn" k="1"/><hkern g1="y.ordn" g2="t.ordn" k="-1"/><hkern g1="y.ordn" g2="s.ordn" k="1"/><hkern g1="y.ordn" g2="q.ordn" k="2"/><hkern g1="y.ordn" g2="o.ordn" k="2"/><hkern g1="y.ordn" g2="g.ordn" k="2"/><hkern g1="y.ordn" g2="f.ordn" k="-1"/><hkern g1="y.ordn" g2="e.ordn" k="2"/><hkern g1="y.ordn" g2="d.ordn" k="2"/><hkern g1="y.ordn" g2="c.ordn" k="2"/><hkern g1="y.ordn" g2="a.ordn" k="1"/><hkern g1="z.ordn" g2="x.ordn" k="-1"/><hkern g1="numbersign.tf" k="-27" u2="&#x37;"/><hkern g1="numbersign.tf" k="9" u2="&#x34;"/><hkern g1="numbersign.tf" k="-18" u2="&#x33;"/><hkern g1="numbersign.tf" k="-18" u2="&#x32;"/><hkern g1="numbersign.tf" k="-18" u2="&#x31;"/><hkern g1="florin.tf" k="9" u2="&#x39;"/><hkern g1="florin.tf" k="9" u2="&#x38;"/><hkern g1="florin.tf" k="-41" u2="&#x37;"/><hkern g1="florin.tf" k="29" u2="&#x35;"/><hkern g1="florin.tf" k="60" u2="&#x34;"/><hkern g1="florin.tf" k="-9" u2="&#x33;"/><hkern g1="parenleft.case" g2="one.ss05" k="2"/><hkern g1="parenleft.case" k="-4" u2="&#x192;"/><hkern g1="parenleft.case" k="23" u2="&#x141;"/><hkern g1="parenleft.case" k="-2" u2="X"/><hkern g1="parenleft.case" k="-11" u2="V"/><hkern g1="parenleft.case" k="-1" u2="&#x39;"/><hkern g1="parenleft.case" k="9" u2="&#x38;"/><hkern g1="parenleft.case" k="-29" u2="&#x37;"/><hkern g1="parenleft.case" k="38" u2="&#x36;"/><hkern g1="parenleft.case" k="2" u2="&#x35;"/><hkern g1="parenleft.case" k="30" u2="&#x34;"/><hkern g1="parenleft.case" k="-9" u2="&#x33;"/><hkern g1="parenleft.case" k="-1" u2="&#x32;"/><hkern g1="parenleft.case" k="19" u2="&#x31;"/><hkern g1="braceleft.case" k="-1" u2="&#xe6;"/><hkern g1="braceleft.case" k="-1" u2="&#xe5;"/><hkern g1="braceleft.case" k="-1" u2="&#xe4;"/><hkern g1="braceleft.case" k="-1" u2="&#xe3;"/><hkern g1="braceleft.case" k="-1" u2="&#xe2;"/><hkern g1="braceleft.case" k="-1" u2="&#xe1;"/><hkern g1="braceleft.case" k="-1" u2="&#xe0;"/><hkern g1="braceleft.case" k="-1" u2="a"/><hkern g1="exclamdown.case" k="-1" u2="V"/><hkern g1="exclamdown.case" k="-29" u2="&#x37;"/><hkern g1="exclamdown.case" k="-18" u2="&#x33;"/><hkern g1="questiondown.case" g2="one.ss05" k="1"/><hkern g1="questiondown.case" k="-18" u2="&#x142;"/><hkern g1="questiondown.case" k="-23" u2="&#x141;"/><hkern g1="questiondown.case" k="27" u2="v"/><hkern g1="questiondown.case" k="9" u2="X"/><hkern g1="questiondown.case" k="49" u2="V"/><hkern g1="questiondown.case" k="18" u2="&#x37;"/><hkern g1="questiondown.case" k="36" u2="&#x31;"/><hkern g1="G.ss01" k="-14" u2="&#xef;"/><hkern g1="G.ss01" k="-15" u2="&#xee;"/><hkern g1="r.ss03" k="-1" u2="&#x142;"/><hkern g1="ampersand.ss04" g2="one.ss05" k="40"/><hkern g1="ampersand.ss04" k="-20" u2="&#x142;"/><hkern g1="ampersand.ss04" k="-2" u2="&#x141;"/><hkern g1="ampersand.ss04" k="18" u2="&#xdf;"/><hkern g1="ampersand.ss04" k="45" u2="x"/><hkern g1="ampersand.ss04" k="18" u2="v"/><hkern g1="ampersand.ss04" k="64" u2="X"/><hkern g1="ampersand.ss04" k="74" u2="V"/><hkern g1="ampersand.ss04" k="17" u2="&#x39;"/><hkern g1="ampersand.ss04" k="9" u2="&#x38;"/><hkern g1="ampersand.ss04" k="27" u2="&#x37;"/><hkern g1="ampersand.ss04" k="14" u2="&#x35;"/><hkern g1="ampersand.ss04" k="-1" u2="&#x34;"/><hkern g1="ampersand.ss04" k="9" u2="&#x33;"/><hkern g1="ampersand.ss04" k="36" u2="&#x32;"/><hkern g1="ampersand.ss04" k="45" u2="&#x31;"/><hkern g1="ampersand.ss04" k="40" u2=""/><hkern g1="one.ss05" g2="registered.ss06" k="4"/><hkern g1="one.ss05" g2="questiondown.case" k="-2"/><hkern g1="one.ss05" g2="parenright.case" k="-1"/><hkern g1="one.ss05" k="-1" u2="&#x2265;"/><hkern g1="one.ss05" k="18" u2="&#x221e;"/><hkern g1="one.ss05" k="27" u2="&#x221a;"/><hkern g1="one.ss05" k="-22" u2="&#x2206;"/><hkern g1="one.ss05" k="65" u2="&#x2122;"/><hkern g1="one.ss05" k="40" u2="&#x20ac;"/><hkern g1="one.ss05" k="18" u2="&#x2030;"/><hkern g1="one.ss05" k="9" u2="&#x2022;"/><hkern g1="one.ss05" k="-2" u2="&#x2021;"/><hkern g1="one.ss05" k="9" u2="&#x2020;"/><hkern g1="one.ss05" k="-13" u2="&#xbf;"/><hkern g1="one.ss05" k="8" u2="&#xb7;"/><hkern g1="one.ss05" k="27" u2="&#xb0;"/><hkern g1="one.ss05" k="18" u2="&#xac;"/><hkern g1="one.ss05" k="9" u2="&#xa1;"/><hkern g1="one.ss05" k="9" u2="&#x7e;"/><hkern g1="one.ss05" k="-43" u2="_"/><hkern g1="one.ss05" k="18" u2="^"/><hkern g1="one.ss05" k="47" u2="\"/><hkern g1="one.ss05" k="27" u2="&#x40;"/><hkern g1="one.ss05" k="36" u2="&#x3f;"/><hkern g1="one.ss05" k="-30" u2="&#x3e;"/><hkern g1="one.ss05" k="-3" u2="&#x39;"/><hkern g1="one.ss05" k="-1" u2="&#x38;"/><hkern g1="one.ss05" k="-1" u2="&#x37;"/><hkern g1="one.ss05" k="-2" u2="&#x35;"/><hkern g1="one.ss05" k="5" u2="&#x34;"/><hkern g1="one.ss05" k="-16" u2="&#x33;"/><hkern g1="one.ss05" k="-12" u2="&#x32;"/><hkern g1="one.ss05" k="-23" u2="&#x2f;"/><hkern g1="one.ss05" k="9" u2="&#x2b;"/><hkern g1="one.ss05" k="11" u2="&#x2a;"/><hkern g1="one.ss05" k="-3" u2="&#x29;"/><hkern g1="one.ss05" k="9" u2="&#x26;"/><hkern g1="one.ss05" k="9" u2="&#x25;"/><hkern g1="one.ss05" k="1" u2="&#x23;"/><hkern g1="one.ss05" k="9" u2="&#x21;"/><hkern g1="registered.ss06" k="18" u2="X"/><hkern g1="registered.ss06" k="-4" u2="&#x37;"/><hkern g1="registered.ss06" k="2" u2="&#x35;"/><hkern g1="registered.ss06" k="6" u2="&#x34;"/><hkern g1="registered.ss06" k="-1" u2="&#x31;"/><hkern g1="B" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="16"/><hkern g1="B" g2="AE" k="29"/><hkern g1="B" g2="J" k="-20"/><hkern g1="B" g2="T" k="-5"/><hkern g1="B" g2="W" k="9"/><hkern g1="B" g2="Y,Yacute,Ydieresis" k="19"/><hkern g1="B" g2="Z,Zcaron" k="-9"/><hkern g1="B" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="-4"/><hkern g1="B" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="-1"/><hkern g1="B" g2="hyphen.case,endash.case,emdash.case" k="1"/><hkern g1="B" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="1"/><hkern g1="B" g2="guillemotleft,guilsinglleft" k="10"/><hkern g1="B" g2="guillemotleft.case,guilsinglleft.case" k="11"/><hkern g1="B" g2="m,n,p,r,ntilde,r.ss03" k="1"/><hkern g1="B" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="-5"/><hkern g1="B" g2="oslash" k="-4"/><hkern g1="B" g2="comma,period,ellipsis" k="2"/><hkern g1="B" g2="quoteright,quotedblright" k="11"/><hkern g1="B" g2="s,scaron" k="1"/><hkern g1="B" g2="u,ugrave,uacute,ucircumflex,udieresis" k="2"/><hkern g1="B" g2="w" k="1"/><hkern g1="B" g2="y,yacute,ydieresis" k="1"/><hkern g1="Euro" g2="zero,six" k="10"/><hkern g1="F" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="59"/><hkern g1="F" g2="AE" k="136"/><hkern g1="F" g2="J" k="36"/><hkern g1="F" g2="T" k="-39"/><hkern g1="F" g2="W" k="-14"/><hkern g1="F" g2="Y,Yacute,Ydieresis" k="-14"/><hkern g1="F" g2="Z,Zcaron" k="-19"/><hkern g1="F" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="35"/><hkern g1="F" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="31"/><hkern g1="F" g2="hyphen.case,endash.case,emdash.case" k="1"/><hkern g1="F" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="1"/><hkern g1="F" g2="guillemotleft,guilsinglleft" k="20"/><hkern g1="F" g2="guillemotleft.case,guilsinglleft.case" k="19"/><hkern g1="F" g2="m,n,p,r,ntilde,r.ss03" k="39"/><hkern g1="F" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="31"/><hkern g1="F" g2="oslash" k="36"/><hkern g1="F" g2="comma,period,ellipsis" k="109"/><hkern g1="F" g2="quoteright,quotedblright" k="-2"/><hkern g1="F" g2="s,scaron" k="15"/><hkern g1="F" g2="u,ugrave,uacute,ucircumflex,udieresis" k="36"/><hkern g1="F" g2="w" k="16"/><hkern g1="F" g2="y,yacute,ydieresis" k="33"/><hkern g1="F" g2="Eth" k="-1"/><hkern g1="F" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="5"/><hkern g1="F" g2="Oslash" k="5"/><hkern g1="F" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="1"/><hkern g1="F" g2="bracketright,braceright" k="-3"/><hkern g1="F" g2="bracketright.case,braceright.case" k="-2"/><hkern g1="F" g2="colon,semicolon" k="9"/><hkern g1="F" g2="hyphen,uni00AD,endash,emdash" k="19"/><hkern g1="F" g2="guillemotright,guilsinglright" k="10"/><hkern g1="F" g2="guillemotright.case,guilsinglright.case" k="5"/><hkern g1="F" g2="i,igrave,iacute,icircumflex,idieresis,dotlessi" k="1"/><hkern g1="F" g2="j" k="5"/><hkern g1="F" g2="b,h,k,l,thorn" k="-1"/><hkern g1="F" g2="quotedbl,quotesingle" k="-20"/><hkern g1="F" g2="quotesinglbase,quotedblbase" k="77"/><hkern g1="F" g2="quoteleft,quotedblleft" k="-11"/><hkern g1="F" g2="z,zcaron" k="19"/><hkern g1="P" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="56"/><hkern g1="P" g2="AE" k="150"/><hkern g1="P" g2="J" k="61"/><hkern g1="P" g2="W" k="5"/><hkern g1="P" g2="Y,Yacute,Ydieresis" k="19"/><hkern g1="P" g2="Z,Zcaron" k="1"/><hkern g1="P" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="12"/><hkern g1="P" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="31"/><hkern g1="P" g2="hyphen.case,endash.case,emdash.case" k="1"/><hkern g1="P" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-10"/><hkern g1="P" g2="guillemotleft,guilsinglleft" k="21"/><hkern g1="P" g2="guillemotleft.case,guilsinglleft.case" k="11"/><hkern g1="P" g2="m,n,p,r,ntilde,r.ss03" k="11"/><hkern g1="P" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="27"/><hkern g1="P" g2="oslash" k="31"/><hkern g1="P" g2="comma,period,ellipsis" k="147"/><hkern g1="P" g2="quoteright,quotedblright" k="-1"/><hkern g1="P" g2="s,scaron" k="12"/><hkern g1="P" g2="u,ugrave,uacute,ucircumflex,udieresis" k="20"/><hkern g1="P" g2="y,yacute,ydieresis" k="-1"/><hkern g1="P" g2="Eth" k="-1"/><hkern g1="P" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="-4"/><hkern g1="P" g2="bracketright,braceright" k="9"/><hkern g1="P" g2="bracketright.case,braceright.case" k="18"/><hkern g1="P" g2="hyphen,uni00AD,endash,emdash" k="10"/><hkern g1="P" g2="guillemotright,guilsinglright" k="-1"/><hkern g1="P" g2="guillemotright.case,guilsinglright.case" k="-1"/><hkern g1="P" g2="j" k="1"/><hkern g1="P" g2="b,h,k,l,thorn" k="6"/><hkern g1="P" g2="quotedbl,quotesingle" k="-10"/><hkern g1="P" g2="quotesinglbase,quotedblbase" k="77"/><hkern g1="P" g2="quoteleft,quotedblleft" k="-2"/><hkern g1="P" g2="z,zcaron" k="1"/><hkern g1="P" g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn" k="5"/><hkern g1="P" g2="t" k="-10"/><hkern g1="Q" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="2"/><hkern g1="Q" g2="AE" k="2"/><hkern g1="Q" g2="J" k="-1"/><hkern g1="Q" g2="T" k="16"/><hkern g1="Q" g2="W" k="25"/><hkern g1="Q" g2="Y,Yacute,Ydieresis" k="49"/><hkern g1="Q" g2="Z,Zcaron" k="-5"/><hkern g1="Q" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="-9"/><hkern g1="Q" g2="hyphen.case,endash.case,emdash.case" k="-18"/><hkern g1="Q" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-9"/><hkern g1="Q" g2="guillemotleft,guilsinglleft" k="9"/><hkern g1="Q" g2="guillemotleft.case,guilsinglleft.case" k="9"/><hkern g1="Q" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="-5"/><hkern g1="Q" g2="comma,period,ellipsis" k="28"/><hkern g1="Q" g2="quoteright,quotedblright" k="19"/><hkern g1="Q" g2="u,ugrave,uacute,ucircumflex,udieresis" k="5"/><hkern g1="Q" g2="w" k="1"/><hkern g1="Q" g2="y,yacute,ydieresis" k="1"/><hkern g1="Q" g2="Eth" k="-1"/><hkern g1="Q" g2="bracketright,braceright" k="28"/><hkern g1="Q" g2="bracketright.case,braceright.case" k="28"/><hkern g1="Q" g2="guillemotright,guilsinglright" k="11"/><hkern g1="Q" g2="guillemotright.case,guilsinglright.case" k="9"/><hkern g1="Q" g2="j" k="-18"/><hkern g1="Q" g2="quoteleft,quotedblleft" k="18"/><hkern g1="Q" g2="z,zcaron" k="-5"/><hkern g1="Q" g2="t" k="-14"/><hkern g1="Thorn" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="57"/><hkern g1="Thorn" g2="AE" k="94"/><hkern g1="Thorn" g2="J" k="14"/><hkern g1="Thorn" g2="T" k="25"/><hkern g1="Thorn" g2="W" k="20"/><hkern g1="Thorn" g2="Y,Yacute,Ydieresis" k="47"/><hkern g1="Thorn" g2="Z,Zcaron" k="6"/><hkern g1="Thorn" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="-6"/><hkern g1="Thorn" g2="hyphen.case,endash.case,emdash.case" k="-1"/><hkern g1="Thorn" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-11"/><hkern g1="Thorn" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="-6"/><hkern g1="Thorn" g2="oslash" k="1"/><hkern g1="Thorn" g2="comma,period,ellipsis" k="39"/><hkern g1="Thorn" g2="s,scaron" k="-1"/><hkern g1="Thorn" g2="u,ugrave,uacute,ucircumflex,udieresis" k="5"/><hkern g1="Thorn" g2="w" k="-1"/><hkern g1="Thorn" g2="y,yacute,ydieresis" k="-1"/><hkern g1="Thorn" g2="Eth" k="-2"/><hkern g1="Thorn" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-1"/><hkern g1="Thorn" g2="Oslash" k="-1"/><hkern g1="Thorn" g2="bracketright,braceright" k="19"/><hkern g1="Thorn" g2="bracketright.case,braceright.case" k="19"/><hkern g1="Thorn" g2="guillemotright.case,guilsinglright.case" k="8"/><hkern g1="Thorn" g2="quotesinglbase,quotedblbase" k="48"/><hkern g1="Thorn" g2="quoteleft,quotedblleft" k="18"/><hkern g1="Thorn" g2="z,zcaron" k="-2"/><hkern g1="Thorn" g2="t" k="-12"/><hkern g1="V" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="64"/><hkern g1="V" g2="AE" k="144"/><hkern g1="V" g2="J" k="69"/><hkern g1="V" g2="T" k="-20"/><hkern g1="V" g2="W" k="-10"/><hkern g1="V" g2="Y,Yacute,Ydieresis" k="-10"/><hkern g1="V" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="64"/><hkern g1="V" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="50"/><hkern g1="V" g2="hyphen.case,endash.case,emdash.case" k="32"/><hkern g1="V" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="5"/><hkern g1="V" g2="guillemotleft,guilsinglleft" k="69"/><hkern g1="V" g2="guillemotleft.case,guilsinglleft.case" k="58"/><hkern g1="V" g2="m,n,p,r,ntilde,r.ss03" k="57"/><hkern g1="V" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="69"/><hkern g1="V" g2="oslash" k="69"/><hkern g1="V" g2="comma,period,ellipsis" k="127"/><hkern g1="V" g2="quoteright,quotedblright" k="-2"/><hkern g1="V" g2="s,scaron" k="56"/><hkern g1="V" g2="u,ugrave,uacute,ucircumflex,udieresis" k="49"/><hkern g1="V" g2="w" k="19"/><hkern g1="V" g2="y,yacute,ydieresis" k="23"/><hkern g1="V" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="25"/><hkern g1="V" g2="Oslash" k="25"/><hkern g1="V" g2="bracketright,braceright" k="-11"/><hkern g1="V" g2="bracketright.case,braceright.case" k="-11"/><hkern g1="V" g2="colon,semicolon" k="58"/><hkern g1="V" g2="hyphen,uni00AD,endash,emdash" k="69"/><hkern g1="V" g2="guillemotright,guilsinglright" k="31"/><hkern g1="V" g2="guillemotright.case,guilsinglright.case" k="18"/><hkern g1="V" g2="quotedbl,quotesingle" k="-11"/><hkern g1="V" g2="quotesinglbase,quotedblbase" k="116"/><hkern g1="V" g2="quoteleft,quotedblleft" k="-10"/><hkern g1="V" g2="z,zcaron" k="16"/><hkern g1="V" g2="t" k="10"/><hkern g1="V" g2="S,Scaron" k="5"/><hkern g1="V" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="37"/><hkern g1="X" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="27"/><hkern g1="X" g2="AE" k="81"/><hkern g1="X" g2="J" k="37"/><hkern g1="X" g2="T" k="3"/><hkern g1="X" g2="W" k="-10"/><hkern g1="X" g2="Y,Yacute,Ydieresis" k="-10"/><hkern g1="X" g2="Z,Zcaron" k="-1"/><hkern g1="X" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="18"/><hkern g1="X" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="38"/><hkern g1="X" g2="hyphen.case,endash.case,emdash.case" k="77"/><hkern g1="X" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="9"/><hkern g1="X" g2="guillemotleft,guilsinglleft" k="80"/><hkern g1="X" g2="guillemotleft.case,guilsinglleft.case" k="76"/><hkern g1="X" g2="m,n,p,r,ntilde,r.ss03" k="5"/><hkern g1="X" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="43"/><hkern g1="X" g2="oslash" k="28"/><hkern g1="X" g2="comma,period,ellipsis" k="9"/><hkern g1="X" g2="quoteright,quotedblright" k="-1"/><hkern g1="X" g2="s,scaron" k="18"/><hkern g1="X" g2="u,ugrave,uacute,ucircumflex,udieresis" k="37"/><hkern g1="X" g2="w" k="42"/><hkern g1="X" g2="y,yacute,ydieresis" k="42"/><hkern g1="X" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="43"/><hkern g1="X" g2="Oslash" k="38"/><hkern g1="X" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="5"/><hkern g1="X" g2="bracketright,braceright" k="-2"/><hkern g1="X" g2="bracketright.case,braceright.case" k="-2"/><hkern g1="X" g2="colon,semicolon" k="18"/><hkern g1="X" g2="hyphen,uni00AD,endash,emdash" k="76"/><hkern g1="X" g2="guillemotright,guilsinglright" k="30"/><hkern g1="X" g2="guillemotright.case,guilsinglright.case" k="28"/><hkern g1="X" g2="quotedbl,quotesingle" k="-1"/><hkern g1="X" g2="quoteleft,quotedblleft" k="-1"/><hkern g1="X" g2="z,zcaron" k="-1"/><hkern g1="X" g2="t" k="27"/><hkern g1="X" g2="S,Scaron" k="18"/><hkern g1="X" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="65"/><hkern g1="a.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="ampersand" g2="J" k="-9"/><hkern g1="ampersand" g2="T" k="70"/><hkern g1="ampersand" g2="W" k="40"/><hkern g1="ampersand" g2="Y,Yacute,Ydieresis" k="102"/><hkern g1="ampersand" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="9"/><hkern g1="ampersand" g2="u,ugrave,uacute,ucircumflex,udieresis" k="5"/><hkern g1="ampersand" g2="y,yacute,ydieresis" k="27"/><hkern g1="ampersand" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="14"/><hkern g1="ampersand" g2="Oslash" k="9"/><hkern g1="ampersand" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="10"/><hkern g1="ampersand.ss04" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="64"/><hkern g1="ampersand.ss04" g2="AE" k="93"/><hkern g1="ampersand.ss04" g2="T" k="77"/><hkern g1="ampersand.ss04" g2="W" k="55"/><hkern g1="ampersand.ss04" g2="Y,Yacute,Ydieresis" k="94"/><hkern g1="ampersand.ss04" g2="Z,Zcaron" k="25"/><hkern g1="ampersand.ss04" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="9"/><hkern g1="ampersand.ss04" g2="y,yacute,ydieresis" k="27"/><hkern g1="ampersand.ss04" g2="zero,six" k="7"/><hkern g1="ampersand.ss04" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="9"/><hkern g1="ampersand.ss04" g2="Oslash" k="13"/><hkern g1="ampersand.ss04" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="27"/><hkern g1="ampersand.ss04" g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn" k="18"/><hkern g1="ampersand.ss04" g2="S,Scaron" k="9"/><hkern g1="approxequal" g2="zero,six" k="-11"/><hkern g1="asciicircum" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="56"/><hkern g1="asciicircum" g2="AE" k="127"/><hkern g1="asciicircum" g2="Y,Yacute,Ydieresis" k="18"/><hkern g1="asciicircum" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-27"/><hkern g1="asciicircum" g2="w" k="-18"/><hkern g1="asciicircum" g2="y,yacute,ydieresis" k="-9"/><hkern g1="asciicircum" g2="Eth" k="-9"/><hkern g1="asciicircum" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-10"/><hkern g1="asciitilde" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="20"/><hkern g1="asciitilde" g2="AE" k="87"/><hkern g1="asciitilde" g2="T" k="61"/><hkern g1="asciitilde" g2="W" k="20"/><hkern g1="asciitilde" g2="Y,Yacute,Ydieresis" k="69"/><hkern g1="asciitilde" g2="Z,Zcaron" k="9"/><hkern g1="asciitilde" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="-9"/><hkern g1="asciitilde" g2="zero,six" k="-10"/><hkern g1="asciitilde" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-10"/><hkern g1="asciitilde" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="1"/><hkern g1="asciitilde" g2="j" k="9"/><hkern g1="asciitilde" g2="z,zcaron" k="5"/><hkern g1="asterisk" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="74"/><hkern g1="asterisk" g2="AE" k="145"/><hkern g1="asterisk" g2="J" k="50"/><hkern g1="asterisk" g2="T" k="-20"/><hkern g1="asterisk" g2="W" k="-10"/><hkern g1="asterisk" g2="Y,Yacute,Ydieresis" k="9"/><hkern g1="asterisk" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="3"/><hkern g1="asterisk" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="22"/><hkern g1="asterisk" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-36"/><hkern g1="asterisk" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="22"/><hkern g1="asterisk" g2="oslash" k="18"/><hkern g1="asterisk" g2="s,scaron" k="1"/><hkern g1="asterisk" g2="w" k="-9"/><hkern g1="asterisk" g2="y,yacute,ydieresis" k="-18"/><hkern g1="asterisk" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="1"/><hkern g1="at" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="57"/><hkern g1="at" g2="AE" k="106"/><hkern g1="at" g2="J" k="28"/><hkern g1="at" g2="T" k="25"/><hkern g1="at" g2="W" k="20"/><hkern g1="at" g2="Y,Yacute,Ydieresis" k="58"/><hkern g1="at" g2="Z,Zcaron" k="11"/><hkern g1="at" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="9"/><hkern g1="at" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="18"/><hkern g1="at" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-9"/><hkern g1="at" g2="m,n,p,r,ntilde,r.ss03" k="14"/><hkern g1="at" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="18"/><hkern g1="at" g2="oslash" k="14"/><hkern g1="at" g2="s,scaron" k="5"/><hkern g1="at" g2="u,ugrave,uacute,ucircumflex,udieresis" k="5"/><hkern g1="at" g2="w" k="-2"/><hkern g1="at" g2="y,yacute,ydieresis" k="3"/><hkern g1="at" g2="zero,six" k="9"/><hkern g1="at" g2="Eth" k="-1"/><hkern g1="at" g2="Oslash" k="5"/><hkern g1="at" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="9"/><hkern g1="at" g2="z,zcaron" k="-1"/><hkern g1="b.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="backslash" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="-18"/><hkern g1="backslash" g2="AE" k="-27"/><hkern g1="backslash" g2="J" k="9"/><hkern g1="backslash" g2="T" k="68"/><hkern g1="backslash" g2="W" k="78"/><hkern g1="backslash" g2="Y,Yacute,Ydieresis" k="97"/><hkern g1="backslash" g2="Z,Zcaron" k="-9"/><hkern g1="backslash" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="-1"/><hkern g1="backslash" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="28"/><hkern g1="backslash" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="29"/><hkern g1="backslash" g2="oslash" k="18"/><hkern g1="backslash" g2="u,ugrave,uacute,ucircumflex,udieresis" k="18"/><hkern g1="backslash" g2="w" k="28"/><hkern g1="backslash" g2="y,yacute,ydieresis" k="42"/><hkern g1="backslash" g2="zero,six" k="22"/><hkern g1="backslash" g2="Eth" k="18"/><hkern g1="backslash" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="49"/><hkern g1="backslash" g2="Oslash" k="38"/><hkern g1="backslash" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="47"/><hkern g1="backslash" g2="z,zcaron" k="-9"/><hkern g1="backslash" g2="t" k="9"/><hkern g1="backslash" g2="S,Scaron" k="19"/><hkern g1="bar" g2="T" k="-11"/><hkern g1="bar" g2="W" k="-11"/><hkern g1="bar" g2="Y,Yacute,Ydieresis" k="-10"/><hkern g1="bar" g2="Z,Zcaron" k="-9"/><hkern g1="bar" g2="w" k="-11"/><hkern g1="bar" g2="y,yacute,ydieresis" k="-11"/><hkern g1="bar" g2="j" k="-27"/><hkern g1="bar" g2="z,zcaron" k="-10"/><hkern g1="braceright" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="1"/><hkern g1="braceright" g2="AE" k="4"/><hkern g1="braceright" g2="T" k="2"/><hkern g1="braceright" g2="W" k="1"/><hkern g1="braceright" g2="Y,Yacute,Ydieresis" k="4"/><hkern g1="braceright" g2="Z,Zcaron" k="2"/><hkern g1="braceright.case" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="1"/><hkern g1="braceright.case" g2="AE" k="1"/><hkern g1="braceright.case" g2="T" k="1"/><hkern g1="braceright.case" g2="Y,Yacute,Ydieresis" k="1"/><hkern g1="braceright.case" g2="S,Scaron" k="1"/><hkern g1="brokenbar" g2="T" k="-20"/><hkern g1="brokenbar" g2="W" k="-11"/><hkern g1="brokenbar" g2="Y,Yacute,Ydieresis" k="-10"/><hkern g1="brokenbar" g2="Z,Zcaron" k="-9"/><hkern g1="brokenbar" g2="w" k="-11"/><hkern g1="brokenbar" g2="y,yacute,ydieresis" k="-19"/><hkern g1="brokenbar" g2="z,zcaron" k="-9"/><hkern g1="bullet" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="39"/><hkern g1="bullet" g2="AE" k="77"/><hkern g1="bullet" g2="J" k="1"/><hkern g1="bullet" g2="T" k="88"/><hkern g1="bullet" g2="W" k="22"/><hkern g1="bullet" g2="Y,Yacute,Ydieresis" k="97"/><hkern g1="bullet" g2="Z,Zcaron" k="11"/><hkern g1="bullet" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="1"/><hkern g1="bullet" g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn" k="1"/><hkern g1="c.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="cent" g2="zero,six" k="1"/><hkern g1="d.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="dagger" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="39"/><hkern g1="dagger" g2="AE" k="81"/><hkern g1="dagger" g2="J" k="27"/><hkern g1="dagger" g2="T" k="-27"/><hkern g1="dagger" g2="W" k="-18"/><hkern g1="dagger" g2="Z,Zcaron" k="-9"/><hkern g1="dagger" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-27"/><hkern g1="dagger" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="9"/><hkern g1="dagger" g2="w" k="-31"/><hkern g1="dagger" g2="y,yacute,ydieresis" k="-29"/><hkern g1="dagger" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-9"/><hkern g1="dagger" g2="Oslash" k="-9"/><hkern g1="dagger" g2="j" k="-18"/><hkern g1="dagger" g2="b,h,k,l,thorn" k="-9"/><hkern g1="dagger" g2="z,zcaron" k="-18"/><hkern g1="dagger" g2="S,Scaron" k="-9"/><hkern g1="daggerdbl" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="9"/><hkern g1="daggerdbl" g2="AE" k="27"/><hkern g1="daggerdbl" g2="J" k="-9"/><hkern g1="daggerdbl" g2="T" k="-27"/><hkern g1="daggerdbl" g2="W" k="-9"/><hkern g1="daggerdbl" g2="Z,Zcaron" k="-9"/><hkern g1="daggerdbl" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-18"/><hkern g1="daggerdbl" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="-9"/><hkern g1="daggerdbl" g2="w" k="-31"/><hkern g1="daggerdbl" g2="y,yacute,ydieresis" k="-29"/><hkern g1="daggerdbl" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-9"/><hkern g1="daggerdbl" g2="Oslash" k="-9"/><hkern g1="daggerdbl" g2="b,h,k,l,thorn" k="-9"/><hkern g1="daggerdbl" g2="z,zcaron" k="-18"/><hkern g1="divide" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="19"/><hkern g1="divide" g2="T" k="33"/><hkern g1="divide" g2="W" k="25"/><hkern g1="divide" g2="Y,Yacute,Ydieresis" k="59"/><hkern g1="divide" g2="Z,Zcaron" k="9"/><hkern g1="divide" g2="w" k="9"/><hkern g1="divide" g2="y,yacute,ydieresis" k="9"/><hkern g1="divide" g2="zero,six" k="-9"/><hkern g1="divide" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-9"/><hkern g1="divide" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="1"/><hkern g1="uni2215" g2="zero,six" k="29"/><hkern g1="dollar" g2="zero,six" k="1"/><hkern g1="e.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="eight" g2="hyphen.case,endash.case,emdash.case" k="10"/><hkern g1="eight" g2="guillemotleft,guilsinglleft" k="9"/><hkern g1="eight" g2="guillemotleft.case,guilsinglleft.case" k="9"/><hkern g1="eight" g2="comma,period,ellipsis" k="1"/><hkern g1="eight" g2="quoteright,quotedblright" k="9"/><hkern g1="eight" g2="zero,six" k="5"/><hkern g1="eight" g2="bracketright,braceright" k="9"/><hkern g1="eight" g2="bracketright.case,braceright.case" k="9"/><hkern g1="eight" g2="quotesinglbase,quotedblbase" k="9"/><hkern g1="eight" g2="quoteleft,quotedblleft" k="9"/><hkern g1="equal" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="18"/><hkern g1="equal" g2="AE" k="54"/><hkern g1="equal" g2="T" k="30"/><hkern g1="equal" g2="W" k="10"/><hkern g1="equal" g2="Y,Yacute,Ydieresis" k="39"/><hkern g1="equal" g2="zero,six" k="-10"/><hkern g1="equal" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-9"/><hkern g1="equal" g2="Oslash" k="-9"/><hkern g1="equal" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="5"/><hkern g1="exclam" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="18"/><hkern g1="exclam" g2="AE" k="18"/><hkern g1="exclam" g2="J" k="9"/><hkern g1="exclam" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="18"/><hkern g1="exclam" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="18"/><hkern g1="exclam" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="9"/><hkern g1="exclam" g2="guillemotright,guilsinglright" k="9"/><hkern g1="exclam" g2="S,Scaron" k="9"/><hkern g1="exclamdown" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="9"/><hkern g1="exclamdown" g2="J" k="9"/><hkern g1="exclamdown" g2="T" k="53"/><hkern g1="exclamdown" g2="W" k="28"/><hkern g1="exclamdown" g2="Y,Yacute,Ydieresis" k="82"/><hkern g1="exclamdown" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="18"/><hkern g1="exclamdown" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="18"/><hkern g1="exclamdown" g2="oslash" k="9"/><hkern g1="exclamdown" g2="u,ugrave,uacute,ucircumflex,udieresis" k="5"/><hkern g1="exclamdown" g2="w" k="9"/><hkern g1="exclamdown" g2="y,yacute,ydieresis" k="9"/><hkern g1="exclamdown" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="9"/><hkern g1="exclamdown" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="19"/><hkern g1="exclamdown" g2="i,igrave,iacute,icircumflex,idieresis,dotlessi" k="9"/><hkern g1="exclamdown" g2="j" k="-39"/><hkern g1="exclamdown" g2="S,Scaron" k="9"/><hkern g1="exclamdown.case" g2="T" k="-9"/><hkern g1="exclamdown.case" g2="Y,Yacute,Ydieresis" k="9"/><hkern g1="exclamdown.case" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="1"/><hkern g1="exclamdown.case" g2="guillemotright,guilsinglright" k="9"/><hkern g1="exclamdown.case" g2="j" k="-9"/><hkern g1="f.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="five" g2="comma,period,ellipsis" k="10"/><hkern g1="five" g2="zero,six" k="5"/><hkern g1="five" g2="quotesinglbase,quotedblbase" k="1"/><hkern g1="five" g2="quoteleft,quotedblleft" k="1"/><hkern g1="florin" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="42"/><hkern g1="florin" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="34"/><hkern g1="florin" g2="guillemotleft,guilsinglleft" k="38"/><hkern g1="florin" g2="m,n,p,r,ntilde,r.ss03" k="22"/><hkern g1="florin" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="51"/><hkern g1="florin" g2="oslash" k="50"/><hkern g1="florin" g2="comma,period,ellipsis" k="98"/><hkern g1="florin" g2="quoteright,quotedblright" k="-40"/><hkern g1="florin" g2="s,scaron" k="12"/><hkern g1="florin" g2="u,ugrave,uacute,ucircumflex,udieresis" k="22"/><hkern g1="florin" g2="w" k="1"/><hkern g1="florin" g2="bracketright,braceright" k="-29"/><hkern g1="florin" g2="colon,semicolon" k="11"/><hkern g1="florin" g2="hyphen,uni00AD,endash,emdash" k="3"/><hkern g1="florin" g2="quotedbl,quotesingle" k="-40"/><hkern g1="florin" g2="quotesinglbase,quotedblbase" k="87"/><hkern g1="florin" g2="quoteleft,quotedblleft" k="-31"/><hkern g1="florin" g2="t" k="-9"/><hkern g1="florin.tf" g2="zero,six" k="31"/><hkern g1="four" g2="hyphen.case,endash.case,emdash.case" k="1"/><hkern g1="four" g2="quoteright,quotedblright" k="38"/><hkern g1="four" g2="colon,semicolon" k="-9"/><hkern g1="four" g2="guillemotright.case,guilsinglright.case" k="-9"/><hkern g1="four" g2="quotedbl,quotesingle" k="18"/><hkern g1="four" g2="quoteleft,quotedblleft" k="11"/><hkern g1="four" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="9"/><hkern g1="g.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="germandbls" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="-5"/><hkern g1="germandbls" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="1"/><hkern g1="germandbls" g2="oslash" k="-5"/><hkern g1="germandbls" g2="s,scaron" k="-5"/><hkern g1="germandbls" g2="u,ugrave,uacute,ucircumflex,udieresis" k="10"/><hkern g1="germandbls" g2="w" k="9"/><hkern g1="germandbls" g2="y,yacute,ydieresis" k="1"/><hkern g1="germandbls" g2="bracketright,braceright" k="-2"/><hkern g1="germandbls" g2="quoteleft,quotedblleft" k="9"/><hkern g1="germandbls" g2="t" k="5"/><hkern g1="greater" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="25"/><hkern g1="greater" g2="AE" k="72"/><hkern g1="greater" g2="T" k="67"/><hkern g1="greater" g2="W" k="29"/><hkern g1="greater" g2="Y,Yacute,Ydieresis" k="78"/><hkern g1="greater" g2="Z,Zcaron" k="11"/><hkern g1="greater" g2="w" k="9"/><hkern g1="greater" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-9"/><hkern g1="greater" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="1"/><hkern g1="greater" g2="S,Scaron" k="5"/><hkern g1="greaterequal" g2="zero,six" k="-1"/><hkern g1="h.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="i.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="infinity" g2="zero,six" k="-1"/><hkern g1="j.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="k.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="l.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="less" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="-18"/><hkern g1="less" g2="T" k="1"/><hkern g1="less" g2="W" k="-18"/><hkern g1="less" g2="Z,Zcaron" k="-28"/><hkern g1="less" g2="w" k="-18"/><hkern g1="less" g2="y,yacute,ydieresis" k="-27"/><hkern g1="less" g2="zero,six" k="-11"/><hkern g1="less" g2="Eth" k="-9"/><hkern g1="less" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-10"/><hkern g1="less" g2="Oslash" k="-18"/><hkern g1="less" g2="z,zcaron" k="-18"/><hkern g1="less" g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn" k="-1"/><hkern g1="lessequal" g2="zero,six" k="-1"/><hkern g1="lslash" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="-10"/><hkern g1="lslash" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-25"/><hkern g1="lslash" g2="guillemotleft,guilsinglleft" k="-1"/><hkern g1="lslash" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="4"/><hkern g1="lslash" g2="oslash" k="-1"/><hkern g1="lslash" g2="comma,period,ellipsis" k="36"/><hkern g1="lslash" g2="s,scaron" k="-10"/><hkern g1="lslash" g2="u,ugrave,uacute,ucircumflex,udieresis" k="-5"/><hkern g1="lslash" g2="w" k="-12"/><hkern g1="lslash" g2="y,yacute,ydieresis" k="-20"/><hkern g1="lslash" g2="colon,semicolon" k="-9"/><hkern g1="lslash" g2="hyphen,uni00AD,endash,emdash" k="-9"/><hkern g1="lslash" g2="quotedbl,quotesingle" k="-18"/><hkern g1="lslash" g2="quotesinglbase,quotedblbase" k="36"/><hkern g1="lslash" g2="z,zcaron" k="-20"/><hkern g1="lslash" g2="t" k="-20"/><hkern g1="lslash" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="-2"/><hkern g1="m.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="minus" g2="zero,six" k="-10"/><hkern g1="minus" g2="bracketright,braceright" k="4"/><hkern g1="multiply" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="9"/><hkern g1="multiply" g2="T" k="13"/><hkern g1="multiply" g2="W" k="9"/><hkern g1="multiply" g2="Y,Yacute,Ydieresis" k="30"/><hkern g1="multiply" g2="Z,Zcaron" k="-9"/><hkern g1="multiply" g2="w" k="-9"/><hkern g1="multiply" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-9"/><hkern g1="n.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="notequal" g2="zero,six" k="-1"/><hkern g1="numbersign" g2="zero,six" k="11"/><hkern g1="numbersign.tf" g2="zero,six" k="-9"/><hkern g1="o.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="one" g2="hyphen.case,endash.case,emdash.case" k="-18"/><hkern g1="one" g2="comma,period,ellipsis" k="6"/><hkern g1="one" g2="quoteright,quotedblright" k="1"/><hkern g1="one" g2="zero,six" k="-14"/><hkern g1="one" g2="bracketright,braceright" k="-19"/><hkern g1="one" g2="bracketright.case,braceright.case" k="-18"/><hkern g1="one" g2="colon,semicolon" k="-9"/><hkern g1="one" g2="hyphen,uni00AD,endash,emdash" k="-18"/><hkern g1="one" g2="quotedbl,quotesingle" k="1"/><hkern g1="one" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="1"/><hkern g1="one.ss05" g2="hyphen.case,endash.case,emdash.case" k="21"/><hkern g1="one.ss05" g2="guillemotleft,guilsinglleft" k="28"/><hkern g1="one.ss05" g2="guillemotleft.case,guilsinglleft.case" k="30"/><hkern g1="one.ss05" g2="comma,period,ellipsis" k="-12"/><hkern g1="one.ss05" g2="quoteright,quotedblright" k="39"/><hkern g1="one.ss05" g2="zero,six" k="18"/><hkern g1="one.ss05" g2="bracketright,braceright" k="-3"/><hkern g1="one.ss05" g2="bracketright.case,braceright.case" k="-2"/><hkern g1="one.ss05" g2="hyphen,uni00AD,endash,emdash" k="9"/><hkern g1="one.ss05" g2="guillemotright,guilsinglright" k="-1"/><hkern g1="one.ss05" g2="guillemotright.case,guilsinglright.case" k="-1"/><hkern g1="one.ss05" g2="quotedbl,quotesingle" k="27"/><hkern g1="one.ss05" g2="quotesinglbase,quotedblbase" k="-29"/><hkern g1="one.ss05" g2="quoteleft,quotedblleft" k="28"/><hkern g1="onesuperior" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="10"/><hkern g1="onesuperior" g2="comma,period,ellipsis" k="8"/><hkern g1="onesuperior" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="2"/><hkern g1="ordmasculine" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="3"/><hkern g1="p.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="paragraph" g2="T" k="-3"/><hkern g1="parenleft" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="18"/><hkern g1="parenleft" g2="AE" k="36"/><hkern g1="parenleft" g2="J" k="15"/><hkern g1="parenleft" g2="T" k="-2"/><hkern g1="parenleft" g2="W" k="-2"/><hkern g1="parenleft" g2="Y,Yacute,Ydieresis" k="-2"/><hkern g1="parenleft" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="1"/><hkern g1="parenleft" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="29"/><hkern g1="parenleft" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="19"/><hkern g1="parenleft" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="38"/><hkern g1="parenleft" g2="oslash" k="28"/><hkern g1="parenleft" g2="s,scaron" k="23"/><hkern g1="parenleft" g2="u,ugrave,uacute,ucircumflex,udieresis" k="27"/><hkern g1="parenleft" g2="w" k="29"/><hkern g1="parenleft" g2="y,yacute,ydieresis" k="36"/><hkern g1="parenleft" g2="zero,six" k="20"/><hkern g1="parenleft" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="29"/><hkern g1="parenleft" g2="Oslash" k="29"/><hkern g1="parenleft" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="9"/><hkern g1="parenleft" g2="hyphen,uni00AD,endash,emdash" k="40"/><hkern g1="parenleft" g2="j" k="-77"/><hkern g1="parenleft" g2="z,zcaron" k="5"/><hkern g1="parenleft" g2="t" k="18"/><hkern g1="parenleft" g2="S,Scaron" k="14"/><hkern g1="parenleft.case" g2="J" k="19"/><hkern g1="parenleft.case" g2="T" k="-2"/><hkern g1="parenleft.case" g2="W" k="-2"/><hkern g1="parenleft.case" g2="Y,Yacute,Ydieresis" k="-2"/><hkern g1="parenleft.case" g2="Z,Zcaron" k="-5"/><hkern g1="parenleft.case" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="1"/><hkern g1="parenleft.case" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="37"/><hkern g1="parenleft.case" g2="zero,six" k="20"/><hkern g1="parenleft.case" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="29"/><hkern g1="parenleft.case" g2="Oslash" k="29"/><hkern g1="parenleft.case" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="9"/><hkern g1="parenleft.case" g2="j" k="-1"/><hkern g1="parenleft.case" g2="S,Scaron" k="14"/><hkern g1="parenright.case" g2="AE" k="3"/><hkern g1="percent" g2="zero,six" k="20"/><hkern g1="periodcentered" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="38"/><hkern g1="periodcentered" g2="AE" k="83"/><hkern g1="periodcentered" g2="T" k="50"/><hkern g1="periodcentered" g2="W" k="21"/><hkern g1="periodcentered" g2="Y,Yacute,Ydieresis" k="69"/><hkern g1="periodcentered" g2="Z,Zcaron" k="10"/><hkern g1="periodcentered" g2="y,yacute,ydieresis" k="9"/><hkern g1="periodcentered" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-9"/><hkern g1="periodcentered" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="10"/><hkern g1="perthousand" g2="zero,six" k="18"/><hkern g1="plus" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="29"/><hkern g1="plus" g2="AE" k="72"/><hkern g1="plus" g2="T" k="59"/><hkern g1="plus" g2="W" k="30"/><hkern g1="plus" g2="Y,Yacute,Ydieresis" k="68"/><hkern g1="plus" g2="Z,Zcaron" k="7"/><hkern g1="plus" g2="w" k="5"/><hkern g1="plus" g2="zero,six" k="-9"/><hkern g1="plus" g2="Eth" k="9"/><hkern g1="plus" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-9"/><hkern g1="plus" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="1"/><hkern g1="plus" g2="bracketright,braceright" k="4"/><hkern g1="plus" g2="b,h,k,l,thorn" k="18"/><hkern g1="q.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="question" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="54"/><hkern g1="question" g2="AE" k="97"/><hkern g1="question" g2="J" k="18"/><hkern g1="question" g2="Y,Yacute,Ydieresis" k="9"/><hkern g1="question" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="29"/><hkern g1="question" g2="zero,six" k="9"/><hkern g1="question" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="1"/><hkern g1="questiondown" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="-11"/><hkern g1="questiondown" g2="J" k="18"/><hkern g1="questiondown" g2="T" k="120"/><hkern g1="questiondown" g2="W" k="78"/><hkern g1="questiondown" g2="Y,Yacute,Ydieresis" k="116"/><hkern g1="questiondown" g2="Z,Zcaron" k="-9"/><hkern g1="questiondown" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="29"/><hkern g1="questiondown" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="39"/><hkern g1="questiondown" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="24"/><hkern g1="questiondown" g2="m,n,p,r,ntilde,r.ss03" k="1"/><hkern g1="questiondown" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="38"/><hkern g1="questiondown" g2="oslash" k="18"/><hkern g1="questiondown" g2="s,scaron" k="18"/><hkern g1="questiondown" g2="u,ugrave,uacute,ucircumflex,udieresis" k="37"/><hkern g1="questiondown" g2="w" k="39"/><hkern g1="questiondown" g2="y,yacute,ydieresis" k="28"/><hkern g1="questiondown" g2="zero,six" k="29"/><hkern g1="questiondown" g2="Eth" k="36"/><hkern g1="questiondown" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="49"/><hkern g1="questiondown" g2="Oslash" k="36"/><hkern g1="questiondown" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="48"/><hkern g1="questiondown" g2="j" k="-58"/><hkern g1="questiondown" g2="b,h,k,l,thorn" k="1"/><hkern g1="questiondown" g2="quotesinglbase,quotedblbase" k="-4"/><hkern g1="questiondown" g2="quoteleft,quotedblleft" k="3"/><hkern g1="questiondown" g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn" k="5"/><hkern g1="questiondown" g2="t" k="37"/><hkern g1="questiondown" g2="S,Scaron" k="37"/><hkern g1="questiondown.case" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="9"/><hkern g1="questiondown.case" g2="AE" k="27"/><hkern g1="questiondown.case" g2="J" k="-9"/><hkern g1="questiondown.case" g2="T" k="48"/><hkern g1="questiondown.case" g2="W" k="29"/><hkern g1="questiondown.case" g2="Y,Yacute,Ydieresis" k="69"/><hkern g1="questiondown.case" g2="Z,Zcaron" k="-18"/><hkern g1="questiondown.case" g2="Eth" k="18"/><hkern g1="questiondown.case" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="9"/><hkern g1="r.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="radical" g2="zero,six" k="91"/><hkern g1="registered.ss06" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="9"/><hkern g1="registered.ss06" g2="T" k="-27"/><hkern g1="registered.ss06" g2="Y,Yacute,Ydieresis" k="9"/><hkern g1="s.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="section" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="9"/><hkern g1="section" g2="Y,Yacute,Ydieresis" k="9"/><hkern g1="section" g2="Oslash" k="5"/><hkern g1="section" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="9"/><hkern g1="seven" g2="hyphen.case,endash.case,emdash.case" k="24"/><hkern g1="seven" g2="guillemotleft,guilsinglleft" k="29"/><hkern g1="seven" g2="guillemotleft.case,guilsinglleft.case" k="21"/><hkern g1="seven" g2="comma,period,ellipsis" k="137"/><hkern g1="seven" g2="quoteright,quotedblright" k="-29"/><hkern g1="seven" g2="zero,six" k="12"/><hkern g1="seven" g2="bracketright.case,braceright.case" k="5"/><hkern g1="seven" g2="colon,semicolon" k="3"/><hkern g1="seven" g2="hyphen,uni00AD,endash,emdash" k="32"/><hkern g1="seven" g2="guillemotright,guilsinglright" k="-16"/><hkern g1="seven" g2="guillemotright.case,guilsinglright.case" k="-9"/><hkern g1="seven" g2="quotedbl,quotesingle" k="-29"/><hkern g1="seven" g2="quotesinglbase,quotedblbase" k="106"/><hkern g1="seven" g2="quoteleft,quotedblleft" k="-39"/><hkern g1="seven" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="1"/><hkern g1="seven.numr" g2="m,n,p,r,ntilde,r.ss03" k="36"/><hkern g1="seven.numr" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="90"/><hkern g1="seven.numr" g2="s,scaron" k="81"/><hkern g1="six" g2="comma,period,ellipsis" k="1"/><hkern g1="six" g2="quoteright,quotedblright" k="-9"/><hkern g1="six" g2="bracketright,braceright" k="-9"/><hkern g1="six" g2="bracketright.case,braceright.case" k="-9"/><hkern g1="six" g2="colon,semicolon" k="-5"/><hkern g1="six" g2="hyphen,uni00AD,endash,emdash" k="-9"/><hkern g1="six" g2="quotesinglbase,quotedblbase" k="1"/><hkern g1="six" g2="quoteleft,quotedblleft" k="-9"/><hkern g1="six" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="1"/><hkern g1="slash" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="88"/><hkern g1="slash" g2="AE" k="158"/><hkern g1="slash" g2="J" k="84"/><hkern g1="slash" g2="T" k="-22"/><hkern g1="slash" g2="W" k="-20"/><hkern g1="slash" g2="Y,Yacute,Ydieresis" k="-19"/><hkern g1="slash" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="84"/><hkern g1="slash" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="68"/><hkern g1="slash" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="24"/><hkern g1="slash" g2="m,n,p,r,ntilde,r.ss03" k="42"/><hkern g1="slash" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="129"/><hkern g1="slash" g2="oslash" k="89"/><hkern g1="slash" g2="s,scaron" k="98"/><hkern g1="slash" g2="u,ugrave,uacute,ucircumflex,udieresis" k="27"/><hkern g1="slash" g2="w" k="29"/><hkern g1="slash" g2="y,yacute,ydieresis" k="19"/><hkern g1="slash" g2="zero,six" k="40"/><hkern g1="slash" g2="Eth" k="23"/><hkern g1="slash" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="49"/><hkern g1="slash" g2="Oslash" k="49"/><hkern g1="slash" g2="z,zcaron" k="33"/><hkern g1="slash" g2="t" k="9"/><hkern g1="slash" g2="S,Scaron" k="28"/><hkern g1="space" g2="hyphen,uni00AD,endash,emdash" k="50"/><hkern g1="sterling" g2="zero,six" k="1"/><hkern g1="t.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="three" g2="guillemotleft.case,guilsinglleft.case" k="10"/><hkern g1="three" g2="comma,period,ellipsis" k="10"/><hkern g1="three" g2="quoteright,quotedblright" k="1"/><hkern g1="three" g2="zero,six" k="5"/><hkern g1="three" g2="hyphen,uni00AD,endash,emdash" k="1"/><hkern g1="three" g2="guillemotright,guilsinglright" k="-9"/><hkern g1="three" g2="guillemotright.case,guilsinglright.case" k="-10"/><hkern g1="three" g2="quoteleft,quotedblleft" k="-9"/><hkern g1="threesuperior" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="10"/><hkern g1="threesuperior" g2="comma,period,ellipsis" k="8"/><hkern g1="threesuperior" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="3"/><hkern g1="trademark" g2="T" k="-3"/><hkern g1="two" g2="hyphen.case,endash.case,emdash.case" k="-9"/><hkern g1="two" g2="guillemotleft.case,guilsinglleft.case" k="1"/><hkern g1="two" g2="guillemotright,guilsinglright" k="1"/><hkern g1="twosuperior" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="9"/><hkern g1="twosuperior" g2="comma,period,ellipsis" k="8"/><hkern g1="twosuperior" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="2"/><hkern g1="u.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="underscore" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="-30"/><hkern g1="underscore" g2="AE" k="-20"/><hkern g1="underscore" g2="J" k="27"/><hkern g1="underscore" g2="T" k="49"/><hkern g1="underscore" g2="W" k="50"/><hkern g1="underscore" g2="Y,Yacute,Ydieresis" k="87"/><hkern g1="underscore" g2="Z,Zcaron" k="-19"/><hkern g1="underscore" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="29"/><hkern g1="underscore" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="10"/><hkern g1="underscore" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="29"/><hkern g1="underscore" g2="oslash" k="28"/><hkern g1="underscore" g2="u,ugrave,uacute,ucircumflex,udieresis" k="20"/><hkern g1="underscore" g2="w" k="40"/><hkern g1="underscore" g2="y,yacute,ydieresis" k="31"/><hkern g1="underscore" g2="zero,six" k="20"/><hkern g1="underscore" g2="Eth" k="9"/><hkern g1="underscore" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="40"/><hkern g1="underscore" g2="Oslash" k="30"/><hkern g1="underscore" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="20"/><hkern g1="underscore" g2="j" k="-77"/><hkern g1="underscore" g2="z,zcaron" k="-9"/><hkern g1="underscore" g2="t" k="47"/><hkern g1="underscore" g2="S,Scaron" k="28"/><hkern g1="uni2070" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="12"/><hkern g1="uni2070" g2="comma,period,ellipsis" k="8"/><hkern g1="uni2070" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="2"/><hkern g1="uni2074" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="10"/><hkern g1="uni2074" g2="comma,period,ellipsis" k="8"/><hkern g1="uni2074" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="2"/><hkern g1="uni2075" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="9"/><hkern g1="uni2075" g2="comma,period,ellipsis" k="8"/><hkern g1="uni2075" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="2"/><hkern g1="uni2076" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="9"/><hkern g1="uni2076" g2="comma,period,ellipsis" k="8"/><hkern g1="uni2076" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="2"/><hkern g1="uni2077" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="14"/><hkern g1="uni2077" g2="m,n,p,r,ntilde,r.ss03" k="8"/><hkern g1="uni2077" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="10"/><hkern g1="uni2077" g2="comma,period,ellipsis" k="8"/><hkern g1="uni2077" g2="zero,six" k="33"/><hkern g1="uni2077" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="7"/><hkern g1="uni2078" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="10"/><hkern g1="uni2078" g2="comma,period,ellipsis" k="8"/><hkern g1="uni2078" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="3"/><hkern g1="uni2079" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="11"/><hkern g1="uni2079" g2="comma,period,ellipsis" k="8"/><hkern g1="uni2079" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="3"/><hkern g1="uni2080" g2="J" k="9"/><hkern g1="uni2080" g2="T" k="118"/><hkern g1="uni2080" g2="W" k="79"/><hkern g1="uni2080" g2="Y,Yacute,Ydieresis" k="138"/><hkern g1="uni2080" g2="w" k="48"/><hkern g1="uni2080" g2="y,yacute,ydieresis" k="67"/><hkern g1="uni2080" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="30"/><hkern g1="uni2080" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="29"/><hkern g1="uni2081" g2="T" k="100"/><hkern g1="uni2081" g2="W" k="67"/><hkern g1="uni2081" g2="Y,Yacute,Ydieresis" k="111"/><hkern g1="uni2081" g2="w" k="30"/><hkern g1="uni2081" g2="y,yacute,ydieresis" k="57"/><hkern g1="uni2081" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="28"/><hkern g1="uni2081" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="19"/><hkern g1="uni2082" g2="T" k="100"/><hkern g1="uni2082" g2="W" k="70"/><hkern g1="uni2082" g2="Y,Yacute,Ydieresis" k="129"/><hkern g1="uni2082" g2="w" k="57"/><hkern g1="uni2082" g2="y,yacute,ydieresis" k="66"/><hkern g1="uni2082" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="38"/><hkern g1="uni2082" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="19"/><hkern g1="uni2083" g2="T" k="100"/><hkern g1="uni2083" g2="W" k="70"/><hkern g1="uni2083" g2="Y,Yacute,Ydieresis" k="127"/><hkern g1="uni2083" g2="w" k="39"/><hkern g1="uni2083" g2="y,yacute,ydieresis" k="66"/><hkern g1="uni2083" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="28"/><hkern g1="uni2083" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="9"/><hkern g1="uni2084" g2="J" k="9"/><hkern g1="uni2084" g2="T" k="102"/><hkern g1="uni2084" g2="W" k="98"/><hkern g1="uni2084" g2="Y,Yacute,Ydieresis" k="129"/><hkern g1="uni2084" g2="w" k="49"/><hkern g1="uni2084" g2="y,yacute,ydieresis" k="75"/><hkern g1="uni2084" g2="zero,six" k="5"/><hkern g1="uni2084" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="58"/><hkern g1="uni2084" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="20"/><hkern g1="uni2084" g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn" k="18"/><hkern g1="uni2085" g2="T" k="100"/><hkern g1="uni2085" g2="W" k="70"/><hkern g1="uni2085" g2="Y,Yacute,Ydieresis" k="129"/><hkern g1="uni2085" g2="w" k="40"/><hkern g1="uni2085" g2="y,yacute,ydieresis" k="75"/><hkern g1="uni2085" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="38"/><hkern g1="uni2085" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="10"/><hkern g1="uni2085" g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn" k="10"/><hkern g1="uni2085" g2="S,Scaron" k="1"/><hkern g1="uni2086" g2="T" k="100"/><hkern g1="uni2086" g2="W" k="71"/><hkern g1="uni2086" g2="Y,Yacute,Ydieresis" k="129"/><hkern g1="uni2086" g2="w" k="57"/><hkern g1="uni2086" g2="y,yacute,ydieresis" k="75"/><hkern g1="uni2086" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="39"/><hkern g1="uni2086" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="19"/><hkern g1="uni2086" g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn" k="19"/><hkern g1="uni2086" g2="S,Scaron" k="1"/><hkern g1="uni2087" g2="T" k="100"/><hkern g1="uni2087" g2="W" k="60"/><hkern g1="uni2087" g2="Y,Yacute,Ydieresis" k="118"/><hkern g1="uni2087" g2="w" k="48"/><hkern g1="uni2087" g2="y,yacute,ydieresis" k="84"/><hkern g1="uni2087" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="11"/><hkern g1="uni2087" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="10"/><hkern g1="uni2087" g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn" k="10"/><hkern g1="uni2087" g2="S,Scaron" k="1"/><hkern g1="uni2088" g2="T" k="100"/><hkern g1="uni2088" g2="W" k="79"/><hkern g1="uni2088" g2="Y,Yacute,Ydieresis" k="127"/><hkern g1="uni2088" g2="w" k="67"/><hkern g1="uni2088" g2="y,yacute,ydieresis" k="75"/><hkern g1="uni2088" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="39"/><hkern g1="uni2088" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="19"/><hkern g1="uni2088" g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn" k="19"/><hkern g1="uni2088" g2="S,Scaron" k="9"/><hkern g1="uni2089" g2="T" k="100"/><hkern g1="uni2089" g2="W" k="88"/><hkern g1="uni2089" g2="Y,Yacute,Ydieresis" k="136"/><hkern g1="uni2089" g2="w" k="66"/><hkern g1="uni2089" g2="y,yacute,ydieresis" k="84"/><hkern g1="uni2089" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="29"/><hkern g1="uni2089" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="19"/><hkern g1="uni2089" g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn" k="19"/><hkern g1="uni2089" g2="S,Scaron" k="1"/><hkern g1="v" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="1"/><hkern g1="v" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="15"/><hkern g1="v" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-24"/><hkern g1="v" g2="guillemotleft,guilsinglleft" k="18"/><hkern g1="v" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="15"/><hkern g1="v" g2="oslash" k="10"/><hkern g1="v" g2="comma,period,ellipsis" k="72"/><hkern g1="v" g2="w" k="-1"/><hkern g1="v" g2="y,yacute,ydieresis" k="-1"/><hkern g1="v" g2="bracketright,braceright" k="18"/><hkern g1="v" g2="colon,semicolon" k="9"/><hkern g1="v" g2="hyphen,uni00AD,endash,emdash" k="19"/><hkern g1="v" g2="guillemotright,guilsinglright" k="-2"/><hkern g1="v" g2="quotedbl,quotesingle" k="-19"/><hkern g1="v" g2="quotesinglbase,quotedblbase" k="58"/><hkern g1="v" g2="z,zcaron" k="-1"/><hkern g1="v" g2="t" k="-20"/><hkern g1="v" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="-9"/><hkern g1="v.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="w.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="x" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="5"/><hkern g1="x" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="19"/><hkern g1="x" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-11"/><hkern g1="x" g2="guillemotleft,guilsinglleft" k="19"/><hkern g1="x" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="24"/><hkern g1="x" g2="oslash" k="19"/><hkern g1="x" g2="quoteright,quotedblright" k="9"/><hkern g1="x" g2="u,ugrave,uacute,ucircumflex,udieresis" k="5"/><hkern g1="x" g2="w" k="-1"/><hkern g1="x" g2="y,yacute,ydieresis" k="-1"/><hkern g1="x" g2="bracketright,braceright" k="9"/><hkern g1="x" g2="colon,semicolon" k="9"/><hkern g1="x" g2="hyphen,uni00AD,endash,emdash" k="33"/><hkern g1="x" g2="quotedbl,quotesingle" k="-1"/><hkern g1="x" g2="quotesinglbase,quotedblbase" k="-10"/><hkern g1="x" g2="z,zcaron" k="-11"/><hkern g1="x" g2="t" k="-10"/><hkern g1="x" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="9"/><hkern g1="x.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="y.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="yen" g2="zero,six" k="9"/><hkern g1="z.ordn" g2="comma,period,ellipsis" k="8"/><hkern g1="bracketleft,braceleft" g2="V" k="-11"/><hkern g1="bracketleft,braceleft" g2="X" k="-2"/><hkern g1="bracketleft,braceleft" g2="eight" k="9"/><hkern g1="bracketleft,braceleft" g2="five" k="11"/><hkern g1="bracketleft,braceleft" g2="florin" k="-60"/><hkern g1="bracketleft,braceleft" g2="four" k="35"/><hkern g1="bracketleft,braceleft" g2="germandbls" k="18"/><hkern g1="bracketleft,braceleft" g2="minus" k="4"/><hkern g1="bracketleft,braceleft" g2="nine" k="-6"/><hkern g1="bracketleft,braceleft" g2="one" k="18"/><hkern g1="bracketleft,braceleft" g2="one.ss05" k="1"/><hkern g1="bracketleft,braceleft" g2="plus" k="4"/><hkern g1="bracketleft,braceleft" g2="seven" k="-23"/><hkern g1="bracketleft,braceleft" g2="three" k="-5"/><hkern g1="bracketleft,braceleft" g2="v" k="18"/><hkern g1="bracketleft,braceleft" g2="x" k="9"/><hkern g1="bracketleft,braceleft" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="9"/><hkern g1="bracketleft,braceleft" g2="J" k="18"/><hkern g1="bracketleft,braceleft" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="29"/><hkern g1="bracketleft,braceleft" g2="Oslash" k="29"/><hkern g1="bracketleft,braceleft" g2="S,Scaron" k="9"/><hkern g1="bracketleft,braceleft" g2="T" k="-2"/><hkern g1="bracketleft,braceleft" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="9"/><hkern g1="bracketleft,braceleft" g2="W" k="-2"/><hkern g1="bracketleft,braceleft" g2="Y,Yacute,Ydieresis" k="-4"/><hkern g1="bracketleft,braceleft" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="5"/><hkern g1="bracketleft,braceleft" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="20"/><hkern g1="bracketleft,braceleft" g2="hyphen,uni00AD,endash,emdash" k="2"/><hkern g1="bracketleft,braceleft" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="28"/><hkern g1="bracketleft,braceleft" g2="j" k="-77"/><hkern g1="bracketleft,braceleft" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="37"/><hkern g1="bracketleft,braceleft" g2="oslash" k="19"/><hkern g1="bracketleft,braceleft" g2="s,scaron" k="18"/><hkern g1="bracketleft,braceleft" g2="t" k="18"/><hkern g1="bracketleft,braceleft" g2="u,ugrave,uacute,ucircumflex,udieresis" k="18"/><hkern g1="bracketleft,braceleft" g2="w" k="20"/><hkern g1="bracketleft,braceleft" g2="y,yacute,ydieresis" k="27"/><hkern g1="bracketleft,braceleft" g2="zero,six" k="9"/><hkern g1="bracketleft.case,braceleft.case" g2="V" k="-11"/><hkern g1="bracketleft.case,braceleft.case" g2="X" k="-2"/><hkern g1="bracketleft.case,braceleft.case" g2="eight" k="9"/><hkern g1="bracketleft.case,braceleft.case" g2="five" k="11"/><hkern g1="bracketleft.case,braceleft.case" g2="four" k="26"/><hkern g1="bracketleft.case,braceleft.case" g2="nine" k="-2"/><hkern g1="bracketleft.case,braceleft.case" g2="one" k="9"/><hkern g1="bracketleft.case,braceleft.case" g2="seven" k="-21"/><hkern g1="bracketleft.case,braceleft.case" g2="Lslash" k="27"/><hkern g1="bracketleft.case,braceleft.case" g2="two" k="-1"/><hkern g1="bracketleft.case,braceleft.case" g2="J" k="18"/><hkern g1="bracketleft.case,braceleft.case" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="29"/><hkern g1="bracketleft.case,braceleft.case" g2="Oslash" k="29"/><hkern g1="bracketleft.case,braceleft.case" g2="S,Scaron" k="14"/><hkern g1="bracketleft.case,braceleft.case" g2="T" k="-2"/><hkern g1="bracketleft.case,braceleft.case" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="9"/><hkern g1="bracketleft.case,braceleft.case" g2="W" k="-2"/><hkern g1="bracketleft.case,braceleft.case" g2="Y,Yacute,Ydieresis" k="-2"/><hkern g1="bracketleft.case,braceleft.case" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="36"/><hkern g1="bracketleft.case,braceleft.case" g2="zero,six" k="12"/><hkern g1="bracketleft.case,braceleft.case" g2="Eth" k="23"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="V" k="37"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="X" k="65"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="five" k="1"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="four" k="1"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="germandbls" k="9"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="one" k="1"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="one.ss05" k="4"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="three" k="3"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="v" k="-9"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="x" k="9"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="Lslash" k="-1"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="39"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="J" k="9"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-1"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="T" k="10"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="W" k="18"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="Y,Yacute,Ydieresis" k="48"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="2"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-9"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="w" k="-1"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="Eth" k="-1"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="AE" k="89"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="Z,Zcaron" k="9"/><hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" g2="z,zcaron" k="1"/><hkern g1="colon,semicolon" g2="V" k="58"/><hkern g1="colon,semicolon" g2="X" k="18"/><hkern g1="colon,semicolon" g2="nine" k="-2"/><hkern g1="colon,semicolon" g2="one" k="9"/><hkern g1="colon,semicolon" g2="one.ss05" k="1"/><hkern g1="colon,semicolon" g2="seven" k="-1"/><hkern g1="colon,semicolon" g2="v" k="9"/><hkern g1="colon,semicolon" g2="x" k="9"/><hkern g1="colon,semicolon" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="9"/><hkern g1="colon,semicolon" g2="J" k="9"/><hkern g1="colon,semicolon" g2="S,Scaron" k="9"/><hkern g1="colon,semicolon" g2="T" k="47"/><hkern g1="colon,semicolon" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="5"/><hkern g1="colon,semicolon" g2="W" k="19"/><hkern g1="colon,semicolon" g2="Y,Yacute,Ydieresis" k="83"/><hkern g1="colon,semicolon" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-9"/><hkern g1="colon,semicolon" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="10"/><hkern g1="colon,semicolon" g2="y,yacute,ydieresis" k="9"/><hkern g1="colon,semicolon" g2="AE" k="9"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="V" k="69"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="X" k="67"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="five" k="1"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="four" k="1"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="one" k="20"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="one.ss05" k="35"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="seven" k="21"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="three" k="11"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="v" k="19"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="x" k="33"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="Lslash" k="-32"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="two" k="5"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="lslash" k="-22"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="parenright" k="40"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="space" k="50"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="38"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="J" k="9"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="S,Scaron" k="1"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="T" k="69"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="1"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="W" k="22"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="Y,Yacute,Ydieresis" k="98"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="18"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="j" k="1"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="t" k="1"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="w" k="10"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="y,yacute,ydieresis" k="29"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="zero,six" k="-9"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="AE" k="105"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="Z,Zcaron" k="23"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="z,zcaron" k="1"/><hkern g1="hyphen,uni00AD,endash,emdash" g2="bracketright,braceright" k="2"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="V" k="32"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="X" k="77"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="eight" k="10"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="five" k="3"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="four" k="2"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="nine" k="2"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="one" k="21"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="one.ss05" k="7"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="seven" k="13"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="three" k="5"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="Lslash" k="-3"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="two" k="3"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="60"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="J" k="23"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-18"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="S,Scaron" k="3"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="T" k="65"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="W" k="13"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="Y,Yacute,Ydieresis" k="71"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="zero,six" k="-9"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="AE" k="112"/><hkern g1="hyphen.case,endash.case,emdash.case" g2="Z,Zcaron" k="33"/><hkern g1="guillemotleft,guilsinglleft" g2="V" k="31"/><hkern g1="guillemotleft,guilsinglleft" g2="X" k="30"/><hkern g1="guillemotleft,guilsinglleft" g2="florin" k="18"/><hkern g1="guillemotleft,guilsinglleft" g2="germandbls" k="9"/><hkern g1="guillemotleft,guilsinglleft" g2="one" k="-11"/><hkern g1="guillemotleft,guilsinglleft" g2="one.ss05" k="2"/><hkern g1="guillemotleft,guilsinglleft" g2="seven" k="-27"/><hkern g1="guillemotleft,guilsinglleft" g2="three" k="-18"/><hkern g1="guillemotleft,guilsinglleft" g2="v" k="-2"/><hkern g1="guillemotleft,guilsinglleft" g2="two" k="-2"/><hkern g1="guillemotleft,guilsinglleft" g2="lslash" k="9"/><hkern g1="guillemotleft,guilsinglleft" g2="exclam" k="9"/><hkern g1="guillemotleft,guilsinglleft" g2="exclamdown.case" k="9"/><hkern g1="guillemotleft,guilsinglleft" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="9"/><hkern g1="guillemotleft,guilsinglleft" g2="J" k="-10"/><hkern g1="guillemotleft,guilsinglleft" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="10"/><hkern g1="guillemotleft,guilsinglleft" g2="Oslash" k="9"/><hkern g1="guillemotleft,guilsinglleft" g2="T" k="53"/><hkern g1="guillemotleft,guilsinglleft" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="9"/><hkern g1="guillemotleft,guilsinglleft" g2="W" k="10"/><hkern g1="guillemotleft,guilsinglleft" g2="Y,Yacute,Ydieresis" k="66"/><hkern g1="guillemotleft,guilsinglleft" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-19"/><hkern g1="guillemotleft,guilsinglleft" g2="j" k="5"/><hkern g1="guillemotleft,guilsinglleft" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="9"/><hkern g1="guillemotleft,guilsinglleft" g2="oslash" k="9"/><hkern g1="guillemotleft,guilsinglleft" g2="t" k="-1"/><hkern g1="guillemotleft,guilsinglleft" g2="AE" k="32"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="V" k="18"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="X" k="28"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="nine" k="-1"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="one" k="-1"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="one.ss05" k="2"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="seven" k="-27"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="three" k="-27"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="19"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="J" k="-1"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="10"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="Oslash" k="10"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="T" k="21"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="9"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="W" k="10"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="Y,Yacute,Ydieresis" k="55"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="t" k="-9"/><hkern g1="guillemotleft.case,guilsinglleft.case" g2="AE" k="46"/><hkern g1="guillemotright,guilsinglright" g2="V" k="69"/><hkern g1="guillemotright,guilsinglright" g2="X" k="80"/><hkern g1="guillemotright,guilsinglright" g2="eight" k="9"/><hkern g1="guillemotright,guilsinglright" g2="florin" k="48"/><hkern g1="guillemotright,guilsinglright" g2="germandbls" k="14"/><hkern g1="guillemotright,guilsinglright" g2="nine" k="9"/><hkern g1="guillemotright,guilsinglright" g2="one" k="21"/><hkern g1="guillemotright,guilsinglright" g2="one.ss05" k="33"/><hkern g1="guillemotright,guilsinglright" g2="seven" k="39"/><hkern g1="guillemotright,guilsinglright" g2="three" k="19"/><hkern g1="guillemotright,guilsinglright" g2="v" k="19"/><hkern g1="guillemotright,guilsinglright" g2="x" k="19"/><hkern g1="guillemotright,guilsinglright" g2="two" k="1"/><hkern g1="guillemotright,guilsinglright" g2="lslash" k="-2"/><hkern g1="guillemotright,guilsinglright" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="39"/><hkern g1="guillemotright,guilsinglright" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="10"/><hkern g1="guillemotright,guilsinglright" g2="S,Scaron" k="9"/><hkern g1="guillemotright,guilsinglright" g2="T" k="111"/><hkern g1="guillemotright,guilsinglright" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="19"/><hkern g1="guillemotright,guilsinglright" g2="W" k="57"/><hkern g1="guillemotright,guilsinglright" g2="Y,Yacute,Ydieresis" k="134"/><hkern g1="guillemotright,guilsinglright" g2="u,ugrave,uacute,ucircumflex,udieresis" k="9"/><hkern g1="guillemotright,guilsinglright" g2="w" k="10"/><hkern g1="guillemotright,guilsinglright" g2="y,yacute,ydieresis" k="9"/><hkern g1="guillemotright,guilsinglright" g2="AE" k="91"/><hkern g1="guillemotright,guilsinglright" g2="Z,Zcaron" k="17"/><hkern g1="guillemotright,guilsinglright" g2="z,zcaron" k="5"/><hkern g1="guillemotright,guilsinglright" g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn" k="1"/><hkern g1="guillemotright,guilsinglright" g2="b,h,k,l,thorn" k="9"/><hkern g1="guillemotright.case,guilsinglright.case" g2="V" k="58"/><hkern g1="guillemotright.case,guilsinglright.case" g2="X" k="75"/><hkern g1="guillemotright.case,guilsinglright.case" g2="eight" k="9"/><hkern g1="guillemotright.case,guilsinglright.case" g2="four" k="11"/><hkern g1="guillemotright.case,guilsinglright.case" g2="one" k="9"/><hkern g1="guillemotright.case,guilsinglright.case" g2="one.ss05" k="24"/><hkern g1="guillemotright.case,guilsinglright.case" g2="seven" k="2"/><hkern g1="guillemotright.case,guilsinglright.case" g2="three" k="21"/><hkern g1="guillemotright.case,guilsinglright.case" g2="Lslash" k="-2"/><hkern g1="guillemotright.case,guilsinglright.case" g2="two" k="9"/><hkern g1="guillemotright.case,guilsinglright.case" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="66"/><hkern g1="guillemotright.case,guilsinglright.case" g2="J" k="9"/><hkern g1="guillemotright.case,guilsinglright.case" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-9"/><hkern g1="guillemotright.case,guilsinglright.case" g2="S,Scaron" k="9"/><hkern g1="guillemotright.case,guilsinglright.case" g2="T" k="98"/><hkern g1="guillemotright.case,guilsinglright.case" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="19"/><hkern g1="guillemotright.case,guilsinglright.case" g2="W" k="39"/><hkern g1="guillemotright.case,guilsinglright.case" g2="Y,Yacute,Ydieresis" k="123"/><hkern g1="guillemotright.case,guilsinglright.case" g2="AE" k="108"/><hkern g1="guillemotright.case,guilsinglright.case" g2="Z,Zcaron" k="22"/><hkern g1="guillemotright.case,guilsinglright.case" g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn" k="1"/><hkern g1="comma,period,ellipsis" g2="V" k="127"/><hkern g1="comma,period,ellipsis" g2="X" k="9"/><hkern g1="comma,period,ellipsis" g2="eight" k="1"/><hkern g1="comma,period,ellipsis" g2="four" k="23"/><hkern g1="comma,period,ellipsis" g2="germandbls" k="9"/><hkern g1="comma,period,ellipsis" g2="nine" k="18"/><hkern g1="comma,period,ellipsis" g2="one" k="88"/><hkern g1="comma,period,ellipsis" g2="seven" k="47"/><hkern g1="comma,period,ellipsis" g2="v" k="72"/><hkern g1="comma,period,ellipsis" g2="Lslash" k="9"/><hkern g1="comma,period,ellipsis" g2="lslash" k="36"/><hkern g1="comma,period,ellipsis" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="39"/><hkern g1="comma,period,ellipsis" g2="Oslash" k="38"/><hkern g1="comma,period,ellipsis" g2="S,Scaron" k="10"/><hkern g1="comma,period,ellipsis" g2="T" k="118"/><hkern g1="comma,period,ellipsis" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="39"/><hkern g1="comma,period,ellipsis" g2="W" k="82"/><hkern g1="comma,period,ellipsis" g2="Y,Yacute,Ydieresis" k="130"/><hkern g1="comma,period,ellipsis" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="9"/><hkern g1="comma,period,ellipsis" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="20"/><hkern g1="comma,period,ellipsis" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="18"/><hkern g1="comma,period,ellipsis" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="20"/><hkern g1="comma,period,ellipsis" g2="oslash" k="9"/><hkern g1="comma,period,ellipsis" g2="t" k="29"/><hkern g1="comma,period,ellipsis" g2="u,ugrave,uacute,ucircumflex,udieresis" k="1"/><hkern g1="comma,period,ellipsis" g2="w" k="49"/><hkern g1="comma,period,ellipsis" g2="y,yacute,ydieresis" k="81"/><hkern g1="comma,period,ellipsis" g2="zero,six" k="20"/><hkern g1="comma,period,ellipsis" g2="Eth" k="18"/><hkern g1="comma,period,ellipsis" g2="AE" k="9"/><hkern g1="comma,period,ellipsis" g2="b,h,k,l,thorn" k="9"/><hkern g1="quotedbl,quotesingle" g2="V" k="-11"/><hkern g1="quotedbl,quotesingle" g2="X" k="-1"/><hkern g1="quotedbl,quotesingle" g2="five" k="9"/><hkern g1="quotedbl,quotesingle" g2="four" k="50"/><hkern g1="quotedbl,quotesingle" g2="germandbls" k="9"/><hkern g1="quotedbl,quotesingle" g2="one" k="-3"/><hkern g1="quotedbl,quotesingle" g2="one.ss05" k="-7"/><hkern g1="quotedbl,quotesingle" g2="seven" k="-29"/><hkern g1="quotedbl,quotesingle" g2="three" k="-9"/><hkern g1="quotedbl,quotesingle" g2="v" k="-19"/><hkern g1="quotedbl,quotesingle" g2="x" k="-1"/><hkern g1="quotedbl,quotesingle" g2="two" k="-1"/><hkern g1="quotedbl,quotesingle" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="78"/><hkern g1="quotedbl,quotesingle" g2="J" k="4"/><hkern g1="quotedbl,quotesingle" g2="T" k="-21"/><hkern g1="quotedbl,quotesingle" g2="W" k="-2"/><hkern g1="quotedbl,quotesingle" g2="Y,Yacute,Ydieresis" k="-1"/><hkern g1="quotedbl,quotesingle" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="19"/><hkern g1="quotedbl,quotesingle" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="29"/><hkern g1="quotedbl,quotesingle" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-38"/><hkern g1="quotedbl,quotesingle" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="29"/><hkern g1="quotedbl,quotesingle" g2="oslash" k="29"/><hkern g1="quotedbl,quotesingle" g2="w" k="-19"/><hkern g1="quotedbl,quotesingle" g2="y,yacute,ydieresis" k="-10"/><hkern g1="quotedbl,quotesingle" g2="zero,six" k="9"/><hkern g1="quotedbl,quotesingle" g2="AE" k="10"/><hkern g1="quotesinglbase,quotedblbase" g2="V" k="116"/><hkern g1="quotesinglbase,quotedblbase" g2="eight" k="9"/><hkern g1="quotesinglbase,quotedblbase" g2="five" k="9"/><hkern g1="quotesinglbase,quotedblbase" g2="florin" k="-29"/><hkern g1="quotesinglbase,quotedblbase" g2="four" k="12"/><hkern g1="quotesinglbase,quotedblbase" g2="germandbls" k="9"/><hkern g1="quotesinglbase,quotedblbase" g2="one" k="58"/><hkern g1="quotesinglbase,quotedblbase" g2="seven" k="29"/><hkern g1="quotesinglbase,quotedblbase" g2="three" k="2"/><hkern g1="quotesinglbase,quotedblbase" g2="v" k="58"/><hkern g1="quotesinglbase,quotedblbase" g2="x" k="-10"/><hkern g1="quotesinglbase,quotedblbase" g2="two" k="-1"/><hkern g1="quotesinglbase,quotedblbase" g2="lslash" k="18"/><hkern g1="quotesinglbase,quotedblbase" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="-12"/><hkern g1="quotesinglbase,quotedblbase" g2="J" k="9"/><hkern g1="quotesinglbase,quotedblbase" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="55"/><hkern g1="quotesinglbase,quotedblbase" g2="Oslash" k="28"/><hkern g1="quotesinglbase,quotedblbase" g2="S,Scaron" k="19"/><hkern g1="quotesinglbase,quotedblbase" g2="T" k="98"/><hkern g1="quotesinglbase,quotedblbase" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="54"/><hkern g1="quotesinglbase,quotedblbase" g2="W" k="89"/><hkern g1="quotesinglbase,quotedblbase" g2="Y,Yacute,Ydieresis" k="128"/><hkern g1="quotesinglbase,quotedblbase" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="28"/><hkern g1="quotesinglbase,quotedblbase" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="12"/><hkern g1="quotesinglbase,quotedblbase" g2="j" k="-37"/><hkern g1="quotesinglbase,quotedblbase" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="19"/><hkern g1="quotesinglbase,quotedblbase" g2="oslash" k="9"/><hkern g1="quotesinglbase,quotedblbase" g2="s,scaron" k="5"/><hkern g1="quotesinglbase,quotedblbase" g2="t" k="57"/><hkern g1="quotesinglbase,quotedblbase" g2="u,ugrave,uacute,ucircumflex,udieresis" k="14"/><hkern g1="quotesinglbase,quotedblbase" g2="w" k="49"/><hkern g1="quotesinglbase,quotedblbase" g2="y,yacute,ydieresis" k="50"/><hkern g1="quotesinglbase,quotedblbase" g2="zero,six" k="21"/><hkern g1="quotesinglbase,quotedblbase" g2="Eth" k="27"/><hkern g1="quotesinglbase,quotedblbase" g2="Z,Zcaron" k="-5"/><hkern g1="quotesinglbase,quotedblbase" g2="z,zcaron" k="-9"/><hkern g1="quotesinglbase,quotedblbase" g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn" k="9"/><hkern g1="quotesinglbase,quotedblbase" g2="i,igrave,iacute,icircumflex,idieresis,dotlessi" k="9"/><hkern g1="quoteleft,quotedblleft" g2="V" k="-2"/><hkern g1="quoteleft,quotedblleft" g2="X" k="-1"/><hkern g1="quoteleft,quotedblleft" g2="five" k="9"/><hkern g1="quoteleft,quotedblleft" g2="florin" k="63"/><hkern g1="quoteleft,quotedblleft" g2="four" k="52"/><hkern g1="quoteleft,quotedblleft" g2="one" k="-11"/><hkern g1="quoteleft,quotedblleft" g2="one.ss05" k="-8"/><hkern g1="quoteleft,quotedblleft" g2="seven" k="-29"/><hkern g1="quoteleft,quotedblleft" g2="three" k="-18"/><hkern g1="quoteleft,quotedblleft" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="110"/><hkern g1="quoteleft,quotedblleft" g2="J" k="91"/><hkern g1="quoteleft,quotedblleft" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="19"/><hkern g1="quoteleft,quotedblleft" g2="Oslash" k="18"/><hkern g1="quoteleft,quotedblleft" g2="T" k="-30"/><hkern g1="quoteleft,quotedblleft" g2="W" k="-11"/><hkern g1="quoteleft,quotedblleft" g2="Y,Yacute,Ydieresis" k="-2"/><hkern g1="quoteleft,quotedblleft" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="23"/><hkern g1="quoteleft,quotedblleft" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="31"/><hkern g1="quoteleft,quotedblleft" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-8"/><hkern g1="quoteleft,quotedblleft" g2="j" k="-5"/><hkern g1="quoteleft,quotedblleft" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="36"/><hkern g1="quoteleft,quotedblleft" g2="oslash" k="29"/><hkern g1="quoteleft,quotedblleft" g2="s,scaron" k="10"/><hkern g1="quoteleft,quotedblleft" g2="t" k="-14"/><hkern g1="quoteleft,quotedblleft" g2="u,ugrave,uacute,ucircumflex,udieresis" k="6"/><hkern g1="quoteleft,quotedblleft" g2="w" k="-9"/><hkern g1="quoteleft,quotedblleft" g2="zero,six" k="1"/><hkern g1="quoteleft,quotedblleft" g2="AE" k="180"/><hkern g1="quoteleft,quotedblleft" g2="z,zcaron" k="-5"/><hkern g1="quoteleft,quotedblleft" g2="i,igrave,iacute,icircumflex,idieresis,dotlessi" k="-5"/><hkern g1="quoteleft,quotedblleft" g2="m,n,p,r,ntilde,r.ss03" k="29"/><hkern g1="quoteright,quotedblright" g2="V" k="-2"/><hkern g1="quoteright,quotedblright" g2="X" k="-1"/><hkern g1="quoteright,quotedblright" g2="eight" k="10"/><hkern g1="quoteright,quotedblright" g2="five" k="3"/><hkern g1="quoteright,quotedblright" g2="florin" k="54"/><hkern g1="quoteright,quotedblright" g2="four" k="88"/><hkern g1="quoteright,quotedblright" g2="germandbls" k="18"/><hkern g1="quoteright,quotedblright" g2="one" k="-1"/><hkern g1="quoteright,quotedblright" g2="one.ss05" k="3"/><hkern g1="quoteright,quotedblright" g2="seven" k="-29"/><hkern g1="quoteright,quotedblright" g2="three" k="-18"/><hkern g1="quoteright,quotedblright" g2="v" k="-9"/><hkern g1="quoteright,quotedblright" g2="Lslash" k="27"/><hkern g1="quoteright,quotedblright" g2="two" k="1"/><hkern g1="quoteright,quotedblright" g2="lslash" k="3"/><hkern g1="quoteright,quotedblright" g2="question" k="-2"/><hkern g1="quoteright,quotedblright" g2="questiondown" k="6"/><hkern g1="quoteright,quotedblright" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="110"/><hkern g1="quoteright,quotedblright" g2="J" k="51"/><hkern g1="quoteright,quotedblright" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="19"/><hkern g1="quoteright,quotedblright" g2="Oslash" k="18"/><hkern g1="quoteright,quotedblright" g2="S,Scaron" k="5"/><hkern g1="quoteright,quotedblright" g2="T" k="-30"/><hkern g1="quoteright,quotedblright" g2="W" k="-11"/><hkern g1="quoteright,quotedblright" g2="Y,Yacute,Ydieresis" k="-2"/><hkern g1="quoteright,quotedblright" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="31"/><hkern g1="quoteright,quotedblright" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="60"/><hkern g1="quoteright,quotedblright" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-9"/><hkern g1="quoteright,quotedblright" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="78"/><hkern g1="quoteright,quotedblright" g2="oslash" k="74"/><hkern g1="quoteright,quotedblright" g2="s,scaron" k="55"/><hkern g1="quoteright,quotedblright" g2="u,ugrave,uacute,ucircumflex,udieresis" k="5"/><hkern g1="quoteright,quotedblright" g2="w" k="-9"/><hkern g1="quoteright,quotedblright" g2="zero,six" k="25"/><hkern g1="quoteright,quotedblright" g2="Eth" k="18"/><hkern g1="quoteright,quotedblright" g2="AE" k="139"/><hkern g1="quoteright,quotedblright" g2="m,n,p,r,ntilde,r.ss03" k="22"/><hkern g1="quoteright,quotedblright" g2="comma,period,ellipsis" k="7"/><hkern g1="zero,nine" g2="eight" k="5"/><hkern g1="zero,nine" g2="five" k="1"/><hkern g1="zero,nine" g2="minus" k="-10"/><hkern g1="zero,nine" g2="one" k="14"/><hkern g1="zero,nine" g2="one.ss05" k="6"/><hkern g1="zero,nine" g2="plus" k="-9"/><hkern g1="zero,nine" g2="three" k="1"/><hkern g1="zero,nine" g2="two" k="5"/><hkern g1="zero,nine" g2="parenright" k="20"/><hkern g1="zero,nine" g2="questiondown" k="9"/><hkern g1="zero,nine" g2="ampersand" k="18"/><hkern g1="zero,nine" g2="ampersand.ss04" k="11"/><hkern g1="zero,nine" g2="approxequal" k="-11"/><hkern g1="zero,nine" g2="asciitilde" k="-10"/><hkern g1="zero,nine" g2="backslash" k="40"/><hkern g1="zero,nine" g2="divide" k="-9"/><hkern g1="zero,nine" g2="uni2215" k="29"/><hkern g1="zero,nine" g2="equal" k="-10"/><hkern g1="zero,nine" g2="florin.tf" k="43"/><hkern g1="zero,nine" g2="greater" k="-9"/><hkern g1="zero,nine" g2="greaterequal" k="-1"/><hkern g1="zero,nine" g2="infinity" k="-1"/><hkern g1="zero,nine" g2="lessequal" k="-1"/><hkern g1="zero,nine" g2="notequal" k="-1"/><hkern g1="zero,nine" g2="numbersign" k="11"/><hkern g1="zero,nine" g2="parenright.case" k="20"/><hkern g1="zero,nine" g2="percent" k="20"/><hkern g1="zero,nine" g2="perthousand" k="18"/><hkern g1="zero,nine" g2="slash" k="22"/><hkern g1="zero,nine" g2="summation" k="2"/><hkern g1="zero,nine" g2="trademark" k="50"/><hkern g1="zero,nine" g2="underscore" k="20"/><hkern g1="zero,nine" g2="uni2077" k="4"/><hkern g1="zero,nine" g2="uni2084" k="7"/><hkern g1="zero,nine" g2="hyphen,uni00AD,endash,emdash" k="-9"/><hkern g1="zero,nine" g2="zero,six" k="1"/><hkern g1="zero,nine" g2="bracketright,braceright" k="9"/><hkern g1="zero,nine" g2="comma,period,ellipsis" k="40"/><hkern g1="zero,nine" g2="bracketright.case,braceright.case" k="10"/><hkern g1="zero,nine" g2="hyphen.case,endash.case,emdash.case" k="-9"/><hkern g1="zero,nine" g2="quotedbl,quotesingle" k="9"/><hkern g1="zero,nine" g2="quotesinglbase,quotedblbase" k="20"/><hkern g1="zero,nine" g2="quoteleft,quotedblleft" k="1"/><hkern g1="zero,nine" g2="quoteright,quotedblright" k="1"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="V" k="64"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="X" k="27"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="a.ordn" k="25"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="ampersand" k="37"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="ampersand.ss04" k="10"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="asciicircum" k="56"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="asciitilde" k="20"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="asterisk" k="74"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="at" k="48"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="b.ordn" k="41"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="backslash" k="79"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="braceleft" k="2"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="braceleft.case" k="4"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="bracketleft.case" k="2"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="bullet" k="39"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="c.ordn" k="25"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="d.ordn" k="25"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="dagger" k="39"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="daggerdbl" k="9"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="divide" k="19"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="e.ordn" k="25"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="equal" k="18"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="exclam" k="18"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="exclamdown" k="9"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="f.ordn" k="42"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="g.ordn" k="23"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="germandbls" k="1"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="greater" k="-18"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="h.ordn" k="22"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="i.ordn" k="23"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="j.ordn" k="13"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="k.ordn" k="23"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="l.ordn" k="23"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="less" k="20"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="m.ordn" k="25"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="multiply" k="9"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="n.ordn" k="25"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="o.ordn" k="43"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="onesuperior" k="91"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="ordfeminine" k="65"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="ordmasculine" k="63"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="p.ordn" k="23"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="paragraph" k="47"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="parenleft" k="1"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="parenleft.case" k="2"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="parenright" k="18"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="periodcentered" k="38"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="plus" k="29"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="q.ordn" k="45"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="question" k="59"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="questiondown" k="-21"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="r.ordn" k="40"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="registered.ss06" k="109"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="s.ordn" k="24"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="section" k="9"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="slash" k="-20"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="t.ordn" k="25"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="threesuperior" k="100"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="trademark" k="125"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="twosuperior" k="90"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="u.ordn" k="24"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="underscore" k="-30"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="uni2070" k="102"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="uni2074" k="100"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="uni2075" k="99"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="uni2076" k="99"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="uni2077" k="99"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="uni2078" k="100"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="uni2079" k="100"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="v" k="48"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="v.ordn" k="42"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="w.ordn" k="42"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="x.ordn" k="22"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="y.ordn" k="30"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="z.ordn" k="22"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="Eth" k="9"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="J" k="-1"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="39"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="Oslash" k="29"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="S,Scaron" k="1"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="T" k="65"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="24"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="W" k="41"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="Y,Yacute,Ydieresis" k="88"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="Z,Zcaron" k="-14"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="5"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="11"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="bracketright,braceright" k="9"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="39"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="colon,semicolon" k="9"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="hyphen,uni00AD,endash,emdash" k="38"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="hyphen.case,endash.case,emdash.case" k="60"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="18"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="guillemotleft,guilsinglleft" k="38"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="guillemotleft.case,guilsinglleft.case" k="66"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="guillemotright,guilsinglright" k="9"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="guillemotright.case,guilsinglright.case" k="19"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="m,n,p,r,ntilde,r.ss03" k="1"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="10"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="oslash" k="5"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="quotedbl,quotesingle" k="76"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="quotesinglbase,quotedblbase" k="-1"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="quoteleft,quotedblleft" k="110"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="quoteright,quotedblright" k="110"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="t" k="21"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="u,ugrave,uacute,ucircumflex,udieresis" k="16"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="w" k="30"/><hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" g2="y,yacute,ydieresis" k="48"/><hkern g1="C,Ccedilla" g2="V" k="19"/><hkern g1="C,Ccedilla" g2="X" k="33"/><hkern g1="C,Ccedilla" g2="ampersand" k="20"/><hkern g1="C,Ccedilla" g2="ampersand.ss04" k="2"/><hkern g1="C,Ccedilla" g2="asciicircum" k="1"/><hkern g1="C,Ccedilla" g2="at" k="3"/><hkern g1="C,Ccedilla" g2="backslash" k="39"/><hkern g1="C,Ccedilla" g2="braceleft" k="2"/><hkern g1="C,Ccedilla" g2="braceleft.case" k="2"/><hkern g1="C,Ccedilla" g2="germandbls" k="20"/><hkern g1="C,Ccedilla" g2="greater" k="-1"/><hkern g1="C,Ccedilla" g2="ordfeminine" k="11"/><hkern g1="C,Ccedilla" g2="ordmasculine" k="1"/><hkern g1="C,Ccedilla" g2="parenright" k="27"/><hkern g1="C,Ccedilla" g2="plus" k="9"/><hkern g1="C,Ccedilla" g2="questiondown" k="38"/><hkern g1="C,Ccedilla" g2="section" k="10"/><hkern g1="C,Ccedilla" g2="slash" k="39"/><hkern g1="C,Ccedilla" g2="threesuperior" k="9"/><hkern g1="C,Ccedilla" g2="trademark" k="10"/><hkern g1="C,Ccedilla" g2="underscore" k="19"/><hkern g1="C,Ccedilla" g2="v" k="10"/><hkern g1="C,Ccedilla" g2="florin" k="40"/><hkern g1="C,Ccedilla" g2="parenright.case" k="27"/><hkern g1="C,Ccedilla" g2="questiondown.case" k="-2"/><hkern g1="C,Ccedilla" g2="uni2080" k="20"/><hkern g1="C,Ccedilla" g2="uni2081" k="9"/><hkern g1="C,Ccedilla" g2="uni2082" k="18"/><hkern g1="C,Ccedilla" g2="uni2083" k="11"/><hkern g1="C,Ccedilla" g2="uni2084" k="67"/><hkern g1="C,Ccedilla" g2="uni2085" k="38"/><hkern g1="C,Ccedilla" g2="uni2086" k="38"/><hkern g1="C,Ccedilla" g2="uni2087" k="18"/><hkern g1="C,Ccedilla" g2="uni2088" k="27"/><hkern g1="C,Ccedilla" g2="uni2089" k="18"/><hkern g1="C,Ccedilla" g2="x" k="6"/><hkern g1="C,Ccedilla" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="6"/><hkern g1="C,Ccedilla" g2="Oslash" k="5"/><hkern g1="C,Ccedilla" g2="T" k="10"/><hkern g1="C,Ccedilla" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="9"/><hkern g1="C,Ccedilla" g2="W" k="20"/><hkern g1="C,Ccedilla" g2="Y,Yacute,Ydieresis" k="34"/><hkern g1="C,Ccedilla" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="11"/><hkern g1="C,Ccedilla" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="16"/><hkern g1="C,Ccedilla" g2="bracketright,braceright" k="26"/><hkern g1="C,Ccedilla" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="1"/><hkern g1="C,Ccedilla" g2="hyphen,uni00AD,endash,emdash" k="9"/><hkern g1="C,Ccedilla" g2="hyphen.case,endash.case,emdash.case" k="11"/><hkern g1="C,Ccedilla" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="6"/><hkern g1="C,Ccedilla" g2="guillemotleft,guilsinglleft" k="28"/><hkern g1="C,Ccedilla" g2="guillemotleft.case,guilsinglleft.case" k="20"/><hkern g1="C,Ccedilla" g2="guillemotright,guilsinglright" k="9"/><hkern g1="C,Ccedilla" g2="guillemotright.case,guilsinglright.case" k="9"/><hkern g1="C,Ccedilla" g2="m,n,p,r,ntilde,r.ss03" k="6"/><hkern g1="C,Ccedilla" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="16"/><hkern g1="C,Ccedilla" g2="oslash" k="11"/><hkern g1="C,Ccedilla" g2="quotesinglbase,quotedblbase" k="27"/><hkern g1="C,Ccedilla" g2="quoteleft,quotedblleft" k="9"/><hkern g1="C,Ccedilla" g2="quoteright,quotedblright" k="9"/><hkern g1="C,Ccedilla" g2="t" k="10"/><hkern g1="C,Ccedilla" g2="u,ugrave,uacute,ucircumflex,udieresis" k="20"/><hkern g1="C,Ccedilla" g2="w" k="10"/><hkern g1="C,Ccedilla" g2="y,yacute,ydieresis" k="11"/><hkern g1="C,Ccedilla" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="34"/><hkern g1="C,Ccedilla" g2="AE" k="72"/><hkern g1="C,Ccedilla" g2="bracketright.case,braceright.case" k="27"/><hkern g1="C,Ccedilla" g2="b,h,k,l,thorn" k="10"/><hkern g1="C,Ccedilla" g2="comma,period,ellipsis" k="28"/><hkern g1="C,Ccedilla" g2="s,scaron" k="1"/><hkern g1="C,Ccedilla" g2="z,zcaron" k="1"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="V" k="-5"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="X" k="-5"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="ampersand" k="18"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="at" k="3"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="braceleft" k="12"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="braceleft.case" k="3"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="dagger" k="-9"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="daggerdbl" k="-9"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="germandbls" k="7"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="greater" k="-19"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="ordfeminine" k="10"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="ordmasculine" k="10"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="parenright" k="-1"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="plus" k="9"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="registered.ss06" k="-9"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="v" k="11"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="J" k="19"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="1"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="Oslash" k="1"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="T" k="-18"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="W" k="-5"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="Y,Yacute,Ydieresis" k="-9"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="Z,Zcaron" k="-18"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="2"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="7"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="bracketright,braceright" k="-2"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="hyphen,uni00AD,endash,emdash" k="1"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="hyphen.case,endash.case,emdash.case" k="1"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="1"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="guillemotleft,guilsinglleft" k="20"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="guillemotleft.case,guilsinglleft.case" k="12"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="guillemotright,guilsinglright" k="1"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="m,n,p,r,ntilde,r.ss03" k="2"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="7"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="oslash" k="7"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="quoteright,quotedblright" k="1"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="u,ugrave,uacute,ucircumflex,udieresis" k="6"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="w" k="6"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="y,yacute,ydieresis" k="11"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="bracketright.case,braceright.case" k="-1"/><hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" g2="s,scaron" k="1"/><hkern g1="G" g2="V" k="20"/><hkern g1="G" g2="X" k="5"/><hkern g1="G" g2="ampersand" k="20"/><hkern g1="G" g2="ampersand.ss04" k="9"/><hkern g1="G" g2="asciitilde" k="-1"/><hkern g1="G" g2="backslash" k="47"/><hkern g1="G" g2="braceleft" k="1"/><hkern g1="G" g2="braceleft.case" k="2"/><hkern g1="G" g2="exclamdown" k="9"/><hkern g1="G" g2="germandbls" k="9"/><hkern g1="G" g2="greater" k="-11"/><hkern g1="G" g2="ordmasculine" k="9"/><hkern g1="G" g2="parenright" k="9"/><hkern g1="G" g2="question" k="9"/><hkern g1="G" g2="questiondown" k="-2"/><hkern g1="G" g2="registered.ss06" k="9"/><hkern g1="G" g2="slash" k="-1"/><hkern g1="G" g2="trademark" k="19"/><hkern g1="G" g2="underscore" k="-10"/><hkern g1="G" g2="parenright.case" k="9"/><hkern g1="G" g2="uni2084" k="9"/><hkern g1="G" g2="x" k="-5"/><hkern g1="G" g2="lslash" k="-2"/><hkern g1="G" g2="T" k="14"/><hkern g1="G" g2="W" k="19"/><hkern g1="G" g2="Y,Yacute,Ydieresis" k="35"/><hkern g1="G" g2="bracketright,braceright" k="9"/><hkern g1="G" g2="hyphen.case,endash.case,emdash.case" k="1"/><hkern g1="G" g2="guillemotleft,guilsinglleft" k="19"/><hkern g1="G" g2="guillemotleft.case,guilsinglleft.case" k="9"/><hkern g1="G" g2="guillemotright,guilsinglright" k="9"/><hkern g1="G" g2="guillemotright.case,guilsinglright.case" k="9"/><hkern g1="G" g2="quoteleft,quotedblleft" k="9"/><hkern g1="G" g2="quoteright,quotedblright" k="10"/><hkern g1="G" g2="t" k="-3"/><hkern g1="G" g2="u,ugrave,uacute,ucircumflex,udieresis" k="1"/><hkern g1="G" g2="AE" k="1"/><hkern g1="G" g2="bracketright.case,braceright.case" k="9"/><hkern g1="G" g2="s,scaron" k="-9"/><hkern g1="G.ss01" g2="V" k="16"/><hkern g1="G.ss01" g2="X" k="33"/><hkern g1="G.ss01" g2="ampersand" k="19"/><hkern g1="G.ss01" g2="asciicircum" k="-9"/><hkern g1="G.ss01" g2="asciitilde" k="-9"/><hkern g1="G.ss01" g2="asterisk" k="1"/><hkern g1="G.ss01" g2="backslash" k="48"/><hkern g1="G.ss01" g2="dagger" k="-9"/><hkern g1="G.ss01" g2="daggerdbl" k="-9"/><hkern g1="G.ss01" g2="divide" k="-9"/><hkern g1="G.ss01" g2="equal" k="-9"/><hkern g1="G.ss01" g2="exclamdown" k="9"/><hkern g1="G.ss01" g2="germandbls" k="10"/><hkern g1="G.ss01" g2="greater" k="-9"/><hkern g1="G.ss01" g2="multiply" k="-9"/><hkern g1="G.ss01" g2="ordfeminine" k="1"/><hkern g1="G.ss01" g2="parenright" k="28"/><hkern g1="G.ss01" g2="plus" k="-9"/><hkern g1="G.ss01" g2="questiondown" k="38"/><hkern g1="G.ss01" g2="slash" k="48"/><hkern g1="G.ss01" g2="trademark" k="22"/><hkern g1="G.ss01" g2="underscore" k="38"/><hkern g1="G.ss01" g2="uni2077" k="1"/><hkern g1="G.ss01" g2="florin" k="29"/><hkern g1="G.ss01" g2="parenright.case" k="28"/><hkern g1="G.ss01" g2="uni2081" k="9"/><hkern g1="G.ss01" g2="uni2082" k="18"/><hkern g1="G.ss01" g2="uni2083" k="18"/><hkern g1="G.ss01" g2="uni2084" k="39"/><hkern g1="G.ss01" g2="uni2085" k="28"/><hkern g1="G.ss01" g2="uni2086" k="18"/><hkern g1="G.ss01" g2="uni2088" k="9"/><hkern g1="G.ss01" g2="uni2089" k="9"/><hkern g1="G.ss01" g2="x" k="-5"/><hkern g1="G.ss01" g2="lslash" k="-15"/><hkern g1="G.ss01" g2="Lslash" k="-1"/><hkern g1="G.ss01" g2="Eth" k="-1"/><hkern g1="G.ss01" g2="J" k="-1"/><hkern g1="G.ss01" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="-5"/><hkern g1="G.ss01" g2="S,Scaron" k="-5"/><hkern g1="G.ss01" g2="T" k="7"/><hkern g1="G.ss01" g2="W" k="20"/><hkern g1="G.ss01" g2="Y,Yacute,Ydieresis" k="40"/><hkern g1="G.ss01" g2="Z,Zcaron" k="5"/><hkern g1="G.ss01" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="-9"/><hkern g1="G.ss01" g2="bracketright,braceright" k="28"/><hkern g1="G.ss01" g2="hyphen,uni00AD,endash,emdash" k="-1"/><hkern g1="G.ss01" g2="hyphen.case,endash.case,emdash.case" k="-18"/><hkern g1="G.ss01" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-14"/><hkern g1="G.ss01" g2="guillemotright,guilsinglright" k="9"/><hkern g1="G.ss01" g2="guillemotright.case,guilsinglright.case" k="9"/><hkern g1="G.ss01" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="-5"/><hkern g1="G.ss01" g2="quotesinglbase,quotedblbase" k="37"/><hkern g1="G.ss01" g2="quoteleft,quotedblleft" k="9"/><hkern g1="G.ss01" g2="t" k="-21"/><hkern g1="G.ss01" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="29"/><hkern g1="G.ss01" g2="AE" k="79"/><hkern g1="G.ss01" g2="bracketright.case,braceright.case" k="28"/><hkern g1="G.ss01" g2="comma,period,ellipsis" k="37"/><hkern g1="G.ss01" g2="s,scaron" k="-10"/><hkern g1="G.ss01" g2="i,igrave,iacute,icircumflex,idieresis,dotlessi" k="-5"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="ampersand" k="9"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="ampersand.ss04" k="18"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="at" k="1"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="braceleft" k="3"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="braceleft.case" k="3"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="bullet" k="1"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="germandbls" k="14"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="greater" k="-1"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="ordfeminine" k="18"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="ordmasculine" k="9"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="questiondown" k="3"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="v" k="5"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="uni2084" k="21"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="uni2085" k="20"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="uni2086" k="30"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="uni2087" k="10"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="uni2088" k="28"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="uni2089" k="28"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="lslash" k="9"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="1"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="9"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="guillemotleft,guilsinglleft" k="1"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="guillemotleft.case,guilsinglleft.case" k="1"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="5"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="oslash" k="9"/><hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" g2="w" k="5"/><hkern g1="K" g2="V" k="2"/><hkern g1="K" g2="a.ordn" k="12"/><hkern g1="K" g2="ampersand" k="33"/><hkern g1="K" g2="ampersand.ss04" k="21"/><hkern g1="K" g2="asciicircum" k="20"/><hkern g1="K" g2="asciitilde" k="69"/><hkern g1="K" g2="asterisk" k="1"/><hkern g1="K" g2="at" k="42"/><hkern g1="K" g2="b.ordn" k="9"/><hkern g1="K" g2="backslash" k="-18"/><hkern g1="K" g2="braceleft" k="49"/><hkern g1="K" g2="braceleft.case" k="42"/><hkern g1="K" g2="bullet" k="41"/><hkern g1="K" g2="c.ordn" k="12"/><hkern g1="K" g2="d.ordn" k="12"/><hkern g1="K" g2="divide" k="30"/><hkern g1="K" g2="e.ordn" k="12"/><hkern g1="K" g2="equal" k="23"/><hkern g1="K" g2="f.ordn" k="9"/><hkern g1="K" g2="g.ordn" k="12"/><hkern g1="K" g2="germandbls" k="22"/><hkern g1="K" g2="greater" k="-27"/><hkern g1="K" g2="h.ordn" k="9"/><hkern g1="K" g2="i.ordn" k="9"/><hkern g1="K" g2="j.ordn" k="12"/><hkern g1="K" g2="k.ordn" k="9"/><hkern g1="K" g2="l.ordn" k="9"/><hkern g1="K" g2="less" k="68"/><hkern g1="K" g2="m.ordn" k="11"/><hkern g1="K" g2="multiply" k="10"/><hkern g1="K" g2="n.ordn" k="11"/><hkern g1="K" g2="o.ordn" k="12"/><hkern g1="K" g2="onesuperior" k="2"/><hkern g1="K" g2="ordfeminine" k="40"/><hkern g1="K" g2="ordmasculine" k="39"/><hkern g1="K" g2="p.ordn" k="10"/><hkern g1="K" g2="paragraph" k="36"/><hkern g1="K" g2="parenleft" k="18"/><hkern g1="K" g2="parenleft.case" k="20"/><hkern g1="K" g2="parenright" k="-1"/><hkern g1="K" g2="periodcentered" k="40"/><hkern g1="K" g2="plus" k="31"/><hkern g1="K" g2="q.ordn" k="12"/><hkern g1="K" g2="question" k="19"/><hkern g1="K" g2="questiondown" k="-28"/><hkern g1="K" g2="r.ordn" k="9"/><hkern g1="K" g2="registered.ss06" k="39"/><hkern g1="K" g2="s.ordn" k="9"/><hkern g1="K" g2="section" k="11"/><hkern g1="K" g2="slash" k="-11"/><hkern g1="K" g2="t.ordn" k="9"/><hkern g1="K" g2="threesuperior" k="11"/><hkern g1="K" g2="trademark" k="-9"/><hkern g1="K" g2="twosuperior" k="11"/><hkern g1="K" g2="underscore" k="-29"/><hkern g1="K" g2="uni2070" k="13"/><hkern g1="K" g2="uni2074" k="43"/><hkern g1="K" g2="uni2075" k="13"/><hkern g1="K" g2="uni2076" k="40"/><hkern g1="K" g2="uni2077" k="11"/><hkern g1="K" g2="uni2078" k="12"/><hkern g1="K" g2="uni2079" k="13"/><hkern g1="K" g2="v" k="72"/><hkern g1="K" g2="z.ordn" k="9"/><hkern g1="K" g2="florin" k="5"/><hkern g1="K" g2="parenright.case" k="-1"/><hkern g1="K" g2="questiondown.case" k="10"/><hkern g1="K" g2="uni2087" k="18"/><hkern g1="K" g2="x" k="1"/><hkern g1="K" g2="lslash" k="9"/><hkern g1="K" g2="Eth" k="27"/><hkern g1="K" g2="J" k="19"/><hkern g1="K" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="51"/><hkern g1="K" g2="Oslash" k="38"/><hkern g1="K" g2="S,Scaron" k="15"/><hkern g1="K" g2="T" k="-5"/><hkern g1="K" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="6"/><hkern g1="K" g2="W" k="2"/><hkern g1="K" g2="Z,Zcaron" k="-9"/><hkern g1="K" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="24"/><hkern g1="K" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="40"/><hkern g1="K" g2="bracketright,braceright" k="-1"/><hkern g1="K" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="60"/><hkern g1="K" g2="colon,semicolon" k="10"/><hkern g1="K" g2="hyphen,uni00AD,endash,emdash" k="75"/><hkern g1="K" g2="hyphen.case,endash.case,emdash.case" k="96"/><hkern g1="K" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="25"/><hkern g1="K" g2="guillemotleft,guilsinglleft" k="61"/><hkern g1="K" g2="guillemotleft.case,guilsinglleft.case" k="70"/><hkern g1="K" g2="guillemotright,guilsinglright" k="34"/><hkern g1="K" g2="guillemotright.case,guilsinglright.case" k="37"/><hkern g1="K" g2="m,n,p,r,ntilde,r.ss03" k="11"/><hkern g1="K" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="44"/><hkern g1="K" g2="oslash" k="21"/><hkern g1="K" g2="quoteleft,quotedblleft" k="27"/><hkern g1="K" g2="quoteright,quotedblright" k="9"/><hkern g1="K" g2="t" k="53"/><hkern g1="K" g2="u,ugrave,uacute,ucircumflex,udieresis" k="55"/><hkern g1="K" g2="w" k="54"/><hkern g1="K" g2="y,yacute,ydieresis" k="74"/><hkern g1="K" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="1"/><hkern g1="K" g2="AE" k="5"/><hkern g1="K" g2="bracketright.case,braceright.case" k="-1"/><hkern g1="K" g2="s,scaron" k="10"/><hkern g1="L,Lslash" g2="V" k="69"/><hkern g1="L,Lslash" g2="a.ordn" k="60"/><hkern g1="L,Lslash" g2="ampersand" k="12"/><hkern g1="L,Lslash" g2="asciicircum" k="32"/><hkern g1="L,Lslash" g2="asciitilde" k="5"/><hkern g1="L,Lslash" g2="asterisk" k="70"/><hkern g1="L,Lslash" g2="at" k="7"/><hkern g1="L,Lslash" g2="b.ordn" k="60"/><hkern g1="L,Lslash" g2="backslash" k="97"/><hkern g1="L,Lslash" g2="braceleft" k="3"/><hkern g1="L,Lslash" g2="braceleft.case" k="21"/><hkern g1="L,Lslash" g2="bullet" k="11"/><hkern g1="L,Lslash" g2="c.ordn" k="60"/><hkern g1="L,Lslash" g2="d.ordn" k="60"/><hkern g1="L,Lslash" g2="dagger" k="18"/><hkern g1="L,Lslash" g2="divide" k="-8"/><hkern g1="L,Lslash" g2="e.ordn" k="60"/><hkern g1="L,Lslash" g2="equal" k="-7"/><hkern g1="L,Lslash" g2="f.ordn" k="60"/><hkern g1="L,Lslash" g2="g.ordn" k="42"/><hkern g1="L,Lslash" g2="germandbls" k="1"/><hkern g1="L,Lslash" g2="greater" k="-27"/><hkern g1="L,Lslash" g2="h.ordn" k="60"/><hkern g1="L,Lslash" g2="i.ordn" k="60"/><hkern g1="L,Lslash" g2="j.ordn" k="24"/><hkern g1="L,Lslash" g2="k.ordn" k="60"/><hkern g1="L,Lslash" g2="l.ordn" k="60"/><hkern g1="L,Lslash" g2="less" k="13"/><hkern g1="L,Lslash" g2="m.ordn" k="60"/><hkern g1="L,Lslash" g2="multiply" k="-9"/><hkern g1="L,Lslash" g2="n.ordn" k="60"/><hkern g1="L,Lslash" g2="o.ordn" k="60"/><hkern g1="L,Lslash" g2="onesuperior" k="43"/><hkern g1="L,Lslash" g2="ordfeminine" k="44"/><hkern g1="L,Lslash" g2="ordmasculine" k="44"/><hkern g1="L,Lslash" g2="p.ordn" k="40"/><hkern g1="L,Lslash" g2="paragraph" k="41"/><hkern g1="L,Lslash" g2="parenleft.case" k="2"/><hkern g1="L,Lslash" g2="parenright" k="-1"/><hkern g1="L,Lslash" g2="periodcentered" k="18"/><hkern g1="L,Lslash" g2="plus" k="11"/><hkern g1="L,Lslash" g2="q.ordn" k="60"/><hkern g1="L,Lslash" g2="question" k="59"/><hkern g1="L,Lslash" g2="questiondown" k="-21"/><hkern g1="L,Lslash" g2="r.ordn" k="60"/><hkern g1="L,Lslash" g2="registered.ss06" k="82"/><hkern g1="L,Lslash" g2="s.ordn" k="60"/><hkern g1="L,Lslash" g2="slash" k="-31"/><hkern g1="L,Lslash" g2="t.ordn" k="60"/><hkern g1="L,Lslash" g2="threesuperior" k="43"/><hkern g1="L,Lslash" g2="trademark" k="119"/><hkern g1="L,Lslash" g2="twosuperior" k="43"/><hkern g1="L,Lslash" g2="u.ordn" k="60"/><hkern g1="L,Lslash" g2="underscore" k="-39"/><hkern g1="L,Lslash" g2="uni2070" k="44"/><hkern g1="L,Lslash" g2="uni2074" k="66"/><hkern g1="L,Lslash" g2="uni2075" k="61"/><hkern g1="L,Lslash" g2="uni2076" k="61"/><hkern g1="L,Lslash" g2="uni2077" k="43"/><hkern g1="L,Lslash" g2="uni2078" k="43"/><hkern g1="L,Lslash" g2="uni2079" k="43"/><hkern g1="L,Lslash" g2="v" k="49"/><hkern g1="L,Lslash" g2="v.ordn" k="42"/><hkern g1="L,Lslash" g2="w.ordn" k="42"/><hkern g1="L,Lslash" g2="x.ordn" k="42"/><hkern g1="L,Lslash" g2="y.ordn" k="41"/><hkern g1="L,Lslash" g2="z.ordn" k="24"/><hkern g1="L,Lslash" g2="questiondown.case" k="-18"/><hkern g1="L,Lslash" g2="uni2080" k="-1"/><hkern g1="L,Lslash" g2="uni2081" k="-1"/><hkern g1="L,Lslash" g2="uni2088" k="-1"/><hkern g1="L,Lslash" g2="bar" k="-2"/><hkern g1="L,Lslash" g2="brokenbar" k="-2"/><hkern g1="L,Lslash" g2="J" k="-16"/><hkern g1="L,Lslash" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="17"/><hkern g1="L,Lslash" g2="Oslash" k="11"/><hkern g1="L,Lslash" g2="S,Scaron" k="-5"/><hkern g1="L,Lslash" g2="T" k="70"/><hkern g1="L,Lslash" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="10"/><hkern g1="L,Lslash" g2="W" k="46"/><hkern g1="L,Lslash" g2="Y,Yacute,Ydieresis" k="74"/><hkern g1="L,Lslash" g2="Z,Zcaron" k="-27"/><hkern g1="L,Lslash" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="-9"/><hkern g1="L,Lslash" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="1"/><hkern g1="L,Lslash" g2="bracketright,braceright" k="-1"/><hkern g1="L,Lslash" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="11"/><hkern g1="L,Lslash" g2="hyphen,uni00AD,endash,emdash" k="13"/><hkern g1="L,Lslash" g2="hyphen.case,endash.case,emdash.case" k="21"/><hkern g1="L,Lslash" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="10"/><hkern g1="L,Lslash" g2="guillemotleft,guilsinglleft" k="20"/><hkern g1="L,Lslash" g2="guillemotleft.case,guilsinglleft.case" k="21"/><hkern g1="L,Lslash" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="-4"/><hkern g1="L,Lslash" g2="oslash" k="1"/><hkern g1="L,Lslash" g2="quotedbl,quotesingle" k="62"/><hkern g1="L,Lslash" g2="quotesinglbase,quotedblbase" k="-38"/><hkern g1="L,Lslash" g2="quoteleft,quotedblleft" k="88"/><hkern g1="L,Lslash" g2="quoteright,quotedblright" k="78"/><hkern g1="L,Lslash" g2="t" k="29"/><hkern g1="L,Lslash" g2="u,ugrave,uacute,ucircumflex,udieresis" k="11"/><hkern g1="L,Lslash" g2="w" k="36"/><hkern g1="L,Lslash" g2="y,yacute,ydieresis" k="50"/><hkern g1="L,Lslash" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="-2"/><hkern g1="L,Lslash" g2="bracketright.case,braceright.case" k="-1"/><hkern g1="L,Lslash" g2="comma,period,ellipsis" k="-10"/><hkern g1="L,Lslash" g2="s,scaron" k="-14"/><hkern g1="L,Lslash" g2="z,zcaron" k="-14"/><hkern g1="L,Lslash" g2="j" k="-9"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="V" k="25"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="X" k="44"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="ampersand" k="38"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="ampersand.ss04" k="18"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="asciicircum" k="-10"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="asciitilde" k="-10"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="asterisk" k="1"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="backslash" k="49"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="bracketleft.case" k="1"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="dagger" k="-9"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="daggerdbl" k="-9"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="divide" k="-9"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="equal" k="-9"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="exclam" k="9"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="exclamdown" k="9"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="germandbls" k="23"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="greater" k="-10"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="less" k="-9"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="multiply" k="-9"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="onesuperior" k="2"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="ordfeminine" k="10"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="parenright" k="29"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="periodcentered" k="-9"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="plus" k="-9"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="questiondown" k="47"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="slash" k="49"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="t.ordn" k="1"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="threesuperior" k="3"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="trademark" k="31"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="twosuperior" k="2"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="underscore" k="40"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2070" k="2"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2074" k="2"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2075" k="3"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2076" k="3"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2077" k="4"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2078" k="3"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2079" k="3"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="florin" k="42"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="parenright.case" k="29"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2080" k="39"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2081" k="28"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2082" k="38"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2083" k="28"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2084" k="79"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2085" k="58"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2086" k="77"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2087" k="2"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2088" k="48"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="uni2089" k="38"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="lslash" k="-5"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="Lslash" k="-1"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="bracketleft" k="1"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="S,Scaron" k="1"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="T" k="16"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="W" k="25"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="Y,Yacute,Ydieresis" k="50"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="Z,Zcaron" k="10"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="-5"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="bracketright,braceright" k="29"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="-1"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="hyphen.case,endash.case,emdash.case" k="-18"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-14"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="guillemotleft,guilsinglleft" k="10"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="guillemotleft.case,guilsinglleft.case" k="-9"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="guillemotright,guilsinglright" k="10"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="guillemotright.case,guilsinglright.case" k="10"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="-5"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="quotesinglbase,quotedblbase" k="50"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="quoteleft,quotedblleft" k="19"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="quoteright,quotedblright" k="19"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="t" k="-21"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="39"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="AE" k="87"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="bracketright.case,braceright.case" k="29"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="b,h,k,l,thorn" k="5"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="comma,period,ellipsis" k="39"/><hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis" g2="z,zcaron" k="-5"/><hkern g1="Oslash" g2="V" k="25"/><hkern g1="Oslash" g2="X" k="38"/><hkern g1="Oslash" g2="ampersand" k="18"/><hkern g1="Oslash" g2="ampersand.ss04" k="19"/><hkern g1="Oslash" g2="backslash" k="39"/><hkern g1="Oslash" g2="dagger" k="-9"/><hkern g1="Oslash" g2="daggerdbl" k="-9"/><hkern g1="Oslash" g2="equal" k="-9"/><hkern g1="Oslash" g2="germandbls" k="24"/><hkern g1="Oslash" g2="ordfeminine" k="1"/><hkern g1="Oslash" g2="parenright" k="29"/><hkern g1="Oslash" g2="questiondown" k="49"/><hkern g1="Oslash" g2="slash" k="49"/><hkern g1="Oslash" g2="trademark" k="18"/><hkern g1="Oslash" g2="underscore" k="40"/><hkern g1="Oslash" g2="florin" k="31"/><hkern g1="Oslash" g2="parenright.case" k="29"/><hkern g1="Oslash" g2="uni2080" k="18"/><hkern g1="Oslash" g2="uni2081" k="9"/><hkern g1="Oslash" g2="uni2082" k="9"/><hkern g1="Oslash" g2="uni2083" k="9"/><hkern g1="Oslash" g2="uni2084" k="36"/><hkern g1="Oslash" g2="uni2085" k="9"/><hkern g1="Oslash" g2="uni2086" k="36"/><hkern g1="Oslash" g2="uni2087" k="9"/><hkern g1="Oslash" g2="uni2088" k="18"/><hkern g1="Oslash" g2="uni2089" k="18"/><hkern g1="Oslash" g2="lslash" k="-1"/><hkern g1="Oslash" g2="T" k="11"/><hkern g1="Oslash" g2="W" k="29"/><hkern g1="Oslash" g2="Y,Yacute,Ydieresis" k="53"/><hkern g1="Oslash" g2="Z,Zcaron" k="6"/><hkern g1="Oslash" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="1"/><hkern g1="Oslash" g2="bracketright,braceright" k="29"/><hkern g1="Oslash" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-9"/><hkern g1="Oslash" g2="guillemotright,guilsinglright" k="9"/><hkern g1="Oslash" g2="guillemotright.case,guilsinglright.case" k="10"/><hkern g1="Oslash" g2="quotesinglbase,quotedblbase" k="39"/><hkern g1="Oslash" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="34"/><hkern g1="Oslash" g2="AE" k="90"/><hkern g1="Oslash" g2="bracketright.case,braceright.case" k="29"/><hkern g1="Oslash" g2="comma,period,ellipsis" k="39"/><hkern g1="R" g2="V" k="5"/><hkern g1="R" g2="ampersand" k="19"/><hkern g1="R" g2="ampersand.ss04" k="9"/><hkern g1="R" g2="asciicircum" k="-1"/><hkern g1="R" g2="asciitilde" k="1"/><hkern g1="R" g2="asterisk" k="-1"/><hkern g1="R" g2="at" k="6"/><hkern g1="R" g2="backslash" k="32"/><hkern g1="R" g2="braceleft" k="10"/><hkern g1="R" g2="braceleft.case" k="20"/><hkern g1="R" g2="bullet" k="11"/><hkern g1="R" g2="dagger" k="-19"/><hkern g1="R" g2="daggerdbl" k="-19"/><hkern g1="R" g2="divide" k="9"/><hkern g1="R" g2="germandbls" k="2"/><hkern g1="R" g2="greater" k="-21"/><hkern g1="R" g2="j.ordn" k="1"/><hkern g1="R" g2="less" k="1"/><hkern g1="R" g2="multiply" k="-2"/><hkern g1="R" g2="ordfeminine" k="2"/><hkern g1="R" g2="parenright" k="-1"/><hkern g1="R" g2="periodcentered" k="1"/><hkern g1="R" g2="plus" k="10"/><hkern g1="R" g2="registered.ss06" k="-10"/><hkern g1="R" g2="underscore" k="-19"/><hkern g1="R" g2="v.ordn" k="-1"/><hkern g1="R" g2="w.ordn" k="-1"/><hkern g1="R" g2="y.ordn" k="-2"/><hkern g1="R" g2="florin" k="5"/><hkern g1="R" g2="parenright.case" k="-1"/><hkern g1="R" g2="questiondown.case" k="9"/><hkern g1="R" g2="uni2083" k="2"/><hkern g1="R" g2="uni2084" k="2"/><hkern g1="R" g2="uni2085" k="1"/><hkern g1="R" g2="uni2086" k="2"/><hkern g1="R" g2="uni2087" k="3"/><hkern g1="R" g2="uni2088" k="2"/><hkern g1="R" g2="x" k="-1"/><hkern g1="R" g2="lslash" k="5"/><hkern g1="R" g2="J" k="11"/><hkern g1="R" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="5"/><hkern g1="R" g2="Oslash" k="1"/><hkern g1="R" g2="W" k="10"/><hkern g1="R" g2="Y,Yacute,Ydieresis" k="10"/><hkern g1="R" g2="Z,Zcaron" k="-5"/><hkern g1="R" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="10"/><hkern g1="R" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="12"/><hkern g1="R" g2="bracketright,braceright" k="-1"/><hkern g1="R" g2="hyphen,uni00AD,endash,emdash" k="11"/><hkern g1="R" g2="hyphen.case,endash.case,emdash.case" k="1"/><hkern g1="R" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-5"/><hkern g1="R" g2="guillemotleft,guilsinglleft" k="20"/><hkern g1="R" g2="guillemotleft.case,guilsinglleft.case" k="19"/><hkern g1="R" g2="guillemotright.case,guilsinglright.case" k="9"/><hkern g1="R" g2="m,n,p,r,ntilde,r.ss03" k="1"/><hkern g1="R" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="11"/><hkern g1="R" g2="oslash" k="12"/><hkern g1="R" g2="quoteleft,quotedblleft" k="-1"/><hkern g1="R" g2="t" k="-14"/><hkern g1="R" g2="u,ugrave,uacute,ucircumflex,udieresis" k="7"/><hkern g1="R" g2="w" k="1"/><hkern g1="R" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="1"/><hkern g1="R" g2="AE" k="2"/><hkern g1="R" g2="bracketright.case,braceright.case" k="-1"/><hkern g1="R" g2="comma,period,ellipsis" k="19"/><hkern g1="R" g2="s,scaron" k="1"/><hkern g1="S,Scaron" g2="V" k="5"/><hkern g1="S,Scaron" g2="X" k="18"/><hkern g1="S,Scaron" g2="ampersand" k="18"/><hkern g1="S,Scaron" g2="asciicircum" k="1"/><hkern g1="S,Scaron" g2="backslash" k="21"/><hkern g1="S,Scaron" g2="braceleft" k="2"/><hkern g1="S,Scaron" g2="braceleft.case" k="1"/><hkern g1="S,Scaron" g2="daggerdbl" k="-9"/><hkern g1="S,Scaron" g2="exclamdown" k="9"/><hkern g1="S,Scaron" g2="germandbls" k="17"/><hkern g1="S,Scaron" g2="greater" k="-3"/><hkern g1="S,Scaron" g2="j.ordn" k="9"/><hkern g1="S,Scaron" g2="multiply" k="1"/><hkern g1="S,Scaron" g2="ordfeminine" k="19"/><hkern g1="S,Scaron" g2="ordmasculine" k="10"/><hkern g1="S,Scaron" g2="parenright" k="9"/><hkern g1="S,Scaron" g2="questiondown" k="18"/><hkern g1="S,Scaron" g2="slash" k="27"/><hkern g1="S,Scaron" g2="trademark" k="18"/><hkern g1="S,Scaron" g2="underscore" k="1"/><hkern g1="S,Scaron" g2="v" k="20"/><hkern g1="S,Scaron" g2="florin" k="41"/><hkern g1="S,Scaron" g2="parenright.case" k="8"/><hkern g1="S,Scaron" g2="uni2084" k="19"/><hkern g1="S,Scaron" g2="uni2085" k="20"/><hkern g1="S,Scaron" g2="uni2086" k="19"/><hkern g1="S,Scaron" g2="uni2088" k="18"/><hkern g1="S,Scaron" g2="uni2089" k="19"/><hkern g1="S,Scaron" g2="x" k="2"/><hkern g1="S,Scaron" g2="lslash" k="-5"/><hkern g1="S,Scaron" g2="Lslash" k="-1"/><hkern g1="S,Scaron" g2="J" k="-1"/><hkern g1="S,Scaron" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="1"/><hkern g1="S,Scaron" g2="Oslash" k="1"/><hkern g1="S,Scaron" g2="T" k="1"/><hkern g1="S,Scaron" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="1"/><hkern g1="S,Scaron" g2="W" k="5"/><hkern g1="S,Scaron" g2="Y,Yacute,Ydieresis" k="24"/><hkern g1="S,Scaron" g2="Z,Zcaron" k="-9"/><hkern g1="S,Scaron" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="1"/><hkern g1="S,Scaron" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="2"/><hkern g1="S,Scaron" g2="bracketright,braceright" k="8"/><hkern g1="S,Scaron" g2="colon,semicolon" k="9"/><hkern g1="S,Scaron" g2="hyphen,uni00AD,endash,emdash" k="1"/><hkern g1="S,Scaron" g2="hyphen.case,endash.case,emdash.case" k="1"/><hkern g1="S,Scaron" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="2"/><hkern g1="S,Scaron" g2="guillemotleft,guilsinglleft" k="1"/><hkern g1="S,Scaron" g2="guillemotleft.case,guilsinglleft.case" k="9"/><hkern g1="S,Scaron" g2="guillemotright,guilsinglright" k="1"/><hkern g1="S,Scaron" g2="m,n,p,r,ntilde,r.ss03" k="8"/><hkern g1="S,Scaron" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="1"/><hkern g1="S,Scaron" g2="oslash" k="1"/><hkern g1="S,Scaron" g2="quotesinglbase,quotedblbase" k="18"/><hkern g1="S,Scaron" g2="quoteleft,quotedblleft" k="9"/><hkern g1="S,Scaron" g2="t" k="-13"/><hkern g1="S,Scaron" g2="u,ugrave,uacute,ucircumflex,udieresis" k="7"/><hkern g1="S,Scaron" g2="w" k="11"/><hkern g1="S,Scaron" g2="y,yacute,ydieresis" k="21"/><hkern g1="S,Scaron" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="19"/><hkern g1="S,Scaron" g2="AE" k="64"/><hkern g1="S,Scaron" g2="bracketright.case,braceright.case" k="8"/><hkern g1="S,Scaron" g2="b,h,k,l,thorn" k="5"/><hkern g1="S,Scaron" g2="comma,period,ellipsis" k="9"/><hkern g1="S,Scaron" g2="z,zcaron" k="1"/><hkern g1="S,Scaron" g2="i,igrave,iacute,icircumflex,idieresis,dotlessi" k="1"/><hkern g1="S,Scaron" g2="j" k="1"/><hkern g1="T" g2="V" k="-20"/><hkern g1="T" g2="X" k="3"/><hkern g1="T" g2="ampersand" k="39"/><hkern g1="T" g2="ampersand.ss04" k="11"/><hkern g1="T" g2="asciitilde" k="61"/><hkern g1="T" g2="asterisk" k="-20"/><hkern g1="T" g2="at" k="25"/><hkern g1="T" g2="backslash" k="-22"/><hkern g1="T" g2="braceleft" k="39"/><hkern g1="T" g2="braceleft.case" k="22"/><hkern g1="T" g2="bullet" k="88"/><hkern g1="T" g2="c.ordn" k="1"/><hkern g1="T" g2="d.ordn" k="1"/><hkern g1="T" g2="dagger" k="-29"/><hkern g1="T" g2="daggerdbl" k="-28"/><hkern g1="T" g2="divide" k="33"/><hkern g1="T" g2="e.ordn" k="1"/><hkern g1="T" g2="equal" k="30"/><hkern g1="T" g2="exclamdown" k="21"/><hkern g1="T" g2="g.ordn" k="1"/><hkern g1="T" g2="germandbls" k="31"/><hkern g1="T" g2="greater" k="-17"/><hkern g1="T" g2="j.ordn" k="10"/><hkern g1="T" g2="less" k="58"/><hkern g1="T" g2="multiply" k="13"/><hkern g1="T" g2="onesuperior" k="-37"/><hkern g1="T" g2="paragraph" k="-19"/><hkern g1="T" g2="parenright" k="-2"/><hkern g1="T" g2="periodcentered" k="50"/><hkern g1="T" g2="plus" k="59"/><hkern g1="T" g2="question" k="-11"/><hkern g1="T" g2="questiondown" k="87"/><hkern g1="T" g2="r.ordn" k="-2"/><hkern g1="T" g2="registered.ss06" k="-30"/><hkern g1="T" g2="section" k="-2"/><hkern g1="T" g2="slash" k="97"/><hkern g1="T" g2="threesuperior" k="-28"/><hkern g1="T" g2="trademark" k="-21"/><hkern g1="T" g2="twosuperior" k="-28"/><hkern g1="T" g2="u.ordn" k="-18"/><hkern g1="T" g2="underscore" k="49"/><hkern g1="T" g2="uni2070" k="-37"/><hkern g1="T" g2="uni2074" k="-26"/><hkern g1="T" g2="uni2075" k="-27"/><hkern g1="T" g2="uni2076" k="-28"/><hkern g1="T" g2="uni2077" k="-38"/><hkern g1="T" g2="uni2078" k="-28"/><hkern g1="T" g2="uni2079" k="-37"/><hkern g1="T" g2="v" k="52"/><hkern g1="T" g2="v.ordn" k="-38"/><hkern g1="T" g2="w.ordn" k="-38"/><hkern g1="T" g2="x.ordn" k="-38"/><hkern g1="T" g2="y.ordn" k="-38"/><hkern g1="T" g2="z.ordn" k="-36"/><hkern g1="T" g2="florin" k="83"/><hkern g1="T" g2="parenright.case" k="-2"/><hkern g1="T" g2="questiondown.case" k="38"/><hkern g1="T" g2="uni2080" k="118"/><hkern g1="T" g2="uni2081" k="100"/><hkern g1="T" g2="uni2082" k="100"/><hkern g1="T" g2="uni2083" k="100"/><hkern g1="T" g2="uni2084" k="102"/><hkern g1="T" g2="uni2085" k="100"/><hkern g1="T" g2="uni2086" k="100"/><hkern g1="T" g2="uni2087" k="100"/><hkern g1="T" g2="uni2088" k="100"/><hkern g1="T" g2="uni2089" k="100"/><hkern g1="T" g2="x" k="35"/><hkern g1="T" g2="lslash" k="20"/><hkern g1="T" g2="bar" k="-11"/><hkern g1="T" g2="brokenbar" k="-20"/><hkern g1="T" g2="exclamdown.case" k="-9"/><hkern g1="T" g2="J" k="88"/><hkern g1="T" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="16"/><hkern g1="T" g2="Oslash" k="11"/><hkern g1="T" g2="T" k="-20"/><hkern g1="T" g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" k="-5"/><hkern g1="T" g2="W" k="-20"/><hkern g1="T" g2="Y,Yacute,Ydieresis" k="-20"/><hkern g1="T" g2="Z,Zcaron" k="-18"/><hkern g1="T" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="85"/><hkern g1="T" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="62"/><hkern g1="T" g2="bracketright,braceright" k="-2"/><hkern g1="T" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="10"/><hkern g1="T" g2="colon,semicolon" k="47"/><hkern g1="T" g2="hyphen,uni00AD,endash,emdash" k="69"/><hkern g1="T" g2="hyphen.case,endash.case,emdash.case" k="65"/><hkern g1="T" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="4"/><hkern g1="T" g2="guillemotleft,guilsinglleft" k="111"/><hkern g1="T" g2="guillemotleft.case,guilsinglleft.case" k="98"/><hkern g1="T" g2="guillemotright,guilsinglright" k="53"/><hkern g1="T" g2="guillemotright.case,guilsinglright.case" k="21"/><hkern g1="T" g2="m,n,p,r,ntilde,r.ss03" k="78"/><hkern g1="T" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="95"/><hkern g1="T" g2="oslash" k="84"/><hkern g1="T" g2="quotedbl,quotesingle" k="-21"/><hkern g1="T" g2="quotesinglbase,quotedblbase" k="98"/><hkern g1="T" g2="quoteleft,quotedblleft" k="-30"/><hkern g1="T" g2="quoteright,quotedblright" k="-30"/><hkern g1="T" g2="t" k="21"/><hkern g1="T" g2="u,ugrave,uacute,ucircumflex,udieresis" k="77"/><hkern g1="T" g2="w" k="53"/><hkern g1="T" g2="y,yacute,ydieresis" k="52"/><hkern g1="T" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="65"/><hkern g1="T" g2="AE" k="148"/><hkern g1="T" g2="bracketright.case,braceright.case" k="-2"/><hkern g1="T" g2="b,h,k,l,thorn" k="-5"/><hkern g1="T" g2="comma,period,ellipsis" k="118"/><hkern g1="T" g2="s,scaron" k="70"/><hkern g1="T" g2="z,zcaron" k="32"/><hkern g1="T" g2="i,igrave,iacute,icircumflex,idieresis,dotlessi" k="2"/><hkern g1="T" g2="j" k="1"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="X" k="5"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="ampersand" k="19"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="asciitilde" k="1"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="at" k="10"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="backslash" k="9"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="braceleft" k="20"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="braceleft.case" k="29"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="bullet" k="20"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="divide" k="1"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="equal" k="5"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="exclamdown" k="19"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="germandbls" k="19"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="j.ordn" k="10"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="less" k="1"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="ordfeminine" k="9"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="ordmasculine" k="9"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="parenright" k="9"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="periodcentered" k="10"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="plus" k="1"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="questiondown" k="47"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="slash" k="47"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="trademark" k="9"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="underscore" k="20"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="v.ordn" k="-27"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="w.ordn" k="-27"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="x.ordn" k="-27"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="y.ordn" k="-27"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="z.ordn" k="-27"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="florin" k="50"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="parenright.case" k="9"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="uni2080" k="30"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="uni2081" k="20"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="uni2082" k="29"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="uni2083" k="28"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="uni2084" k="58"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="uni2085" k="48"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="uni2086" k="49"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="uni2087" k="19"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="uni2088" k="38"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="uni2089" k="28"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="lslash" k="9"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="exclamdown.case" k="1"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="J" k="5"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="T" k="-5"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="5"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="5"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="bracketright,braceright" k="9"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="colon,semicolon" k="5"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="hyphen,uni00AD,endash,emdash" k="1"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="guillemotleft,guilsinglleft" k="19"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="guillemotleft.case,guilsinglleft.case" k="19"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="guillemotright,guilsinglright" k="9"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="guillemotright.case,guilsinglright.case" k="9"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="m,n,p,r,ntilde,r.ss03" k="5"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="5"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="oslash" k="5"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="quotesinglbase,quotedblbase" k="40"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="u,ugrave,uacute,ucircumflex,udieresis" k="9"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="26"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="AE" k="79"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="bracketright.case,braceright.case" k="9"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="comma,period,ellipsis" k="39"/><hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" g2="s,scaron" k="5"/><hkern g1="W" g2="V" k="-10"/><hkern g1="W" g2="X" k="-10"/><hkern g1="W" g2="ampersand" k="36"/><hkern g1="W" g2="ampersand.ss04" k="19"/><hkern g1="W" g2="asciitilde" k="20"/><hkern g1="W" g2="asterisk" k="-10"/><hkern g1="W" g2="at" k="20"/><hkern g1="W" g2="backslash" k="-20"/><hkern g1="W" g2="braceleft" k="21"/><hkern g1="W" g2="braceleft.case" k="12"/><hkern g1="W" g2="bullet" k="21"/><hkern g1="W" g2="dagger" k="-18"/><hkern g1="W" g2="daggerdbl" k="-9"/><hkern g1="W" g2="divide" k="25"/><hkern g1="W" g2="equal" k="10"/><hkern g1="W" g2="exclamdown" k="28"/><hkern g1="W" g2="germandbls" k="38"/><hkern g1="W" g2="greater" k="-18"/><hkern g1="W" g2="j.ordn" k="9"/><hkern g1="W" g2="less" k="20"/><hkern g1="W" g2="multiply" k="9"/><hkern g1="W" g2="onesuperior" k="-1"/><hkern g1="W" g2="parenright" k="-2"/><hkern g1="W" g2="periodcentered" k="21"/><hkern g1="W" g2="plus" k="30"/><hkern g1="W" g2="questiondown" k="78"/><hkern g1="W" g2="slash" k="78"/><hkern g1="W" g2="t.ordn" k="-18"/><hkern g1="W" g2="trademark" k="-20"/><hkern g1="W" g2="u.ordn" k="-18"/><hkern g1="W" g2="underscore" k="50"/><hkern g1="W" g2="v" k="10"/><hkern g1="W" g2="v.ordn" k="-38"/><hkern g1="W" g2="w.ordn" k="-38"/><hkern g1="W" g2="x.ordn" k="-36"/><hkern g1="W" g2="y.ordn" k="-38"/><hkern g1="W" g2="z.ordn" k="-19"/><hkern g1="W" g2="florin" k="60"/><hkern g1="W" g2="parenright.case" k="-2"/><hkern g1="W" g2="questiondown.case" k="30"/><hkern g1="W" g2="uni2080" k="79"/><hkern g1="W" g2="uni2081" k="67"/><hkern g1="W" g2="uni2082" k="70"/><hkern g1="W" g2="uni2083" k="70"/><hkern g1="W" g2="uni2084" k="100"/><hkern g1="W" g2="uni2085" k="99"/><hkern g1="W" g2="uni2086" k="98"/><hkern g1="W" g2="uni2087" k="59"/><hkern g1="W" g2="uni2088" k="88"/><hkern g1="W" g2="uni2089" k="88"/><hkern g1="W" g2="x" k="5"/><hkern g1="W" g2="lslash" k="9"/><hkern g1="W" g2="bar" k="-11"/><hkern g1="W" g2="brokenbar" k="-11"/><hkern g1="W" g2="Eth" k="5"/><hkern g1="W" g2="J" k="59"/><hkern g1="W" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="25"/><hkern g1="W" g2="Oslash" k="29"/><hkern g1="W" g2="S,Scaron" k="5"/><hkern g1="W" g2="T" k="-20"/><hkern g1="W" g2="W" k="-10"/><hkern g1="W" g2="Y,Yacute,Ydieresis" k="-10"/><hkern g1="W" g2="Z,Zcaron" k="-5"/><hkern g1="W" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="30"/><hkern g1="W" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="40"/><hkern g1="W" g2="bracketright,braceright" k="-2"/><hkern g1="W" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="18"/><hkern g1="W" g2="colon,semicolon" k="19"/><hkern g1="W" g2="hyphen,uni00AD,endash,emdash" k="22"/><hkern g1="W" g2="hyphen.case,endash.case,emdash.case" k="13"/><hkern g1="W" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="5"/><hkern g1="W" g2="guillemotleft,guilsinglleft" k="57"/><hkern g1="W" g2="guillemotleft.case,guilsinglleft.case" k="39"/><hkern g1="W" g2="guillemotright,guilsinglright" k="10"/><hkern g1="W" g2="guillemotright.case,guilsinglright.case" k="10"/><hkern g1="W" g2="m,n,p,r,ntilde,r.ss03" k="30"/><hkern g1="W" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="40"/><hkern g1="W" g2="oslash" k="41"/><hkern g1="W" g2="quotedbl,quotesingle" k="-2"/><hkern g1="W" g2="quotesinglbase,quotedblbase" k="89"/><hkern g1="W" g2="quoteleft,quotedblleft" k="-11"/><hkern g1="W" g2="quoteright,quotedblright" k="-11"/><hkern g1="W" g2="t" k="1"/><hkern g1="W" g2="u,ugrave,uacute,ucircumflex,udieresis" k="35"/><hkern g1="W" g2="w" k="10"/><hkern g1="W" g2="y,yacute,ydieresis" k="23"/><hkern g1="W" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="41"/><hkern g1="W" g2="AE" k="129"/><hkern g1="W" g2="bracketright.case,braceright.case" k="-2"/><hkern g1="W" g2="comma,period,ellipsis" k="82"/><hkern g1="W" g2="s,scaron" k="20"/><hkern g1="W" g2="z,zcaron" k="5"/><hkern g1="Y,Yacute,Ydieresis" g2="V" k="-10"/><hkern g1="Y,Yacute,Ydieresis" g2="X" k="-10"/><hkern g1="Y,Yacute,Ydieresis" g2="ampersand" k="77"/><hkern g1="Y,Yacute,Ydieresis" g2="ampersand.ss04" k="18"/><hkern g1="Y,Yacute,Ydieresis" g2="asciicircum" k="18"/><hkern g1="Y,Yacute,Ydieresis" g2="asciitilde" k="69"/><hkern g1="Y,Yacute,Ydieresis" g2="asterisk" k="9"/><hkern g1="Y,Yacute,Ydieresis" g2="at" k="58"/><hkern g1="Y,Yacute,Ydieresis" g2="backslash" k="-19"/><hkern g1="Y,Yacute,Ydieresis" g2="braceleft" k="38"/><hkern g1="Y,Yacute,Ydieresis" g2="braceleft.case" k="30"/><hkern g1="Y,Yacute,Ydieresis" g2="bullet" k="97"/><hkern g1="Y,Yacute,Ydieresis" g2="c.ordn" k="2"/><hkern g1="Y,Yacute,Ydieresis" g2="d.ordn" k="2"/><hkern g1="Y,Yacute,Ydieresis" g2="divide" k="59"/><hkern g1="Y,Yacute,Ydieresis" g2="e.ordn" k="2"/><hkern g1="Y,Yacute,Ydieresis" g2="equal" k="39"/><hkern g1="Y,Yacute,Ydieresis" g2="exclamdown" k="57"/><hkern g1="Y,Yacute,Ydieresis" g2="g.ordn" k="2"/><hkern g1="Y,Yacute,Ydieresis" g2="germandbls" k="54"/><hkern g1="Y,Yacute,Ydieresis" g2="j.ordn" k="19"/><hkern g1="Y,Yacute,Ydieresis" g2="less" k="78"/><hkern g1="Y,Yacute,Ydieresis" g2="multiply" k="30"/><hkern g1="Y,Yacute,Ydieresis" g2="o.ordn" k="2"/><hkern g1="Y,Yacute,Ydieresis" g2="onesuperior" k="-2"/><hkern g1="Y,Yacute,Ydieresis" g2="ordfeminine" k="18"/><hkern g1="Y,Yacute,Ydieresis" g2="ordmasculine" k="18"/><hkern g1="Y,Yacute,Ydieresis" g2="parenright" k="-2"/><hkern g1="Y,Yacute,Ydieresis" g2="periodcentered" k="69"/><hkern g1="Y,Yacute,Ydieresis" g2="plus" k="68"/><hkern g1="Y,Yacute,Ydieresis" g2="q.ordn" k="2"/><hkern g1="Y,Yacute,Ydieresis" g2="questiondown" k="100"/><hkern g1="Y,Yacute,Ydieresis" g2="registered.ss06" k="18"/><hkern g1="Y,Yacute,Ydieresis" g2="s.ordn" k="1"/><hkern g1="Y,Yacute,Ydieresis" g2="slash" k="117"/><hkern g1="Y,Yacute,Ydieresis" g2="t.ordn" k="-18"/><hkern g1="Y,Yacute,Ydieresis" g2="threesuperior" k="-1"/><hkern g1="Y,Yacute,Ydieresis" g2="trademark" k="-18"/><hkern g1="Y,Yacute,Ydieresis" g2="u.ordn" k="-18"/><hkern g1="Y,Yacute,Ydieresis" g2="underscore" k="87"/><hkern g1="Y,Yacute,Ydieresis" g2="uni2074" k="2"/><hkern g1="Y,Yacute,Ydieresis" g2="uni2077" k="-2"/><hkern g1="Y,Yacute,Ydieresis" g2="v" k="54"/><hkern g1="Y,Yacute,Ydieresis" g2="v.ordn" k="-18"/><hkern g1="Y,Yacute,Ydieresis" g2="w.ordn" k="-18"/><hkern g1="Y,Yacute,Ydieresis" g2="x.ordn" k="-18"/><hkern g1="Y,Yacute,Ydieresis" g2="y.ordn" k="-20"/><hkern g1="Y,Yacute,Ydieresis" g2="florin" k="90"/><hkern g1="Y,Yacute,Ydieresis" g2="parenright.case" k="-2"/><hkern g1="Y,Yacute,Ydieresis" g2="questiondown.case" k="67"/><hkern g1="Y,Yacute,Ydieresis" g2="uni2080" k="138"/><hkern g1="Y,Yacute,Ydieresis" g2="uni2081" k="111"/><hkern g1="Y,Yacute,Ydieresis" g2="uni2082" k="129"/><hkern g1="Y,Yacute,Ydieresis" g2="uni2083" k="127"/><hkern g1="Y,Yacute,Ydieresis" g2="uni2084" k="129"/><hkern g1="Y,Yacute,Ydieresis" g2="uni2085" k="129"/><hkern g1="Y,Yacute,Ydieresis" g2="uni2086" k="147"/><hkern g1="Y,Yacute,Ydieresis" g2="uni2087" k="118"/><hkern g1="Y,Yacute,Ydieresis" g2="uni2088" k="127"/><hkern g1="Y,Yacute,Ydieresis" g2="uni2089" k="136"/><hkern g1="Y,Yacute,Ydieresis" g2="x" k="39"/><hkern g1="Y,Yacute,Ydieresis" g2="lslash" k="18"/><hkern g1="Y,Yacute,Ydieresis" g2="Lslash" k="18"/><hkern g1="Y,Yacute,Ydieresis" g2="bar" k="-10"/><hkern g1="Y,Yacute,Ydieresis" g2="brokenbar" k="-10"/><hkern g1="Y,Yacute,Ydieresis" g2="exclamdown.case" k="9"/><hkern g1="Y,Yacute,Ydieresis" g2="Eth" k="18"/><hkern g1="Y,Yacute,Ydieresis" g2="J" k="98"/><hkern g1="Y,Yacute,Ydieresis" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="50"/><hkern g1="Y,Yacute,Ydieresis" g2="Oslash" k="45"/><hkern g1="Y,Yacute,Ydieresis" g2="S,Scaron" k="24"/><hkern g1="Y,Yacute,Ydieresis" g2="T" k="-20"/><hkern g1="Y,Yacute,Ydieresis" g2="W" k="-10"/><hkern g1="Y,Yacute,Ydieresis" g2="Y,Yacute,Ydieresis" k="-10"/><hkern g1="Y,Yacute,Ydieresis" g2="Z,Zcaron" k="-1"/><hkern g1="Y,Yacute,Ydieresis" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="88"/><hkern g1="Y,Yacute,Ydieresis" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="85"/><hkern g1="Y,Yacute,Ydieresis" g2="bracketright,braceright" k="-2"/><hkern g1="Y,Yacute,Ydieresis" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="48"/><hkern g1="Y,Yacute,Ydieresis" g2="colon,semicolon" k="83"/><hkern g1="Y,Yacute,Ydieresis" g2="hyphen,uni00AD,endash,emdash" k="98"/><hkern g1="Y,Yacute,Ydieresis" g2="hyphen.case,endash.case,emdash.case" k="70"/><hkern g1="Y,Yacute,Ydieresis" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="30"/><hkern g1="Y,Yacute,Ydieresis" g2="guillemotleft,guilsinglleft" k="134"/><hkern g1="Y,Yacute,Ydieresis" g2="guillemotleft.case,guilsinglleft.case" k="123"/><hkern g1="Y,Yacute,Ydieresis" g2="guillemotright,guilsinglright" k="66"/><hkern g1="Y,Yacute,Ydieresis" g2="guillemotright.case,guilsinglright.case" k="55"/><hkern g1="Y,Yacute,Ydieresis" g2="m,n,p,r,ntilde,r.ss03" k="69"/><hkern g1="Y,Yacute,Ydieresis" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="104"/><hkern g1="Y,Yacute,Ydieresis" g2="oslash" k="90"/><hkern g1="Y,Yacute,Ydieresis" g2="quotedbl,quotesingle" k="-1"/><hkern g1="Y,Yacute,Ydieresis" g2="quotesinglbase,quotedblbase" k="127"/><hkern g1="Y,Yacute,Ydieresis" g2="quoteleft,quotedblleft" k="-2"/><hkern g1="Y,Yacute,Ydieresis" g2="quoteright,quotedblright" k="-2"/><hkern g1="Y,Yacute,Ydieresis" g2="t" k="33"/><hkern g1="Y,Yacute,Ydieresis" g2="u,ugrave,uacute,ucircumflex,udieresis" k="74"/><hkern g1="Y,Yacute,Ydieresis" g2="w" k="55"/><hkern g1="Y,Yacute,Ydieresis" g2="y,yacute,ydieresis" k="45"/><hkern g1="Y,Yacute,Ydieresis" g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" k="88"/><hkern g1="Y,Yacute,Ydieresis" g2="AE" k="173"/><hkern g1="Y,Yacute,Ydieresis" g2="bracketright.case,braceright.case" k="-2"/><hkern g1="Y,Yacute,Ydieresis" g2="b,h,k,l,thorn" k="5"/><hkern g1="Y,Yacute,Ydieresis" g2="comma,period,ellipsis" k="130"/><hkern g1="Y,Yacute,Ydieresis" g2="s,scaron" k="74"/><hkern g1="Y,Yacute,Ydieresis" g2="z,zcaron" k="49"/><hkern g1="Y,Yacute,Ydieresis" g2="i,igrave,iacute,icircumflex,idieresis,dotlessi" k="10"/><hkern g1="Y,Yacute,Ydieresis" g2="j" k="1"/><hkern g1="Z,Zcaron" g2="X" k="-1"/><hkern g1="Z,Zcaron" g2="ampersand" k="18"/><hkern g1="Z,Zcaron" g2="ampersand.ss04" k="-9"/><hkern g1="Z,Zcaron" g2="asciitilde" k="9"/><hkern g1="Z,Zcaron" g2="at" k="2"/><hkern g1="Z,Zcaron" g2="braceleft.case" k="9"/><hkern g1="Z,Zcaron" g2="bullet" k="19"/><hkern g1="Z,Zcaron" g2="dagger" k="-9"/><hkern g1="Z,Zcaron" g2="daggerdbl" k="-9"/><hkern g1="Z,Zcaron" g2="divide" k="9"/><hkern g1="Z,Zcaron" g2="germandbls" k="1"/><hkern g1="Z,Zcaron" g2="j.ordn" k="9"/><hkern g1="Z,Zcaron" g2="less" k="14"/><hkern g1="Z,Zcaron" g2="multiply" k="-9"/><hkern g1="Z,Zcaron" g2="onesuperior" k="-20"/><hkern g1="Z,Zcaron" g2="periodcentered" k="19"/><hkern g1="Z,Zcaron" g2="plus" k="6"/><hkern g1="Z,Zcaron" g2="question" k="-1"/><hkern g1="Z,Zcaron" g2="questiondown" k="-9"/><hkern g1="Z,Zcaron" g2="registered.ss06" k="-9"/><hkern g1="Z,Zcaron" g2="t.ordn" k="-18"/><hkern g1="Z,Zcaron" g2="trademark" k="-9"/><hkern g1="Z,Zcaron" g2="u.ordn" k="-18"/><hkern g1="Z,Zcaron" g2="underscore" k="-19"/><hkern g1="Z,Zcaron" g2="v" k="5"/><hkern g1="Z,Zcaron" g2="v.ordn" k="-28"/><hkern g1="Z,Zcaron" g2="w.ordn" k="-28"/><hkern g1="Z,Zcaron" g2="x.ordn" k="-27"/><hkern g1="Z,Zcaron" g2="y.ordn" k="-28"/><hkern g1="Z,Zcaron" g2="z.ordn" k="-27"/><hkern g1="Z,Zcaron" g2="florin" k="2"/><hkern g1="Z,Zcaron" g2="bar" k="-9"/><hkern g1="Z,Zcaron" g2="brokenbar" k="-9"/><hkern g1="Z,Zcaron" g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01" k="20"/><hkern g1="Z,Zcaron" g2="Oslash" k="7"/><hkern g1="Z,Zcaron" g2="T" k="-20"/><hkern g1="Z,Zcaron" g2="Y,Yacute,Ydieresis" k="-1"/><hkern g1="Z,Zcaron" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="10"/><hkern g1="Z,Zcaron" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="9"/><hkern g1="Z,Zcaron" g2="hyphen,uni00AD,endash,emdash" k="23"/><hkern g1="Z,Zcaron" g2="hyphen.case,endash.case,emdash.case" k="33"/><hkern g1="Z,Zcaron" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-14"/><hkern g1="Z,Zcaron" g2="guillemotleft,guilsinglleft" k="30"/><hkern g1="Z,Zcaron" g2="guillemotleft.case,guilsinglleft.case" k="31"/><hkern g1="Z,Zcaron" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="10"/><hkern g1="Z,Zcaron" g2="oslash" k="9"/><hkern g1="Z,Zcaron" g2="u,ugrave,uacute,ucircumflex,udieresis" k="10"/><hkern g1="Z,Zcaron" g2="w" k="5"/><hkern g1="Z,Zcaron" g2="y,yacute,ydieresis" k="2"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="a.ordn" k="2"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="ampersand" k="28"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="ampersand.ss04" k="10"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="asterisk" k="20"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="at" k="10"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="b.ordn" k="2"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="backslash" k="60"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="braceleft.case" k="-9"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="bullet" k="5"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="c.ordn" k="2"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="d.ordn" k="2"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="dagger" k="9"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="daggerdbl" k="9"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="e.ordn" k="2"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="exclam" k="18"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="f.ordn" k="2"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="g.ordn" k="2"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="h.ordn" k="2"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="i.ordn" k="2"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="j.ordn" k="11"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="k.ordn" k="2"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="l.ordn" k="2"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="m.ordn" k="1"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="n.ordn" k="1"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="o.ordn" k="1"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="onesuperior" k="5"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="ordfeminine" k="19"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="ordmasculine" k="11"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="paragraph" k="9"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="parenright" k="9"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="q.ordn" k="2"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="question" k="18"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="registered.ss06" k="50"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="s.ordn" k="1"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="t.ordn" k="2"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="threesuperior" k="1"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="trademark" k="48"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="twosuperior" k="1"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="u.ordn" k="1"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="underscore" k="-12"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="uni2070" k="1"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="uni2075" k="1"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="uni2076" k="1"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="uni2077" k="6"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="uni2078" k="1"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="uni2079" k="1"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="v" k="14"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="v.ordn" k="20"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="w.ordn" k="20"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="x.ordn" k="9"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="y.ordn" k="19"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="z.ordn" k="1"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="5"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="bracketright,braceright" k="9"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="colon,semicolon" k="5"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="3"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="quotedbl,quotesingle" k="18"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="quoteleft,quotedblleft" k="31"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="quoteright,quotedblright" k="20"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="t" k="5"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="u,ugrave,uacute,ucircumflex,udieresis" k="6"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="w" k="9"/><hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" g2="y,yacute,ydieresis" k="15"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="ampersand" k="28"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="asciicircum" k="1"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="at" k="5"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="backslash" k="69"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="exclam" k="9"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="germandbls" k="10"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="ordfeminine" k="14"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="ordmasculine" k="9"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="parenright" k="9"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="periodcentered" k="9"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="trademark" k="31"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="v" k="5"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="5"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="bracketright,braceright" k="9"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="guillemotleft,guilsinglleft" k="18"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="guillemotright,guilsinglright" k="9"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="5"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="quoteleft,quotedblleft" k="18"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="quoteright,quotedblright" k="27"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="u,ugrave,uacute,ucircumflex,udieresis" k="5"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="w" k="5"/><hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" g2="y,yacute,ydieresis" k="5"/><hkern g1="c,ccedilla" g2="ampersand" k="28"/><hkern g1="c,ccedilla" g2="ampersand.ss04" k="1"/><hkern g1="c,ccedilla" g2="asterisk" k="19"/><hkern g1="c,ccedilla" g2="at" k="1"/><hkern g1="c,ccedilla" g2="backslash" k="79"/><hkern g1="c,ccedilla" g2="onesuperior" k="3"/><hkern g1="c,ccedilla" g2="ordfeminine" k="15"/><hkern g1="c,ccedilla" g2="ordmasculine" k="10"/><hkern g1="c,ccedilla" g2="parenright" k="27"/><hkern g1="c,ccedilla" g2="question" k="9"/><hkern g1="c,ccedilla" g2="questiondown" k="10"/><hkern g1="c,ccedilla" g2="registered.ss06" k="50"/><hkern g1="c,ccedilla" g2="slash" k="20"/><hkern g1="c,ccedilla" g2="t.ordn" k="2"/><hkern g1="c,ccedilla" g2="trademark" k="66"/><hkern g1="c,ccedilla" g2="underscore" k="1"/><hkern g1="c,ccedilla" g2="v" k="14"/><hkern g1="c,ccedilla" g2="v.ordn" k="1"/><hkern g1="c,ccedilla" g2="w.ordn" k="1"/><hkern g1="c,ccedilla" g2="uni2084" k="10"/><hkern g1="c,ccedilla" g2="x" k="10"/><hkern g1="c,ccedilla" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="1"/><hkern g1="c,ccedilla" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="5"/><hkern g1="c,ccedilla" g2="bracketright,braceright" k="18"/><hkern g1="c,ccedilla" g2="hyphen,uni00AD,endash,emdash" k="5"/><hkern g1="c,ccedilla" g2="guillemotright.case,guilsinglright.case" k="-1"/><hkern g1="c,ccedilla" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="5"/><hkern g1="c,ccedilla" g2="oslash" k="5"/><hkern g1="c,ccedilla" g2="quotedbl,quotesingle" k="27"/><hkern g1="c,ccedilla" g2="quotesinglbase,quotedblbase" k="6"/><hkern g1="c,ccedilla" g2="quoteleft,quotedblleft" k="37"/><hkern g1="c,ccedilla" g2="quoteright,quotedblright" k="36"/><hkern g1="c,ccedilla" g2="u,ugrave,uacute,ucircumflex,udieresis" k="10"/><hkern g1="c,ccedilla" g2="w" k="14"/><hkern g1="c,ccedilla" g2="y,yacute,ydieresis" k="14"/><hkern g1="c,ccedilla" g2="comma,period,ellipsis" k="9"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="ampersand" k="20"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="ampersand.ss04" k="1"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="asterisk" k="20"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="backslash" k="79"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="divide" k="-9"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="exclamdown" k="9"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="germandbls" k="1"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="onesuperior" k="3"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="ordfeminine" k="20"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="ordmasculine" k="3"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="paragraph" k="9"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="parenright" k="28"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="question" k="21"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="questiondown" k="20"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="registered.ss06" k="50"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="slash" k="14"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="t.ordn" k="2"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="trademark" k="79"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="underscore" k="1"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="uni2074" k="1"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="uni2077" k="5"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="v" k="10"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="parenright.case" k="18"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="uni2084" k="11"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="x" k="10"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="lslash" k="-1"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="bracketright,braceright" k="19"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="quotedbl,quotesingle" k="19"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="quotesinglbase,quotedblbase" k="10"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="quoteleft,quotedblleft" k="31"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="quoteright,quotedblright" k="40"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="u,ugrave,uacute,ucircumflex,udieresis" k="2"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="w" k="10"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="y,yacute,ydieresis" k="6"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="bracketright.case,braceright.case" k="18"/><hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" g2="comma,period,ellipsis" k="11"/><hkern g1="f,uniFB00" g2="a.ordn" k="-39"/><hkern g1="f,uniFB00" g2="ampersand.ss04" k="-9"/><hkern g1="f,uniFB00" g2="asciicircum" k="-29"/><hkern g1="f,uniFB00" g2="asciitilde" k="1"/><hkern g1="f,uniFB00" g2="asterisk" k="-40"/><hkern g1="f,uniFB00" g2="at" k="-14"/><hkern g1="f,uniFB00" g2="b.ordn" k="-39"/><hkern g1="f,uniFB00" g2="backslash" k="-39"/><hkern g1="f,uniFB00" g2="bullet" k="1"/><hkern g1="f,uniFB00" g2="c.ordn" k="-39"/><hkern g1="f,uniFB00" g2="d.ordn" k="-39"/><hkern g1="f,uniFB00" g2="dagger" k="-58"/><hkern g1="f,uniFB00" g2="daggerdbl" k="-57"/><hkern g1="f,uniFB00" g2="e.ordn" k="-39"/><hkern g1="f,uniFB00" g2="exclam" k="-20"/><hkern g1="f,uniFB00" g2="exclamdown" k="-9"/><hkern g1="f,uniFB00" g2="f.ordn" k="-39"/><hkern g1="f,uniFB00" g2="g.ordn" k="-39"/><hkern g1="f,uniFB00" g2="greater" k="-48"/><hkern g1="f,uniFB00" g2="h.ordn" k="-39"/><hkern g1="f,uniFB00" g2="i.ordn" k="-39"/><hkern g1="f,uniFB00" g2="j.ordn" k="-29"/><hkern g1="f,uniFB00" g2="k.ordn" k="-39"/><hkern g1="f,uniFB00" g2="l.ordn" k="-39"/><hkern g1="f,uniFB00" g2="m.ordn" k="-39"/><hkern g1="f,uniFB00" g2="multiply" k="-20"/><hkern g1="f,uniFB00" g2="n.ordn" k="-39"/><hkern g1="f,uniFB00" g2="o.ordn" k="-39"/><hkern g1="f,uniFB00" g2="onesuperior" k="-39"/><hkern g1="f,uniFB00" g2="ordfeminine" k="-9"/><hkern g1="f,uniFB00" g2="ordmasculine" k="-18"/><hkern g1="f,uniFB00" g2="p.ordn" k="-39"/><hkern g1="f,uniFB00" g2="paragraph" k="-48"/><hkern g1="f,uniFB00" g2="parenright" k="-21"/><hkern g1="f,uniFB00" g2="periodcentered" k="-9"/><hkern g1="f,uniFB00" g2="plus" k="1"/><hkern g1="f,uniFB00" g2="q.ordn" k="-39"/><hkern g1="f,uniFB00" g2="question" k="-38"/><hkern g1="f,uniFB00" g2="questiondown" k="39"/><hkern g1="f,uniFB00" g2="r.ordn" k="-39"/><hkern g1="f,uniFB00" g2="registered.ss06" k="-48"/><hkern g1="f,uniFB00" g2="s.ordn" k="-30"/><hkern g1="f,uniFB00" g2="section" k="-29"/><hkern g1="f,uniFB00" g2="slash" k="11"/><hkern g1="f,uniFB00" g2="t.ordn" k="-39"/><hkern g1="f,uniFB00" g2="threesuperior" k="-38"/><hkern g1="f,uniFB00" g2="trademark" k="-38"/><hkern g1="f,uniFB00" g2="twosuperior" k="-38"/><hkern g1="f,uniFB00" g2="u.ordn" k="-39"/><hkern g1="f,uniFB00" g2="underscore" k="38"/><hkern g1="f,uniFB00" g2="uni2070" k="-38"/><hkern g1="f,uniFB00" g2="uni2074" k="-38"/><hkern g1="f,uniFB00" g2="uni2075" k="-38"/><hkern g1="f,uniFB00" g2="uni2076" k="-38"/><hkern g1="f,uniFB00" g2="uni2077" k="-38"/><hkern g1="f,uniFB00" g2="uni2078" k="-38"/><hkern g1="f,uniFB00" g2="uni2079" k="-38"/><hkern g1="f,uniFB00" g2="v" k="-15"/><hkern g1="f,uniFB00" g2="v.ordn" k="-58"/><hkern g1="f,uniFB00" g2="w.ordn" k="-58"/><hkern g1="f,uniFB00" g2="x.ordn" k="-58"/><hkern g1="f,uniFB00" g2="y.ordn" k="-58"/><hkern g1="f,uniFB00" g2="z.ordn" k="-57"/><hkern g1="f,uniFB00" g2="questiondown.case" k="1"/><hkern g1="f,uniFB00" g2="uni2080" k="28"/><hkern g1="f,uniFB00" g2="uni2081" k="28"/><hkern g1="f,uniFB00" g2="uni2082" k="29"/><hkern g1="f,uniFB00" g2="uni2083" k="29"/><hkern g1="f,uniFB00" g2="uni2084" k="39"/><hkern g1="f,uniFB00" g2="uni2085" k="38"/><hkern g1="f,uniFB00" g2="uni2086" k="47"/><hkern g1="f,uniFB00" g2="uni2087" k="37"/><hkern g1="f,uniFB00" g2="uni2088" k="38"/><hkern g1="f,uniFB00" g2="uni2089" k="47"/><hkern g1="f,uniFB00" g2="x" k="-19"/><hkern g1="f,uniFB00" g2="lslash" k="-1"/><hkern g1="f,uniFB00" g2="bar" k="-30"/><hkern g1="f,uniFB00" g2="brokenbar" k="-40"/><hkern g1="f,uniFB00" g2="exclamdown.case" k="-29"/><hkern g1="f,uniFB00" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="2"/><hkern g1="f,uniFB00" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="6"/><hkern g1="f,uniFB00" g2="bracketright,braceright" k="-21"/><hkern g1="f,uniFB00" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="-10"/><hkern g1="f,uniFB00" g2="colon,semicolon" k="-18"/><hkern g1="f,uniFB00" g2="hyphen,uni00AD,endash,emdash" k="10"/><hkern g1="f,uniFB00" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-4"/><hkern g1="f,uniFB00" g2="guillemotleft,guilsinglleft" k="10"/><hkern g1="f,uniFB00" g2="guillemotright,guilsinglright" k="-18"/><hkern g1="f,uniFB00" g2="guillemotright.case,guilsinglright.case" k="-1"/><hkern g1="f,uniFB00" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="7"/><hkern g1="f,uniFB00" g2="oslash" k="7"/><hkern g1="f,uniFB00" g2="quotedbl,quotesingle" k="-50"/><hkern g1="f,uniFB00" g2="quotesinglbase,quotedblbase" k="58"/><hkern g1="f,uniFB00" g2="quoteleft,quotedblleft" k="-49"/><hkern g1="f,uniFB00" g2="quoteright,quotedblright" k="-47"/><hkern g1="f,uniFB00" g2="t" k="-32"/><hkern g1="f,uniFB00" g2="u,ugrave,uacute,ucircumflex,udieresis" k="1"/><hkern g1="f,uniFB00" g2="w" k="-15"/><hkern g1="f,uniFB00" g2="y,yacute,ydieresis" k="-29"/><hkern g1="f,uniFB00" g2="comma,period,ellipsis" k="68"/><hkern g1="f,uniFB00" g2="s,scaron" k="-9"/><hkern g1="f,uniFB00" g2="z,zcaron" k="-28"/><hkern g1="f,uniFB00" g2="j" k="-3"/><hkern g1="g" g2="ampersand" k="28"/><hkern g1="g" g2="ampersand.ss04" k="1"/><hkern g1="g" g2="backslash" k="58"/><hkern g1="g" g2="divide" k="1"/><hkern g1="g" g2="exclam" k="9"/><hkern g1="g" g2="f.ordn" k="2"/><hkern g1="g" g2="germandbls" k="9"/><hkern g1="g" g2="j.ordn" k="1"/><hkern g1="g" g2="ordfeminine" k="16"/><hkern g1="g" g2="ordmasculine" k="5"/><hkern g1="g" g2="parenright" k="18"/><hkern g1="g" g2="question" k="9"/><hkern g1="g" g2="registered.ss06" k="30"/><hkern g1="g" g2="t.ordn" k="2"/><hkern g1="g" g2="trademark" k="30"/><hkern g1="g" g2="uni2077" k="1"/><hkern g1="g" g2="v" k="5"/><hkern g1="g" g2="v.ordn" k="9"/><hkern g1="g" g2="w.ordn" k="9"/><hkern g1="g" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="5"/><hkern g1="g" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="1"/><hkern g1="g" g2="bracketright,braceright" k="18"/><hkern g1="g" g2="hyphen,uni00AD,endash,emdash" k="5"/><hkern g1="g" g2="guillemotleft,guilsinglleft" k="18"/><hkern g1="g" g2="quoteleft,quotedblleft" k="9"/><hkern g1="g" g2="quoteright,quotedblright" k="27"/><hkern g1="g" g2="u,ugrave,uacute,ucircumflex,udieresis" k="5"/><hkern g1="g" g2="w" k="5"/><hkern g1="g" g2="y,yacute,ydieresis" k="5"/><hkern g1="g" g2="comma,period,ellipsis" k="9"/><hkern g1="i,q,igrave,iacute,icircumflex,idieresis,dotlessi,fi,uniFB03" g2="ampersand" k="18"/><hkern g1="i,q,igrave,iacute,icircumflex,idieresis,dotlessi,fi,uniFB03" g2="backslash" k="15"/><hkern g1="i,q,igrave,iacute,icircumflex,idieresis,dotlessi,fi,uniFB03" g2="braceleft.case" k="9"/><hkern g1="i,q,igrave,iacute,icircumflex,idieresis,dotlessi,fi,uniFB03" g2="exclamdown" k="9"/><hkern g1="i,q,igrave,iacute,icircumflex,idieresis,dotlessi,fi,uniFB03" g2="j.ordn" k="9"/><hkern g1="i,q,igrave,iacute,icircumflex,idieresis,dotlessi,fi,uniFB03" g2="question" k="9"/><hkern g1="i,q,igrave,iacute,icircumflex,idieresis,dotlessi,fi,uniFB03" g2="registered.ss06" k="-9"/><hkern g1="i,q,igrave,iacute,icircumflex,idieresis,dotlessi,fi,uniFB03" g2="trademark" k="9"/><hkern g1="i,q,igrave,iacute,icircumflex,idieresis,dotlessi,fi,uniFB03" g2="guillemotleft,guilsinglleft" k="9"/><hkern g1="i,q,igrave,iacute,icircumflex,idieresis,dotlessi,fi,uniFB03" g2="quotesinglbase,quotedblbase" k="9"/><hkern g1="j" g2="ampersand" k="18"/><hkern g1="j" g2="asciitilde" k="9"/><hkern g1="j" g2="at" k="1"/><hkern g1="j" g2="backslash" k="9"/><hkern g1="j" g2="dagger" k="-18"/><hkern g1="j" g2="daggerdbl" k="-5"/><hkern g1="j" g2="germandbls" k="5"/><hkern g1="j" g2="greater" k="-18"/><hkern g1="j" g2="ordfeminine" k="9"/><hkern g1="j" g2="periodcentered" k="9"/><hkern g1="j" g2="exclamdown.case" k="-9"/><hkern g1="j" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="1"/><hkern g1="j" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-5"/><hkern g1="j" g2="guillemotleft,guilsinglleft" k="9"/><hkern g1="j" g2="quoteright,quotedblright" k="-9"/><hkern g1="j" g2="comma,period,ellipsis" k="9"/><hkern g1="k" g2="ampersand" k="28"/><hkern g1="k" g2="asciicircum" k="9"/><hkern g1="k" g2="asciitilde" k="2"/><hkern g1="k" g2="asterisk" k="1"/><hkern g1="k" g2="at" k="5"/><hkern g1="k" g2="backslash" k="39"/><hkern g1="k" g2="braceleft" k="1"/><hkern g1="k" g2="bullet" k="11"/><hkern g1="k" g2="dagger" k="-19"/><hkern g1="k" g2="daggerdbl" k="-19"/><hkern g1="k" g2="divide" k="10"/><hkern g1="k" g2="exclamdown" k="-1"/><hkern g1="k" g2="greater" k="-49"/><hkern g1="k" g2="less" k="29"/><hkern g1="k" g2="multiply" k="-2"/><hkern g1="k" g2="paragraph" k="-18"/><hkern g1="k" g2="parenright" k="18"/><hkern g1="k" g2="periodcentered" k="9"/><hkern g1="k" g2="plus" k="19"/><hkern g1="k" g2="questiondown" k="-27"/><hkern g1="k" g2="slash" k="-20"/><hkern g1="k" g2="trademark" k="38"/><hkern g1="k" g2="underscore" k="-39"/><hkern g1="k" g2="v" k="27"/><hkern g1="k" g2="uni2080" k="-1"/><hkern g1="k" g2="uni2081" k="-1"/><hkern g1="k" g2="uni2082" k="-1"/><hkern g1="k" g2="uni2083" k="-1"/><hkern g1="k" g2="uni2084" k="-1"/><hkern g1="k" g2="uni2085" k="-1"/><hkern g1="k" g2="uni2086" k="-1"/><hkern g1="k" g2="uni2088" k="-1"/><hkern g1="k" g2="uni2089" k="-1"/><hkern g1="k" g2="brokenbar" k="-18"/><hkern g1="k" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="11"/><hkern g1="k" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="20"/><hkern g1="k" g2="bracketright,braceright" k="18"/><hkern g1="k" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="9"/><hkern g1="k" g2="colon,semicolon" k="14"/><hkern g1="k" g2="hyphen,uni00AD,endash,emdash" k="30"/><hkern g1="k" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-5"/><hkern g1="k" g2="guillemotleft,guilsinglleft" k="39"/><hkern g1="k" g2="guillemotright,guilsinglright" k="-9"/><hkern g1="k" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="21"/><hkern g1="k" g2="oslash" k="18"/><hkern g1="k" g2="quotesinglbase,quotedblbase" k="-11"/><hkern g1="k" g2="quoteleft,quotedblleft" k="-9"/><hkern g1="k" g2="quoteright,quotedblright" k="9"/><hkern g1="k" g2="u,ugrave,uacute,ucircumflex,udieresis" k="5"/><hkern g1="k" g2="comma,period,ellipsis" k="9"/><hkern g1="k" g2="z,zcaron" k="-1"/><hkern g1="d,l,fl,uniFB04" g2="ampersand" k="18"/><hkern g1="d,l,fl,uniFB04" g2="backslash" k="9"/><hkern g1="d,l,fl,uniFB04" g2="dagger" k="-9"/><hkern g1="d,l,fl,uniFB04" g2="daggerdbl" k="-9"/><hkern g1="d,l,fl,uniFB04" g2="exclamdown" k="9"/><hkern g1="d,l,fl,uniFB04" g2="germandbls" k="1"/><hkern g1="d,l,fl,uniFB04" g2="ordfeminine" k="18"/><hkern g1="d,l,fl,uniFB04" g2="ordmasculine" k="9"/><hkern g1="d,l,fl,uniFB04" g2="periodcentered" k="9"/><hkern g1="d,l,fl,uniFB04" g2="plus" k="18"/><hkern g1="d,l,fl,uniFB04" g2="questiondown" k="9"/><hkern g1="d,l,fl,uniFB04" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="5"/><hkern g1="d,l,fl,uniFB04" g2="guillemotleft,guilsinglleft" k="9"/><hkern g1="d,l,fl,uniFB04" g2="u,ugrave,uacute,ucircumflex,udieresis" k="1"/><hkern g1="d,l,fl,uniFB04" g2="comma,period,ellipsis" k="9"/><hkern g1="h,m,n,ntilde" g2="ampersand" k="18"/><hkern g1="h,m,n,ntilde" g2="ampersand.ss04" k="1"/><hkern g1="h,m,n,ntilde" g2="asterisk" k="1"/><hkern g1="h,m,n,ntilde" g2="at" k="10"/><hkern g1="h,m,n,ntilde" g2="backslash" k="70"/><hkern g1="h,m,n,ntilde" g2="dagger" k="14"/><hkern g1="h,m,n,ntilde" g2="equal" k="1"/><hkern g1="h,m,n,ntilde" g2="exclamdown" k="5"/><hkern g1="h,m,n,ntilde" g2="germandbls" k="1"/><hkern g1="h,m,n,ntilde" g2="onesuperior" k="4"/><hkern g1="h,m,n,ntilde" g2="ordfeminine" k="19"/><hkern g1="h,m,n,ntilde" g2="ordmasculine" k="18"/><hkern g1="h,m,n,ntilde" g2="periodcentered" k="5"/><hkern g1="h,m,n,ntilde" g2="question" k="20"/><hkern g1="h,m,n,ntilde" g2="registered.ss06" k="50"/><hkern g1="h,m,n,ntilde" g2="trademark" k="85"/><hkern g1="h,m,n,ntilde" g2="uni2077" k="5"/><hkern g1="h,m,n,ntilde" g2="v" k="14"/><hkern g1="h,m,n,ntilde" g2="seven.numr" k="36"/><hkern g1="h,m,n,ntilde" g2="three.numr" k="18"/><hkern g1="h,m,n,ntilde" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="1"/><hkern g1="h,m,n,ntilde" g2="colon,semicolon" k="5"/><hkern g1="h,m,n,ntilde" g2="quotedbl,quotesingle" k="9"/><hkern g1="h,m,n,ntilde" g2="quoteleft,quotedblleft" k="22"/><hkern g1="h,m,n,ntilde" g2="quoteright,quotedblright" k="41"/><hkern g1="h,m,n,ntilde" g2="u,ugrave,uacute,ucircumflex,udieresis" k="1"/><hkern g1="h,m,n,ntilde" g2="w" k="5"/><hkern g1="h,m,n,ntilde" g2="y,yacute,ydieresis" k="14"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="a.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="ampersand" k="28"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="ampersand.ss04" k="20"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="asciitilde" k="-9"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="asterisk" k="22"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="b.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="backslash" k="91"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="c.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="d.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="dagger" k="9"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="daggerdbl" k="-9"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="e.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="exclam" k="18"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="exclamdown" k="18"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="f.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="g.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="germandbls" k="1"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="h.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="i.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="j.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="k.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="l.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="m.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="n.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="o.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="onesuperior" k="6"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="ordfeminine" k="25"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="ordmasculine" k="3"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="p.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="parenright" k="38"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="q.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="question" k="30"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="questiondown" k="36"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="r.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="registered.ss06" k="41"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="s.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="slash" k="29"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="t.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="threesuperior" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="trademark" k="86"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="twosuperior" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="u.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="underscore" k="29"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="uni2070" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="uni2074" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="uni2075" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="uni2076" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="uni2077" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="uni2078" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="uni2079" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="v" k="15"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="v.ordn" k="4"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="w.ordn" k="4"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="y.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="z.ordn" k="2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="florin" k="36"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="parenright.case" k="36"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="uni2083" k="1"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="uni2084" k="3"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="uni2085" k="1"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="uni2086" k="1"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="x" k="24"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="lslash" k="-2"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="seven.numr" k="54"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="bracketright,braceright" k="38"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="colon,semicolon" k="10"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="guillemotright,guilsinglright" k="9"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="quotedbl,quotesingle" k="29"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="quotesinglbase,quotedblbase" k="19"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="quoteleft,quotedblleft" k="40"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="quoteright,quotedblright" k="40"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="t" k="1"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="u,ugrave,uacute,ucircumflex,udieresis" k="1"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="w" k="15"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="y,yacute,ydieresis" k="25"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="bracketright.case,braceright.case" k="36"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="comma,period,ellipsis" k="20"/><hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn" g2="z,zcaron" k="1"/><hkern g1="oslash" g2="ampersand" k="19"/><hkern g1="oslash" g2="backslash" k="79"/><hkern g1="oslash" g2="exclamdown" k="9"/><hkern g1="oslash" g2="germandbls" k="9"/><hkern g1="oslash" g2="greater" k="-9"/><hkern g1="oslash" g2="j.ordn" k="9"/><hkern g1="oslash" g2="parenright" k="28"/><hkern g1="oslash" g2="questiondown" k="27"/><hkern g1="oslash" g2="registered.ss06" k="3"/><hkern g1="oslash" g2="slash" k="27"/><hkern g1="oslash" g2="trademark" k="18"/><hkern g1="oslash" g2="underscore" k="29"/><hkern g1="oslash" g2="v" k="15"/><hkern g1="oslash" g2="uni2080" k="9"/><hkern g1="oslash" g2="uni2084" k="27"/><hkern g1="oslash" g2="uni2085" k="9"/><hkern g1="oslash" g2="uni2086" k="9"/><hkern g1="oslash" g2="uni2088" k="9"/><hkern g1="oslash" g2="uni2089" k="9"/><hkern g1="oslash" g2="x" k="14"/><hkern g1="oslash" g2="lslash" k="-1"/><hkern g1="oslash" g2="bracketright,braceright" k="19"/><hkern g1="oslash" g2="guillemotright,guilsinglright" k="9"/><hkern g1="oslash" g2="quotedbl,quotesingle" k="2"/><hkern g1="oslash" g2="quotesinglbase,quotedblbase" k="28"/><hkern g1="oslash" g2="quoteleft,quotedblleft" k="29"/><hkern g1="oslash" g2="quoteright,quotedblright" k="20"/><hkern g1="oslash" g2="u,ugrave,uacute,ucircumflex,udieresis" k="5"/><hkern g1="oslash" g2="w" k="15"/><hkern g1="oslash" g2="y,yacute,ydieresis" k="19"/><hkern g1="oslash" g2="comma,period,ellipsis" k="20"/><hkern g1="r" g2="ampersand" k="27"/><hkern g1="r" g2="asciicircum" k="-36"/><hkern g1="r" g2="asterisk" k="-40"/><hkern g1="r" g2="at" k="-29"/><hkern g1="r" g2="backslash" k="28"/><hkern g1="r" g2="bullet" k="-9"/><hkern g1="r" g2="dagger" k="-39"/><hkern g1="r" g2="daggerdbl" k="-39"/><hkern g1="r" g2="divide" k="-27"/><hkern g1="r" g2="equal" k="-27"/><hkern g1="r" g2="exclam" k="-12"/><hkern g1="r" g2="exclamdown" k="-11"/><hkern g1="r" g2="greater" k="-45"/><hkern g1="r" g2="less" k="-9"/><hkern g1="r" g2="multiply" k="-18"/><hkern g1="r" g2="ordmasculine" k="-18"/><hkern g1="r" g2="paragraph" k="-49"/><hkern g1="r" g2="parenright" k="18"/><hkern g1="r" g2="periodcentered" k="-9"/><hkern g1="r" g2="plus" k="-9"/><hkern g1="r" g2="question" k="-20"/><hkern g1="r" g2="questiondown" k="19"/><hkern g1="r" g2="registered.ss06" k="-20"/><hkern g1="r" g2="section" k="-12"/><hkern g1="r" g2="slash" k="18"/><hkern g1="r" g2="underscore" k="28"/><hkern g1="r" g2="uni2077" k="1"/><hkern g1="r" g2="v" k="-20"/><hkern g1="r" g2="florin" k="9"/><hkern g1="r" g2="uni2080" k="38"/><hkern g1="r" g2="uni2081" k="29"/><hkern g1="r" g2="uni2082" k="30"/><hkern g1="r" g2="uni2083" k="38"/><hkern g1="r" g2="uni2084" k="48"/><hkern g1="r" g2="uni2085" k="38"/><hkern g1="r" g2="uni2086" k="47"/><hkern g1="r" g2="uni2087" k="28"/><hkern g1="r" g2="uni2088" k="38"/><hkern g1="r" g2="uni2089" k="38"/><hkern g1="r" g2="x" k="-21"/><hkern g1="r" g2="bar" k="-30"/><hkern g1="r" g2="brokenbar" k="-12"/><hkern g1="r" g2="exclamdown.case" k="-18"/><hkern g1="r" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="-9"/><hkern g1="r" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="1"/><hkern g1="r" g2="bracketright,braceright" k="-2"/><hkern g1="r" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="-30"/><hkern g1="r" g2="colon,semicolon" k="-27"/><hkern g1="r" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-21"/><hkern g1="r" g2="guillemotleft.case,guilsinglleft.case" k="-2"/><hkern g1="r" g2="guillemotright,guilsinglright" k="-21"/><hkern g1="r" g2="m,n,p,r,ntilde,r.ss03" k="-5"/><hkern g1="r" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="1"/><hkern g1="r" g2="oslash" k="1"/><hkern g1="r" g2="quotedbl,quotesingle" k="-39"/><hkern g1="r" g2="quotesinglbase,quotedblbase" k="21"/><hkern g1="r" g2="quoteleft,quotedblleft" k="-9"/><hkern g1="r" g2="quoteright,quotedblright" k="-12"/><hkern g1="r" g2="t" k="-21"/><hkern g1="r" g2="u,ugrave,uacute,ucircumflex,udieresis" k="-1"/><hkern g1="r" g2="w" k="-19"/><hkern g1="r" g2="y,yacute,ydieresis" k="-29"/><hkern g1="r" g2="b,h,k,l,thorn" k="-5"/><hkern g1="r" g2="comma,period,ellipsis" k="72"/><hkern g1="r" g2="s,scaron" k="-9"/><hkern g1="r" g2="z,zcaron" k="-19"/><hkern g1="r" g2="i,igrave,iacute,icircumflex,idieresis,dotlessi" k="-5"/><hkern g1="r" g2="j" k="-5"/><hkern g1="r.ss03" g2="ampersand" k="18"/><hkern g1="r.ss03" g2="asciicircum" k="-9"/><hkern g1="r.ss03" g2="asterisk" k="-10"/><hkern g1="r.ss03" g2="at" k="-13"/><hkern g1="r.ss03" g2="backslash" k="42"/><hkern g1="r.ss03" g2="bullet" k="-2"/><hkern g1="r.ss03" g2="dagger" k="-12"/><hkern g1="r.ss03" g2="daggerdbl" k="-12"/><hkern g1="r.ss03" g2="exclam" k="-2"/><hkern g1="r.ss03" g2="exclamdown" k="-1"/><hkern g1="r.ss03" g2="germandbls" k="5"/><hkern g1="r.ss03" g2="greater" k="-9"/><hkern g1="r.ss03" g2="ordfeminine" k="5"/><hkern g1="r.ss03" g2="paragraph" k="-27"/><hkern g1="r.ss03" g2="parenright" k="25"/><hkern g1="r.ss03" g2="periodcentered" k="-3"/><hkern g1="r.ss03" g2="question" k="-2"/><hkern g1="r.ss03" g2="questiondown" k="46"/><hkern g1="r.ss03" g2="registered.ss06" k="-2"/><hkern g1="r.ss03" g2="slash" k="45"/><hkern g1="r.ss03" g2="trademark" k="18"/><hkern g1="r.ss03" g2="underscore" k="36"/><hkern g1="r.ss03" g2="v" k="-3"/><hkern g1="r.ss03" g2="florin" k="27"/><hkern g1="r.ss03" g2="uni2080" k="47"/><hkern g1="r.ss03" g2="uni2081" k="36"/><hkern g1="r.ss03" g2="uni2082" k="37"/><hkern g1="r.ss03" g2="uni2083" k="45"/><hkern g1="r.ss03" g2="uni2084" k="75"/><hkern g1="r.ss03" g2="uni2085" k="46"/><hkern g1="r.ss03" g2="uni2086" k="65"/><hkern g1="r.ss03" g2="uni2087" k="36"/><hkern g1="r.ss03" g2="uni2088" k="45"/><hkern g1="r.ss03" g2="uni2089" k="45"/><hkern g1="r.ss03" g2="x" k="-4"/><hkern g1="r.ss03" g2="bar" k="-1"/><hkern g1="r.ss03" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="5"/><hkern g1="r.ss03" g2="bracketright,braceright" k="7"/><hkern g1="r.ss03" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="-2"/><hkern g1="r.ss03" g2="colon,semicolon" k="-3"/><hkern g1="r.ss03" g2="hyphen,uni00AD,endash,emdash" k="5"/><hkern g1="r.ss03" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-20"/><hkern g1="r.ss03" g2="guillemotleft,guilsinglleft" k="9"/><hkern g1="r.ss03" g2="guillemotright,guilsinglright" k="-2"/><hkern g1="r.ss03" g2="guillemotright.case,guilsinglright.case" k="-2"/><hkern g1="r.ss03" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="9"/><hkern g1="r.ss03" g2="oslash" k="5"/><hkern g1="r.ss03" g2="quotedbl,quotesingle" k="-2"/><hkern g1="r.ss03" g2="quotesinglbase,quotedblbase" k="37"/><hkern g1="r.ss03" g2="quoteleft,quotedblleft" k="-1"/><hkern g1="r.ss03" g2="quoteright,quotedblright" k="-2"/><hkern g1="r.ss03" g2="t" k="-16"/><hkern g1="r.ss03" g2="u,ugrave,uacute,ucircumflex,udieresis" k="5"/><hkern g1="r.ss03" g2="w" k="-3"/><hkern g1="r.ss03" g2="y,yacute,ydieresis" k="-3"/><hkern g1="r.ss03" g2="comma,period,ellipsis" k="106"/><hkern g1="r.ss03" g2="s,scaron" k="-1"/><hkern g1="r.ss03" g2="z,zcaron" k="-1"/><hkern g1="s,scaron" g2="ampersand" k="18"/><hkern g1="s,scaron" g2="ampersand.ss04" k="9"/><hkern g1="s,scaron" g2="asterisk" k="10"/><hkern g1="s,scaron" g2="backslash" k="60"/><hkern g1="s,scaron" g2="germandbls" k="5"/><hkern g1="s,scaron" g2="greater" k="-1"/><hkern g1="s,scaron" g2="ordfeminine" k="20"/><hkern g1="s,scaron" g2="ordmasculine" k="11"/><hkern g1="s,scaron" g2="parenright" k="23"/><hkern g1="s,scaron" g2="periodcentered" k="9"/><hkern g1="s,scaron" g2="question" k="9"/><hkern g1="s,scaron" g2="registered.ss06" k="22"/><hkern g1="s,scaron" g2="slash" k="-1"/><hkern g1="s,scaron" g2="trademark" k="58"/><hkern g1="s,scaron" g2="v" k="10"/><hkern g1="s,scaron" g2="florin" k="18"/><hkern g1="s,scaron" g2="questiondown.case" k="-9"/><hkern g1="s,scaron" g2="x" k="5"/><hkern g1="s,scaron" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="1"/><hkern g1="s,scaron" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="1"/><hkern g1="s,scaron" g2="bracketright,braceright" k="9"/><hkern g1="s,scaron" g2="guillemotleft,guilsinglleft" k="18"/><hkern g1="s,scaron" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="1"/><hkern g1="s,scaron" g2="oslash" k="1"/><hkern g1="s,scaron" g2="quotedbl,quotesingle" k="1"/><hkern g1="s,scaron" g2="quotesinglbase,quotedblbase" k="-5"/><hkern g1="s,scaron" g2="quoteleft,quotedblleft" k="19"/><hkern g1="s,scaron" g2="quoteright,quotedblright" k="10"/><hkern g1="s,scaron" g2="u,ugrave,uacute,ucircumflex,udieresis" k="4"/><hkern g1="s,scaron" g2="w" k="10"/><hkern g1="s,scaron" g2="y,yacute,ydieresis" k="10"/><hkern g1="s,scaron" g2="z,zcaron" k="-5"/><hkern g1="t" g2="asciicircum" k="-9"/><hkern g1="t" g2="asterisk" k="-27"/><hkern g1="t" g2="backslash" k="9"/><hkern g1="t" g2="exclamdown" k="-9"/><hkern g1="t" g2="greater" k="-36"/><hkern g1="t" g2="paragraph" k="-18"/><hkern g1="t" g2="parenright" k="-1"/><hkern g1="t" g2="question" k="-19"/><hkern g1="t" g2="questiondown" k="-9"/><hkern g1="t" g2="slash" k="-29"/><hkern g1="t" g2="underscore" k="-30"/><hkern g1="t" g2="v" k="-1"/><hkern g1="t" g2="x" k="-29"/><hkern g1="t" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="-9"/><hkern g1="t" g2="bracketright,braceright" k="-1"/><hkern g1="t" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="-9"/><hkern g1="t" g2="colon,semicolon" k="-9"/><hkern g1="t" g2="guillemotright,guilsinglright" k="-1"/><hkern g1="t" g2="m,n,p,r,ntilde,r.ss03" k="1"/><hkern g1="t" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="1"/><hkern g1="t" g2="quotedbl,quotesingle" k="-9"/><hkern g1="t" g2="quotesinglbase,quotedblbase" k="-1"/><hkern g1="t" g2="quoteleft,quotedblleft" k="1"/><hkern g1="t" g2="quoteright,quotedblright" k="3"/><hkern g1="t" g2="u,ugrave,uacute,ucircumflex,udieresis" k="-9"/><hkern g1="t" g2="w" k="-1"/><hkern g1="t" g2="y,yacute,ydieresis" k="-19"/><hkern g1="t" g2="b,h,k,l,thorn" k="32"/><hkern g1="t" g2="s,scaron" k="-19"/><hkern g1="t" g2="z,zcaron" k="-25"/><hkern g1="t" g2="i,igrave,iacute,icircumflex,idieresis,dotlessi" k="-5"/><hkern g1="t" g2="j" k="-9"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="ampersand" k="18"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="at" k="5"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="backslash" k="23"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="germandbls" k="5"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="ordfeminine" k="11"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="ordmasculine" k="11"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="parenright" k="14"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="registered.ss06" k="20"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="trademark" k="29"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="1"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="bracketright,braceright" k="5"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-5"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="guillemotleft,guilsinglleft" k="9"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="guillemotleft.case,guilsinglleft.case" k="1"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="1"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="oslash" k="1"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="quoteleft,quotedblleft" k="10"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="quoteright,quotedblright" k="9"/><hkern g1="u,ugrave,uacute,ucircumflex,udieresis" g2="u,ugrave,uacute,ucircumflex,udieresis" k="1"/><hkern g1="w" g2="ampersand" k="28"/><hkern g1="w" g2="asciicircum" k="-18"/><hkern g1="w" g2="asterisk" k="-9"/><hkern g1="w" g2="at" k="-6"/><hkern g1="w" g2="backslash" k="29"/><hkern g1="w" g2="dagger" k="-30"/><hkern g1="w" g2="daggerdbl" k="-31"/><hkern g1="w" g2="divide" k="9"/><hkern g1="w" g2="exclamdown" k="9"/><hkern g1="w" g2="germandbls" k="5"/><hkern g1="w" g2="greater" k="-18"/><hkern g1="w" g2="less" k="9"/><hkern g1="w" g2="multiply" k="-9"/><hkern g1="w" g2="ordmasculine" k="-14"/><hkern g1="w" g2="paragraph" k="-18"/><hkern g1="w" g2="parenright" k="29"/><hkern g1="w" g2="plus" k="5"/><hkern g1="w" g2="question" k="-18"/><hkern g1="w" g2="questiondown" k="38"/><hkern g1="w" g2="registered.ss06" k="-11"/><hkern g1="w" g2="slash" k="29"/><hkern g1="w" g2="trademark" k="9"/><hkern g1="w" g2="underscore" k="40"/><hkern g1="w" g2="v" k="-1"/><hkern g1="w" g2="florin" k="18"/><hkern g1="w" g2="questiondown.case" k="9"/><hkern g1="w" g2="uni2080" k="48"/><hkern g1="w" g2="uni2081" k="39"/><hkern g1="w" g2="uni2082" k="48"/><hkern g1="w" g2="uni2083" k="48"/><hkern g1="w" g2="uni2084" k="69"/><hkern g1="w" g2="uni2085" k="58"/><hkern g1="w" g2="uni2086" k="76"/><hkern g1="w" g2="uni2087" k="37"/><hkern g1="w" g2="uni2088" k="58"/><hkern g1="w" g2="uni2089" k="66"/><hkern g1="w" g2="x" k="-1"/><hkern g1="w" g2="bar" k="-11"/><hkern g1="w" g2="brokenbar" k="-11"/><hkern g1="w" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="1"/><hkern g1="w" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="15"/><hkern g1="w" g2="bracketright,braceright" k="20"/><hkern g1="w" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="-1"/><hkern g1="w" g2="hyphen,uni00AD,endash,emdash" k="10"/><hkern g1="w" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-20"/><hkern g1="w" g2="guillemotleft,guilsinglleft" k="15"/><hkern g1="w" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="15"/><hkern g1="w" g2="oslash" k="15"/><hkern g1="w" g2="quotedbl,quotesingle" k="-19"/><hkern g1="w" g2="quotesinglbase,quotedblbase" k="49"/><hkern g1="w" g2="quoteleft,quotedblleft" k="-9"/><hkern g1="w" g2="quoteright,quotedblright" k="-9"/><hkern g1="w" g2="t" k="-20"/><hkern g1="w" g2="y,yacute,ydieresis" k="-1"/><hkern g1="w" g2="comma,period,ellipsis" k="49"/><hkern g1="w" g2="z,zcaron" k="-5"/><hkern g1="y,yacute,ydieresis" g2="ampersand" k="20"/><hkern g1="y,yacute,ydieresis" g2="asciicircum" k="-9"/><hkern g1="y,yacute,ydieresis" g2="asterisk" k="-18"/><hkern g1="y,yacute,ydieresis" g2="at" k="-1"/><hkern g1="y,yacute,ydieresis" g2="backslash" k="30"/><hkern g1="y,yacute,ydieresis" g2="dagger" k="-29"/><hkern g1="y,yacute,ydieresis" g2="daggerdbl" k="-29"/><hkern g1="y,yacute,ydieresis" g2="divide" k="9"/><hkern g1="y,yacute,ydieresis" g2="exclamdown" k="9"/><hkern g1="y,yacute,ydieresis" g2="germandbls" k="5"/><hkern g1="y,yacute,ydieresis" g2="greater" k="-27"/><hkern g1="y,yacute,ydieresis" g2="multiply" k="-18"/><hkern g1="y,yacute,ydieresis" g2="paragraph" k="-18"/><hkern g1="y,yacute,ydieresis" g2="parenright" k="36"/><hkern g1="y,yacute,ydieresis" g2="periodcentered" k="9"/><hkern g1="y,yacute,ydieresis" g2="question" k="-10"/><hkern g1="y,yacute,ydieresis" g2="questiondown" k="48"/><hkern g1="y,yacute,ydieresis" g2="registered.ss06" k="-10"/><hkern g1="y,yacute,ydieresis" g2="section" k="-9"/><hkern g1="y,yacute,ydieresis" g2="slash" k="46"/><hkern g1="y,yacute,ydieresis" g2="underscore" k="50"/><hkern g1="y,yacute,ydieresis" g2="v" k="-1"/><hkern g1="y,yacute,ydieresis" g2="florin" k="27"/><hkern g1="y,yacute,ydieresis" g2="uni2080" k="85"/><hkern g1="y,yacute,ydieresis" g2="uni2081" k="57"/><hkern g1="y,yacute,ydieresis" g2="uni2082" k="75"/><hkern g1="y,yacute,ydieresis" g2="uni2083" k="66"/><hkern g1="y,yacute,ydieresis" g2="uni2084" k="115"/><hkern g1="y,yacute,ydieresis" g2="uni2085" k="94"/><hkern g1="y,yacute,ydieresis" g2="uni2086" k="103"/><hkern g1="y,yacute,ydieresis" g2="uni2087" k="57"/><hkern g1="y,yacute,ydieresis" g2="uni2088" k="84"/><hkern g1="y,yacute,ydieresis" g2="uni2089" k="84"/><hkern g1="y,yacute,ydieresis" g2="lslash" k="5"/><hkern g1="y,yacute,ydieresis" g2="bar" k="-11"/><hkern g1="y,yacute,ydieresis" g2="brokenbar" k="-19"/><hkern g1="y,yacute,ydieresis" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="15"/><hkern g1="y,yacute,ydieresis" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="25"/><hkern g1="y,yacute,ydieresis" g2="bracketright,braceright" k="36"/><hkern g1="y,yacute,ydieresis" g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788" k="-9"/><hkern g1="y,yacute,ydieresis" g2="colon,semicolon" k="14"/><hkern g1="y,yacute,ydieresis" g2="hyphen,uni00AD,endash,emdash" k="29"/><hkern g1="y,yacute,ydieresis" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-19"/><hkern g1="y,yacute,ydieresis" g2="guillemotleft,guilsinglleft" k="19"/><hkern g1="y,yacute,ydieresis" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="25"/><hkern g1="y,yacute,ydieresis" g2="oslash" k="24"/><hkern g1="y,yacute,ydieresis" g2="quotedbl,quotesingle" k="-10"/><hkern g1="y,yacute,ydieresis" g2="quotesinglbase,quotedblbase" k="61"/><hkern g1="y,yacute,ydieresis" g2="t" k="-19"/><hkern g1="y,yacute,ydieresis" g2="u,ugrave,uacute,ucircumflex,udieresis" k="5"/><hkern g1="y,yacute,ydieresis" g2="y,yacute,ydieresis" k="-1"/><hkern g1="y,yacute,ydieresis" g2="comma,period,ellipsis" k="92"/><hkern g1="y,yacute,ydieresis" g2="s,scaron" k="6"/><hkern g1="z,zcaron" g2="ampersand" k="18"/><hkern g1="z,zcaron" g2="asterisk" k="-9"/><hkern g1="z,zcaron" g2="backslash" k="44"/><hkern g1="z,zcaron" g2="dagger" k="-19"/><hkern g1="z,zcaron" g2="daggerdbl" k="-19"/><hkern g1="z,zcaron" g2="greater" k="-18"/><hkern g1="z,zcaron" g2="paragraph" k="-9"/><hkern g1="z,zcaron" g2="plus" k="-9"/><hkern g1="z,zcaron" g2="questiondown" k="-9"/><hkern g1="z,zcaron" g2="slash" k="-18"/><hkern g1="z,zcaron" g2="trademark" k="9"/><hkern g1="z,zcaron" g2="underscore" k="-10"/><hkern g1="z,zcaron" g2="v" k="-5"/><hkern g1="z,zcaron" g2="questiondown.case" k="-9"/><hkern g1="z,zcaron" g2="x" k="-10"/><hkern g1="z,zcaron" g2="lslash" k="-1"/><hkern g1="z,zcaron" g2="bar" k="-11"/><hkern g1="z,zcaron" g2="brokenbar" k="-9"/><hkern g1="z,zcaron" g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" k="-9"/><hkern g1="z,zcaron" g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02" k="6"/><hkern g1="z,zcaron" g2="f,uniFB00,fi,fl,uniFB03,uniFB04" k="-27"/><hkern g1="z,zcaron" g2="guillemotleft,guilsinglleft" k="6"/><hkern g1="z,zcaron" g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe" k="5"/><hkern g1="z,zcaron" g2="quotesinglbase,quotedblbase" k="-9"/><hkern g1="z,zcaron" g2="t" k="-23"/><hkern g1="z,zcaron" g2="w" k="-5"/><hkern g1="z,zcaron" g2="y,yacute,ydieresis" k="-5"/><hkern g1="z,zcaron" g2="z,zcaron" k="-5"/></font></defs></svg>