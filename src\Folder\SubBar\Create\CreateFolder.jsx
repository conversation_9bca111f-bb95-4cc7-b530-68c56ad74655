import React, { useState, useRef, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import useCurrentUser from 'shared/hooks/currentUser';
import { useCreateFolder } from 'shared/hooks/createFolder';
import brand from '../../../../dev/folder.png';

const CreateFolder = ({ closeModal, fetchFolders }) => {
  const { id: parentId } = useParams();
  const { currentUserId } = useCurrentUser();
  const { createFolder, isSubmitting } = useCreateFolder(fetchFolders, closeModal);

  const [folderName, setFolderName] = useState('Untitled');

    // Create a ref to access the input field
    const inputRef = useRef(null);

    // Automatically focus the input field when the component is mounted
    useEffect(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, []);

  const handleSubmit =  (e) => {
    e.preventDefault();
    createFolder(folderName, currentUserId, parentId);
    setFolderName(''); 
  };

  return (
    <div>
      <div className="p-4 text-lg font-medium">New Folder</div>
      <div className="flex flex-col justify-center items-center mb-20">
        <div className="">
          <img
            src={brand}
            className="rounded-md object-cover w-28 h-auto sm:w-28 md:w-28 lg:w-28 xl:w-28 2xl:w-28 mt-7"
            alt="Logo"
          />
        </div>
        <div className="text-lg font-black mb-4 mt-4">{folderName}</div>
        <input
          type="text"
          id="simple-search"
          onChange={e => setFolderName(e.target.value)}
          ref={inputRef}
          className="border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500  block w-2/3 ps-4 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
          placeholder="Untitled"
          required
        />
        <div className="flex justify-end ml-auto gap-3 p-8">
          <button
            type="button"
            onClick={closeModal}
            className="inline-flex w-full justify-center gap-x-1 rounded-full bg-white px-3 py-2 text-sm font-medium text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            className="inline-flex w-full justify-center gap-x-1 rounded-full bg-blue-400 text-white px-3 py-2 text-sm font-medium shadow-sm ring-1 ring-inset hover:bg-blue-500"
          >
            {isSubmitting ? 'Creating...' : 'Create'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateFolder;