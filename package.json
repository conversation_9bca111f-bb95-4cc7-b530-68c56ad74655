{"name": "jira_client", "version": "1.0.0", "type": "module", "author": "<PERSON><PERSON>", "license": "MIT", "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}, "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "preview": "vite preview", "start:production": "pm2 start --name 'jira_client' server.js", "test:jest": "jest", "test:cypress": "cypress open", "pre-commit": "lint-staged"}, "devDependencies": {"@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "cypress": "^13.6.0", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-cypress": "^2.15.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "lint-staged": "^15.2.11", "postcss": "^8.4.49", "prettier": "^3.4.2", "rimraf": "^6.0.1", "tailwindcss": "^3.4.17", "vite": "^5.4.10"}, "dependencies": {"@4tw/cypress-drag-drop": "^1.3.0", "@reduxjs/toolkit": "^2.5.0", "axios": "^1.7.9", "clsx": "^2.1.1", "color": "^4.2.3", "compression": "^1.7.5", "express": "^4.21.2", "express-history-api-fallback": "^2.2.1", "formik": "^2.4.6", "history": "^5.3.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "moment": "^2.30.1", "prop-types": "^15.8.1", "query-string": "^8.2.0", "quill": "^1.3.7", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-content-loader": "^6.2.1", "react-dom": "^18.3.1", "react-redux": "^9.2.0", "react-router-dom": "^6.20.1", "react-textarea-autosize": "^8.5.4", "react-toastify": "^11.0.2", "react-transition-group": "^4.4.5", "redux": "^5.0.1", "redux-devtools-extension": "^2.13.9", "redux-thunk": "^3.1.0", "styled-components": "^6.1.14", "sweet-pubsub": "^1.1.2"}, "lint-staged": {"*.{js,jsx}": ["eslint --fix", "prettier --write"]}}