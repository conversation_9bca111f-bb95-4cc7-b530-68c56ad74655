<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20161116 at Tue Jun 27 14:16:20 2017
 By <PERSON>ting
Generated in 2013 by FontLab Studio. Copyright info pending.
</metadata>
<defs>
<font id="CircularStd-Book" horiz-adv-x="560" >
  <font-face 
    font-family="CircularStd"
    font-weight="450"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 11 6 4 2 1 1 2 1 2"
    ascent="809"
    descent="-191"
    x-height="481"
    cap-height="709"
    bbox="-46 -232 1487 958"
    underline-thickness="80"
    underline-position="-275"
    unicode-range="U+000D-FEFF"
  />
<missing-glyph horiz-adv-x="562" 
d="M80 0v709h402v-709h-402zM159 79h244v551h-244v-551z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="659" 
d="M598 639q-31 0 -53.5 -18.5t-22.5 -63.5v-76h116v-85h-116v-396h-97v396h-219v-396h-97v396h-89v85h89v81q0 77 45 121t113 44q43 0 56 -10v-84q-15 6 -41 6q-31 0 -53.5 -18.5t-22.5 -63.5v-76h219v81q0 77 45 121t113 44q43 0 56 -10v-84q-15 6 -41 6z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="920" 
d="M109 481v81q0 77 45 121t113 44q43 0 56 -10v-84q-15 6 -41 6q-31 0 -53.5 -18.5t-22.5 -63.5v-76h219v81q0 77 45 121t113 44q43 0 56 -10v-84q-15 6 -41 6q-31 0 -53.5 -18.5t-22.5 -63.5v-76h318v-481h-93v396h-225v-396h-97v396h-219v-396h-97v396h-89v85h89z
M726 665q0 28 19.5 48t47.5 20q29 0 48.5 -19.5t19.5 -48.5q0 -28 -20 -47.5t-48 -19.5t-47.5 19.5t-19.5 47.5z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="920" 
d="M109 481v81q0 77 45 121t113 44q43 0 56 -10v-84q-15 6 -41 6q-31 0 -53.5 -18.5t-22.5 -63.5v-76h219v81q0 77 45 121t113 44q43 0 56 -10v-84q-15 6 -41 6q-31 0 -53.5 -18.5t-22.5 -63.5v-76h225v243h93v-724h-93v396h-225v-396h-97v396h-219v-396h-97v396h-89v85h89z
" />
    <glyph glyph-name="f_i" unicode="fi" horiz-adv-x="604" 
d="M267 727q43 0 56 -10v-84q-15 6 -41 6q-31 0 -53.5 -18t-22.5 -63v-77h318v-481h-93v396h-225v-396h-97v396h-89v85h89v81q0 77 45 121t113 44zM410 665q0 28 19.5 48t47.5 20q29 0 48.5 -19.5t19.5 -48.5q0 -28 -20 -47.5t-48 -19.5t-47.5 19.5t-19.5 47.5z" />
    <glyph glyph-name="f_l" unicode="fl" horiz-adv-x="602" 
d="M282 639q-31 0 -53.5 -18.5t-22.5 -63.5v-76h222v243h94v-724h-94v396h-222v-396h-97v396h-89v85h89v81q0 77 45 121t113 44q43 0 56 -10v-84q-15 6 -41 6z" />
    <glyph glyph-name=".notdef" horiz-adv-x="562" 
d="M80 0v709h402v-709h-402zM159 79h244v551h-244v-551z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni000D" unicode="&#xd;" horiz-adv-x="250" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="250" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="311" 
d="M214 709l-25 -506h-67l-26 506h118zM90 60q0 27 19 46.5t46 19.5t46.5 -19.5t19.5 -46.5t-19.5 -46t-46.5 -19t-46 19t-19 46z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="374" 
d="M40 599q0 27 17 44t44 17q26 0 43 -17t17 -44q0 -8 -2 -18l-42 -188h-34l-41 187q-2 10 -2 19zM213 599q0 27 17 44t44 17q26 0 43 -17t17 -44q0 -8 -2 -18l-42 -188h-34l-41 187q-2 10 -2 19z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="738" 
d="M99 405v76h144l36 167h84l-36 -167h142l36 167h84l-36 -167h135v-76h-151l-34 -160h136v-76h-153l-36 -169h-84l36 169h-142l-36 -169h-84l36 169h-126v76h143l34 160h-128zM311 405l-34 -160h142l34 160h-142z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="595" 
d="M346 -117h-78v104q-97 10 -153 67.5t-65 130.5l95 30q5 -52 36 -91t87 -49v209l-25 5q-78 16 -122 63t-44 119t53.5 126t137.5 64v105h78v-106q83 -11 127.5 -57.5t59.5 -105.5l-90 -33q-5 33 -30 64t-67 43v-205l15 -3q82 -16 130 -67t48 -119q0 -71 -51 -125t-142 -65
v-104zM441 172q0 33 -24 59t-71 37v-193q47 9 71 35t24 62zM176 475q0 -73 92 -91v190q-43 -8 -67.5 -35t-24.5 -64z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="817" 
d="M138 498q0 -34 22 -56.5t55 -22.5t55 22.5t22 56.5t-22 56t-55 22t-55 -22t-22 -56zM55 498q0 65 47.5 111t112.5 46t113 -46t48 -111q0 -66 -48 -112t-113 -46t-112.5 46t-47.5 112zM534 150q0 -34 22 -56.5t55 -22.5t55 22.5t22 56.5t-22 56t-55 22t-55 -22t-22 -56z
M451 150q0 65 47.5 111t112.5 46t113 -46t48 -111q0 -66 -48 -112t-113 -46t-112.5 46t-47.5 112zM227 0h-92l466 648h90z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="697" 
d="M564 0l-90 94q-95 -106 -199 -106q-95 0 -150 54t-55 127q0 64 30.5 107t94.5 91l10 8l-13 14q-25 26 -38.5 42.5t-30 50t-16.5 65.5q0 81 55.5 129t127.5 48q82 0 135 -47.5t53 -125.5q0 -83 -100 -157l-36 -27l132 -137l158 176v-129l-96 -111l161 -166h-133zM276 78
q37 0 67.5 20t64.5 58l3 4l-144 151l-36 -27q-60 -46 -60 -107q0 -38 29.5 -68.5t75.5 -30.5zM204 553q0 -43 54 -99l22 -24l48 36q53 39 53 88q0 36 -25.5 61t-64.5 25q-34 0 -60.5 -23.5t-26.5 -63.5z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="201" 
d="M40 599q0 27 17 44t44 17q26 0 43 -17t17 -44q0 -8 -2 -18l-42 -188h-34l-41 187q-2 10 -2 19z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="302" 
d="M55 317q0 283 190 477l57 -50q-166 -186 -166 -427t166 -427l-57 -50q-190 194 -190 477z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="302" 
d="M247 317q0 -283 -190 -477l-57 50q166 186 166 427t-166 427l57 50q190 -194 190 -477z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="440" 
d="M259 709v-134l123 42l23 -72l-124 -41l82 -105l-61 -46l-83 109l-78 -108l-61 46l79 104l-124 41l23 72l122 -43v135h79z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="567" 
d="M55 327h187v195h82v-195h188v-79h-188v-197h-82v197h-187v79z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="294" 
d="M70 64q0 27 21 47t52 20q34 0 57.5 -25t23.5 -70q0 -90 -46 -139t-102 -56v52q36 8 59 39.5t24 68.5q-7 -5 -22 -5q-29 0 -48 18.5t-19 49.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="361" 
d="M316 246h-271v82h271v-82z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="279" 
d="M70 64q0 29 20 49.5t49 20.5t49.5 -20.5t20.5 -49.5t-20.5 -49t-49.5 -20t-49 20t-20 49z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="434" 
d="M434 709l-343 -709h-91l343 709h91z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="614" 
d="M147 324q0 -150 57 -210q40 -42 103 -42t103 42q57 60 57 210t-57 210q-40 42 -103 42t-103 -42q-57 -60 -57 -210zM50 324q0 149 58 235q69 104 199 104t199 -104q58 -86 58 -235t-58 -235q-69 -104 -199 -104t-199 104q-58 86 -58 235z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="353" 
d="M278 0h-96v457h-157v69q66 1 111 34.5t56 87.5h86v-648z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="529" 
d="M141 415l-98 12q-1 9 -1 26q0 87 61 148.5t163 61.5q101 0 160 -57.5t59 -141.5q0 -123 -122 -200l-128 -82q-69 -44 -79 -91h333v-91h-454q2 82 37.5 144t116.5 114l108 70q89 57 89 135q0 48 -32 81t-90 33q-61 0 -93 -37t-32 -96q0 -9 2 -29z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="553" 
d="M225 301l-48 81l191 176h-310v90h440v-88l-185 -171q79 0 139.5 -52t60.5 -145q0 -86 -63 -147t-172 -61q-107 0 -171.5 59.5t-69.5 144.5l96 22q3 -63 43.5 -101.5t100.5 -38.5q64 0 100.5 34.5t36.5 85.5q0 59 -39 90t-93 31q-29 0 -57 -10z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="574" 
d="M35 146v113l278 389h135v-411h111v-91h-111v-146h-95v146h-318zM353 237v317l-228 -317h228z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="548" 
d="M35 176l94 28q4 -60 44.5 -97t101.5 -37q59 0 99 36t40 95q0 64 -40 99t-100 35q-73 0 -118 -47q-14 5 -46 18t-50 20l84 322h342v-90h-277l-48 -186q43 48 132 48q97 0 158.5 -58t61.5 -157q0 -94 -66 -157.5t-172 -63.5q-100 0 -166.5 56t-73.5 136z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="580" 
d="M525 627l-30 -80q-48 28 -116 28q-99 0 -160.5 -65t-67.5 -167q22 36 66.5 60t104.5 24q99 0 160.5 -59t61.5 -162q0 -101 -70 -161t-170 -60q-104 0 -178.5 75.5t-74.5 224.5q0 176 94 277t240 101q89 0 140 -36zM165 206q0 -61 42 -98.5t98 -37.5q59 0 99.5 37t40.5 99
q0 63 -40.5 100t-99.5 37t-99.5 -37t-40.5 -100z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="483" 
d="M478 648v-94q-25 -20 -49 -43t-63.5 -74t-69.5 -108t-56.5 -144.5t-35.5 -184.5h-101q6 77 24 149.5t42 126.5t51.5 101.5t54 80t48 55.5t34.5 34l13 11h-365v90h473z" />
    <glyph glyph-name="eight" unicode="8" 
d="M280 372q52 1 86 30t34 76q0 44 -33 74t-87 30t-87 -30t-33 -74q0 -47 34 -76t86 -30zM280 70q63 0 99.5 31t36.5 78q0 48 -36.5 79.5t-99.5 31.5t-99.5 -31.5t-36.5 -79.5q0 -47 36.5 -78t99.5 -31zM280 -15q-109 0 -172 52.5t-63 133.5q0 57 34.5 99.5t88.5 60.5
q-46 17 -76 58.5t-30 93.5q0 79 62 129.5t156 50.5t156 -50.5t62 -129.5q0 -51 -30 -93t-76 -59q54 -17 88.5 -60t34.5 -100q0 -81 -63 -133.5t-172 -52.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="581" 
d="M57 23l32 81q51 -31 118 -31q111 0 166.5 63.5t58.5 166.5q-19 -35 -62.5 -59t-105.5 -24q-90 0 -157 58.5t-67 160.5q0 98 70.5 161t168.5 63q104 0 178 -76t74 -222q0 -182 -81.5 -281t-242.5 -99q-38 0 -82 11t-68 27zM419 441q0 60 -42 98t-98 38q-57 0 -98.5 -37.5
t-41.5 -98.5q0 -63 41 -100t99 -37q57 0 98.5 37.5t41.5 99.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="290" 
d="M76 64q0 29 20 49.5t49 20.5t49.5 -20.5t20.5 -49.5t-20.5 -49t-49.5 -20t-49 20t-20 49zM75 416q0 29 20 49.5t49 20.5t49.5 -20.5t20.5 -49.5t-20.5 -49t-49.5 -20t-49 20t-20 49z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="297" 
d="M75 62q0 26 20 44.5t50 18.5q33 0 55 -23.5t22 -66.5q0 -85 -43.5 -131.5t-97.5 -53.5v49q34 8 56 37.5t23 65.5q-8 -5 -21 -5q-27 0 -45.5 17.5t-18.5 47.5zM76 416q0 29 20 49.5t49 20.5t49.5 -20.5t20.5 -49.5t-20.5 -49t-49.5 -20t-49 20t-20 49z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="482" 
d="M35 244v85l412 206v-95l-310 -153l310 -153v-95z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="593" 
d="M528 346h-463v77h463v-77zM528 151h-463v79h463v-79z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="482" 
d="M447 329v-85l-412 -205v95l312 153l-312 153v95z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="510" 
d="M284 203h-88q-1 12 -1 35q0 100 83 156l44 30q53 36 53 98q0 48 -31 81t-88 33q-61 0 -93 -38t-32 -90q0 -30 7 -49l-97 12q-6 21 -6 48q0 49 22 94t74 78t125 33q100 0 159.5 -60t59.5 -141q0 -105 -102 -174l-46 -31q-44 -30 -44 -96q0 -9 1 -19zM174 60q0 27 19 46.5
t46 19.5t46.5 -19.5t19.5 -46.5t-19.5 -46t-46.5 -19t-46 19t-19 46z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="826" 
d="M601 137q-37 0 -64 16t-35 48q-46 -66 -119 -66q-68 0 -110.5 47t-42.5 124q0 99 59 166t141 67q35 0 65.5 -17t41.5 -49l9 53h79l-54 -251q-3 -15 -3 -28q0 -41 38 -41q42 0 75 57.5t33 132.5q0 111 -77.5 183t-202.5 72q-133 0 -220 -88.5t-87 -230.5q0 -134 85.5 -220
t232.5 -86q117 0 205 70l41 -54q-43 -40 -111.5 -64.5t-134.5 -24.5q-176 0 -288 105.5t-112 274.5q0 171 113 281t275 110q153 0 255.5 -93.5t102.5 -233.5q0 -111 -55.5 -185.5t-134.5 -74.5zM515 370q0 45 -22 69.5t-62 24.5q-52 0 -85.5 -44.5t-33.5 -102.5
q0 -109 85 -109q51 0 83.5 45.5t34.5 116.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="697" 
d="M581 0l-77 198h-314l-75 -198h-105l282 709h113l282 -709h-106zM346 607l-121 -318h244z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="602" 
d="M90 709h231q99 0 156.5 -53t57.5 -139q0 -55 -29 -95t-77 -58q59 -15 93.5 -62.5t34.5 -108.5q0 -84 -61 -138.5t-158 -54.5h-248v709zM188 399h122q58 0 92 31.5t34 81.5q0 51 -35 81t-95 30h-118v-224zM188 86h136q60 0 96.5 31t36.5 82q0 53 -33 84.5t-96 31.5h-140
v-229z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="750" 
d="M397 -15q-145 0 -248.5 100t-103.5 270q0 114 52 200t131 127.5t169 41.5q120 0 202.5 -60.5t109.5 -164.5l-91 -32q-20 80 -77 123.5t-144 43.5q-104 0 -178 -73.5t-74 -205.5t74 -206t178 -74q87 0 145 46.5t81 123.5l87 -32q-29 -103 -111.5 -165.5t-201.5 -62.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="720" 
d="M188 87h145q104 0 172.5 69t68.5 197q0 129 -67.5 199t-171.5 70h-147v-535zM336 0h-246v709h248q143 0 240 -94.5t97 -261.5q0 -166 -98 -259.5t-241 -93.5z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="581" 
d="M521 0h-431v709h431v-91h-333v-218h302v-92h-302v-217h333v-91z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="551" 
d="M188 0h-98v709h431v-91h-333v-228h302v-92h-302v-298z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="765" 
d="M704 0h-79l-9 100q-26 -47 -85 -81t-142 -34q-91 0 -168 41.5t-126.5 127.5t-49.5 201t52 201t130.5 127t168.5 41q114 0 198.5 -58.5t115.5 -155.5l-89 -38q-24 77 -83.5 119.5t-141.5 42.5q-103 0 -177 -73t-74 -206q0 -134 72.5 -207.5t174.5 -73.5q56 0 99.5 18
t68 47.5t37 59.5t14.5 60h-246v88h339v-347z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="738" 
d="M648 0h-98v315h-362v-315h-98v709h98v-303h362v303h98v-709z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="280" 
d="M190 0h-100v709h100v-709z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="534" 
d="M20 211v46l98 22v-67q0 -68 33 -102t89 -34q54 0 85 34t31 95v504h98v-497q0 -96 -58.5 -161.5t-154.5 -65.5q-103 0 -162 61.5t-59 164.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="668" 
d="M529 0l-240 322l-101 -108v-214h-98v709h98v-365l332 365h129l-295 -316l299 -393h-124z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="548" 
d="M523 0h-433v709h98v-617h335v-92z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="946" 
d="M856 0h-97v552l-242 -552h-90l-240 552v-552h-97v709h133l250 -577l252 577h131v-709z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="763" 
d="M673 0h-102l-383 578v-578h-98v709h131l354 -544v544h98v-709z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="802" 
d="M145 355q0 -132 75 -206t181 -74t181 74t75 206t-75 205.5t-181 73.5t-181 -73.5t-75 -205.5zM45 355q0 86 30.5 157t81 116.5t113.5 70.5t131 25t131 -25t113.5 -70.5t81 -116.5t30.5 -157q0 -114 -52.5 -200t-132.5 -128t-171 -42t-171 42t-132.5 128t-52.5 200z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="597" 
d="M188 378h142q63 0 100 32.5t37 87.5q0 56 -37 89.5t-100 33.5h-142v-243zM348 290h-160v-290h-98v709h258q97 0 158 -60t61 -150q0 -91 -61 -150t-158 -59z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="802" 
d="M45 355q0 86 30.5 157t81 116.5t113.5 70.5t131 25t131 -25t113.5 -70.5t81 -116.5t30.5 -157q0 -159 -100 -262l83 -93l-67 -59l-86 95q-84 -51 -186 -51q-91 0 -171 42t-132.5 128t-52.5 200zM145 355q0 -132 75 -206t181 -74q68 0 124 31l-123 138l68 59l124 -140
q63 76 63 192q0 132 -75 205.5t-181 73.5t-181 -73.5t-75 -205.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="619" 
d="M457 0l-162 290h-106v-290h-99v709h267q98 0 157.5 -60.5t59.5 -150.5q0 -77 -46 -131.5t-126 -69.5l169 -297h-114zM189 378h150q60 0 97 32.5t37 87.5q0 56 -37 89.5t-97 33.5h-150v-243z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="593" 
d="M542 548l-88 -31q-7 46 -45 83.5t-105 37.5q-59 0 -98 -34t-39 -84q0 -39 23.5 -66t67.5 -37l106 -23q88 -19 136 -71.5t48 -127.5q0 -86 -68 -148t-177 -62q-119 0 -186.5 62.5t-78.5 148.5l94 30q6 -64 50 -108t120 -44q70 0 108 32t38 81q0 40 -27 69.5t-79 40.5
l-101 22q-79 17 -126 67t-47 127q0 84 68.5 147.5t165.5 63.5q109 0 167 -53t73 -123z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="592" 
d="M582 618h-237v-618h-98v618h-237v91h572v-91z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="670" 
d="M335 -15q-112 0 -183.5 68t-71.5 189v467h97v-464q0 -81 41.5 -125t116.5 -44t116 44t41 125v464h98v-467q0 -121 -71.5 -189t-183.5 -68z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="657" 
d="M328 126l216 583h103l-273 -709h-97l-267 709h104z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1004" 
d="M735 148l153 561h101l-202 -709h-101l-185 575l-183 -575h-98l-205 709h101l156 -555l178 555h103z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="617" 
d="M607 709l-241 -354l238 -355h-117l-179 277l-183 -277h-115l242 355l-240 354h118l179 -278l182 278h116z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="618" 
d="M260 303l-250 406h118l184 -316l184 316h112l-250 -406v-303h-98v303z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="632" 
d="M592 0h-547v98l423 520h-413v91h532v-95l-425 -522h430v-92z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="288" 
d="M283 -148h-193v933h193v-76h-111v-782h111v-75z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="434" 
d="M341 0l-341 709h93l341 -709h-93z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="288" 
d="M198 -148h-193v75h111v782h-111v76h193v-933z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="521" 
d="M496 354h-99l-136 258l-137 -258h-99l194 356h83z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="542" 
d="M527 -79h-512v79h512v-79z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="272" 
d="M160 543l-126 146h118l86 -146h-78z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="523" 
d="M50 129q0 62 40 99.5t106 46.5l130 19q37 5 37 36q0 38 -26 62t-78 24q-48 0 -76 -26.5t-33 -70.5l-90 21q7 69 63 112.5t134 43.5q102 0 151 -49.5t49 -126.5v-241q0 -43 6 -79h-92q-5 30 -5 65q-20 -32 -58.5 -56t-96.5 -24q-71 0 -116 42.5t-45 101.5zM224 64
q63 0 101 34t38 108v22l-147 -22q-32 -5 -51 -23t-19 -49q0 -28 21.5 -49t56.5 -21z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="593" 
d="M173 0h-93v724h93v-311q19 35 60.5 58.5t98.5 23.5q105 0 163 -71t58 -181q0 -111 -60 -183.5t-164 -72.5q-108 0 -156 83v-70zM457 243q0 78 -38.5 123.5t-103.5 45.5q-63 0 -103 -46t-40 -123t40 -124.5t103 -47.5q64 0 103 47t39 125z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="530" 
d="M282 409q-61 0 -103.5 -44t-42.5 -124q0 -79 42.5 -124t104.5 -45q60 0 91.5 30t42.5 69l83 -36q-20 -60 -75.5 -105t-141.5 -45q-105 0 -174 73t-69 183q0 112 69 183.5t173 71.5q88 0 142.5 -45t71.5 -108l-85 -36q-11 42 -42 72t-87 30z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="588" 
d="M136 242q0 -76 37.5 -124t103.5 -48q63 0 101 49t38 125q0 75 -37 121t-101 46t-103 -47t-39 -122zM417 65v8q-19 -37 -57.5 -61.5t-91.5 -24.5q-103 0 -165.5 72.5t-62.5 182.5q0 105 64 178.5t164 73.5q59 0 96 -23.5t51 -57.5v311h93v-635q0 -49 5 -89h-91
q-5 28 -5 65z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="542" 
d="M141 290h263q-2 54 -36 88.5t-96 34.5q-56 0 -92 -36.5t-39 -86.5zM418 165l81 -28q-21 -67 -78 -109.5t-138 -42.5q-101 0 -171.5 69.5t-70.5 187.5q0 110 68 182t162 72q109 0 170 -68.5t61 -183.5q0 -18 -2 -30h-362q1 -64 42.5 -105t102.5 -41q103 0 135 97z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="343" 
d="M322 396h-116v-396h-97v396h-89v85h89v81q0 77 45 121t113 44q43 0 56 -10v-84q-15 6 -41 6q-31 0 -53.5 -18.5t-22.5 -63.5v-76h116v-85z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="578" 
d="M45 -26l91 21q5 -51 41 -84.5t90 -33.5q147 0 147 156v69q-18 -35 -56.5 -57t-90.5 -22q-96 0 -158.5 65.5t-62.5 167.5q0 99 62 166.5t159 67.5q110 0 148 -76v67h93v-445q0 -48 -12.5 -89.5t-39.5 -76.5t-75.5 -55t-113.5 -20q-89 0 -151 50.5t-71 128.5zM281 102
q61 0 99 42.5t38 111.5t-38 111.5t-99 42.5q-62 0 -100.5 -42t-38.5 -112q0 -71 37.5 -112.5t101.5 -41.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="562" 
d="M174 283v-283h-94v724h94v-299q48 70 146 70q83 0 127.5 -53t44.5 -137v-305h-94v289q0 121 -112 121q-52 0 -81 -35.5t-31 -91.5z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="253" 
d="M173 0h-93v481h93v-481zM59 663q0 28 19.5 48t47.5 20t48 -19.5t20 -48.5q0 -28 -20 -47.5t-48 -19.5t-47.5 19.5t-19.5 47.5z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="254" 
d="M80 -52v533h94v-537q0 -66 -36 -107.5t-97 -41.5q-35 0 -56 7v79q15 -3 30 -3q65 0 65 70zM59 665q0 28 19.5 48t47.5 20t48 -19.5t20 -48.5q0 -28 -20 -47.5t-48 -19.5t-47.5 19.5t-19.5 47.5z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="525" 
d="M511 481l-205 -203l209 -278h-118l-157 212l-67 -67v-145h-93v724h93v-456l208 213h130z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="254" 
d="M174 0h-94v724h94v-724z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="857" 
d="M173 0h-93v481h90v-64q22 38 62 58t84 20q47 0 86 -23t58 -68q50 91 161 91q69 0 117.5 -47t48.5 -136v-312h-93v302q0 50 -25.5 80t-76.5 30q-49 0 -80 -34.5t-31 -86.5v-291h-94v302q0 50 -25.5 80t-76.5 30q-50 0 -81 -34t-31 -88v-290z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="562" 
d="M174 278v-278h-94v481h92v-69q48 83 148 83q83 0 127.5 -53t44.5 -137v-305h-94v289q0 121 -112 121q-53 0 -82.5 -37.5t-29.5 -94.5z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="572" 
d="M286 69q63 0 106.5 45.5t43.5 126.5q0 80 -43.5 125.5t-106.5 45.5t-106.5 -45.5t-43.5 -125.5q0 -81 43.5 -126.5t106.5 -45.5zM286 496q107 0 176.5 -72.5t69.5 -182.5q0 -111 -69 -183.5t-177 -72.5t-177 72.5t-69 183.5q0 110 69.5 182.5t176.5 72.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="593" 
d="M173 -190h-93v671h91v-75q21 38 63 62t98 24q104 0 162.5 -71t58.5 -180q0 -110 -60.5 -182t-163.5 -72q-53 0 -94 22t-62 56v-255zM457 241q0 73 -38.5 120.5t-103.5 47.5q-64 0 -103.5 -47.5t-39.5 -120.5q0 -75 39.5 -122.5t103.5 -47.5t103 47.5t39 122.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="581" 
d="M511 -190h-93v258q-19 -37 -58.5 -59t-91.5 -22q-101 0 -164.5 73.5t-63.5 181.5q0 106 61.5 178.5t164.5 72.5q58 0 97.5 -25t56.5 -61v74h91v-671zM136 242q0 -75 38.5 -123.5t102.5 -48.5q63 0 102.5 48.5t39.5 123.5t-39 121.5t-103 46.5q-65 0 -103 -46.5
t-38 -121.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="367" 
d="M347 486v-100q-21 3 -41 3q-132 0 -132 -148v-241h-94v481h92v-84q44 92 144 92q16 0 31 -3z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="452" 
d="M35 125l85 30q5 -39 34 -64.5t78 -25.5q38 0 60 18.5t22 45.5q0 48 -62 62l-82 18q-56 12 -88 48t-32 87q0 62 50.5 107t120.5 45q46 0 82 -13.5t56 -35.5t30 -41t15 -39l-83 -31q-2 12 -7 23.5t-15.5 25.5t-30.5 22.5t-47 8.5q-35 0 -57.5 -19t-22.5 -45q0 -46 55 -58
l78 -17q64 -14 98.5 -51.5t34.5 -91.5q0 -57 -46.5 -103t-129.5 -46q-89 0 -139 44.5t-57 95.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="355" 
d="M206 633v-152h104v-85h-104v-250q0 -34 15.5 -50t51.5 -16q21 0 37 4v-80q-24 -9 -64 -9q-62 0 -98 36t-36 101v264h-92v85h26q37 0 55.5 20.5t18.5 53.5v78h86z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="566" 
d="M397 58q-19 -36 -58 -54.5t-84 -18.5q-82 0 -131 54.5t-49 138.5v303h94v-289q0 -54 26.5 -89t82.5 -35t85 33.5t29 89.5v290h94v-392q0 -47 5 -89h-90q-4 22 -4 58z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="502" 
d="M492 481l-192 -481h-95l-195 481h106l137 -371l138 371h101z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="793" 
d="M351 481h97l125 -362l106 362h99l-156 -481h-97l-128 366l-125 -366h-99l-158 481h103l108 -362z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="491" 
d="M10 0l179 245l-173 236h114l118 -169l116 169h111l-171 -236q14 -19 88 -122t89 -123h-113l-124 177l-122 -177h-112z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="523" 
d="M200 -201h-103l120 254l-207 428h108l150 -332l144 332h101z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="466" 
d="M426 0h-386v82l264 314h-260v85h377v-82l-264 -314h269v-85z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="353" 
d="M45 275v86q48 0 79 24.5t31 76.5v137q0 186 180 186h13v-75h-13q-54 0 -76 -24.5t-22 -80.5v-164q0 -50 -27.5 -82t-76.5 -41q49 -9 76.5 -41t27.5 -82v-163q0 -56 22 -80.5t76 -24.5h13v-75h-13q-180 0 -180 186v136q0 52 -31 76.5t-79 24.5z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="262" 
d="M172 -148h-82v933h82v-933z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="353" 
d="M308 361v-86q-48 0 -79 -24.5t-31 -76.5v-136q0 -186 -180 -186h-13v75h13q54 0 76 24.5t22 80.5v163q0 50 27.5 82t76.5 41q-49 9 -76.5 41t-27.5 82v164q0 56 -22 80.5t-76 24.5h-13v75h13q180 0 180 -186v-137q0 -52 31 -76.5t79 -24.5z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="570" 
d="M288 239l-46 25q-28 15 -54 15q-54 0 -96 -57l-57 46q58 95 152 95q49 0 95 -26l46 -25q28 -16 57 -16q58 0 93 55l57 -46q-56 -93 -150 -93q-47 0 -97 27z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="250" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="277" 
d="M172 288l25 -505h-118l26 505h67zM73 430q0 27 19 46.5t46 19.5t46.5 -19.5t19.5 -46.5t-19.5 -46t-46.5 -19t-46 19t-19 46z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="542" 
d="M315 0h-76v94q-84 15 -136.5 80t-52.5 154q0 91 52.5 154.5t136.5 78.5v88h76v-86q71 -8 114.5 -47.5t58.5 -92.5l-81 -33q-9 32 -31.5 57.5t-60.5 33.5v-307q75 15 97 89l80 -33q-17 -51 -62.5 -90.5t-114.5 -47.5v-92zM143 328q0 -58 26.5 -97t69.5 -53v299
q-43 -14 -69.5 -52.5t-26.5 -96.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="593" 
d="M71 359h70q-30 68 -30 118q0 80 57 133t141 53q105 0 153.5 -53t52.5 -125l-98 -17q-1 54 -29.5 81.5t-76.5 27.5q-42 0 -72 -26.5t-30 -73.5q0 -42 33 -118h194v-86h-168q2 -18 2 -27q0 -52 -25 -94t-70 -65h203q48 0 72 30.5t24 75.5l94 -14q0 -79 -44.5 -129
t-121.5 -50h-337v95q50 21 80 59.5t30 80.5q0 22 -4 38h-100v86z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="582" 
d="M82 239q0 66 37 121l-75 77l60 59l75 -78q54 35 116 35q60 0 111 -31l72 74l60 -59l-70 -73q41 -57 41 -125q0 -70 -40 -123l69 -71l-59 -59l-70 72q-52 -33 -114 -33q-64 0 -119 37l-73 -76l-59 59l73 76q-35 54 -35 118zM165 239q0 -58 37.5 -95t92.5 -37t91.5 37
t36.5 95q0 57 -36.5 94t-91.5 37t-92.5 -37t-37.5 -94z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="582" 
d="M513 155h-168v-155h-97v155h-169v72h169v80h-169v71h117l-171 270h114l157 -261l149 261h112l-163 -270h119v-71h-168v-80h168v-72z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="262" 
d="M90 785h82v-399h-82v399zM90 251h82v-399h-82v399z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="573" 
d="M484 239q0 -42 -26 -76.5t-58 -48.5q41 -26 64.5 -65t23.5 -80q0 -82 -60 -133t-145 -51q-94 0 -144.5 50.5t-52.5 135.5l89 18q0 -57 28 -91t82 -34q48 0 77 25t29 70q0 56 -59 87l-104 54q-67 35 -103 72.5t-36 98.5q0 43 26.5 77.5t58.5 46.5q-42 26 -65.5 65
t-23.5 81q0 81 60 132t145 51q93 0 145 -52.5t52 -132.5l-90 -18q0 57 -28 90.5t-81 33.5q-47 0 -77 -26t-30 -69q0 -55 60 -86l103 -54q68 -36 104 -73.5t36 -97.5zM328 312l-100 54q-19 -8 -33.5 -31t-14.5 -49q0 -55 64 -88l100 -54q18 8 33 30.5t15 49.5q0 55 -64 88z
" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="379" 
d="M38 621q0 24 16.5 41t40.5 17q25 0 42 -17t17 -41q0 -25 -17 -41.5t-42 -16.5q-24 0 -40.5 16.5t-16.5 41.5zM225 621q0 24 16.5 41t41.5 17q24 0 41 -17t17 -41q0 -25 -17 -41.5t-41 -16.5q-25 0 -41.5 16.5t-16.5 41.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="821" 
d="M123 355q0 -126 83 -211.5t203 -85.5t203 85.5t83 211.5t-83 211t-203 85t-203 -85t-83 -211zM45 355q0 153 106.5 261t257.5 108q152 0 259.5 -108.5t107.5 -260.5q0 -153 -107.5 -261.5t-259.5 -108.5q-151 0 -257.5 108.5t-106.5 261.5zM411 494q-50 0 -84.5 -36
t-34.5 -101t34.5 -101.5t85.5 -36.5q85 0 109 81l68 -30q-17 -49 -62 -85.5t-115 -36.5q-85 0 -141.5 60t-56.5 149q0 91 56 149.5t141 58.5q72 0 116.5 -37t58.5 -88l-70 -29q-8 34 -33.5 58.5t-71.5 24.5z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="428" 
d="M66 401q0 43 29 70t74 33l88 12q26 4 26 25q0 24 -17 40t-51 16q-32 0 -51 -18t-22 -47l-69 16q5 47 43.5 79t97.5 32q73 0 108 -35.5t35 -90.5v-169q0 -39 4 -55h-71q-4 16 -4 44q-32 -54 -105 -54q-52 0 -83.5 30t-31.5 72zM193 359q90 0 90 93v15l-95 -15
q-47 -7 -47 -47q0 -19 14 -32.5t38 -13.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="471" 
d="M256 99h-93l-133 191l133 191h93l-131 -191zM451 99h-93l-133 191l133 191h93l-131 -191z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="573" 
d="M508 149h-84v195h-379v82h463v-277z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="361" 
d="M316 246h-271v82h271v-82z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="821" 
d="M45 355q0 153 106.5 261t257.5 108q152 0 259.5 -108.5t107.5 -260.5q0 -153 -107.5 -261.5t-259.5 -108.5q-151 0 -257.5 108.5t-106.5 261.5zM123 355q0 -126 83 -211.5t203 -85.5t203 85.5t83 211.5t-83 211t-203 85t-203 -85t-83 -211zM363 373h52q34 0 52 15.5
t18 43.5q0 61 -70 61h-52v-120zM363 307v-150h-76v402h139q58 0 97 -36.5t39 -89.5q0 -42 -24.5 -74t-63.5 -45l90 -157h-87l-84 150h-30z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="343" 
d="M308 577h-273v81h273v-81z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="371" 
d="M108 502q0 -34 22 -56.5t55 -22.5t55 22.5t22 56.5t-22 56t-55 22t-55 -22t-22 -56zM25 502q0 65 47.5 111t112.5 46t113 -46t48 -111q0 -66 -48 -112t-113 -46t-112.5 46t-47.5 112z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="567" 
d="M65 386h177v136h82v-136h178v-77h-178v-134h-82v134h-177v77zM65 130h437v-79h-437v79z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="389" 
d="M141 568h-74q-3 15 -3 31q0 49 36 83t96 34q59 0 95 -33.5t36 -81.5q0 -66 -70 -110l-72 -45q-27 -18 -31 -37h175v-65h-268v13q0 93 85 145l60 37q43 26 43 63q0 21 -14.5 35.5t-39.5 14.5q-26 0 -41.5 -16t-15.5 -42q0 -12 3 -26z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="412" 
d="M178 509l-29 56l95 79h-171v65h271v-63l-100 -80q46 -4 77 -32.5t31 -76.5q0 -51 -40.5 -86t-104.5 -35q-61 0 -99 30t-48 73l67 27q5 -29 26.5 -46.5t52.5 -17.5q30 0 49 15.5t19 39.5q0 29 -20 42.5t-48 13.5q-14 0 -28 -4z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="274" 
d="M240 689l-126 -146h-79l87 146h118z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="571" 
d="M402 58q-19 -36 -58 -54.5t-84 -18.5q-46 0 -86 20v-195h-94v671h94v-289q0 -54 26.5 -89t82.5 -35t85 33.5t29 89.5v290h94v-392q0 -57 5 -89h-90q-4 22 -4 58z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="626" 
d="M328 628v-829h-83v522h-16q-79 0 -136.5 55t-57.5 135q0 93 64.5 145.5t161.5 52.5h340v-81h-97v-829h-83v829h-93z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="279" 
d="M70 284q0 29 20 49.5t49 20.5t49.5 -20.5t20.5 -49.5t-20.5 -49t-49.5 -20t-49 20t-20 49z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="294" 
d="M115 -101l-33 39l48 76h71l-39 -60q43 0 70 -22.5t27 -59.5q0 -38 -30 -64t-87 -26q-72 0 -110 46l37 45q31 -34 72 -34q20 0 33.5 8.5t13.5 24.5q0 14 -12 23t-32 9q-16 0 -29 -5z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="293" 
d="M213 344h-77v245h-86v52q36 1 61.5 20.5t30.5 47.5h71v-365z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="484" 
d="M242 366q41 0 70 30t29 83t-28.5 82.5t-70.5 29.5t-71 -29.5t-29 -82.5t29 -83t71 -30zM242 659q76 0 125.5 -51t49.5 -129t-49.5 -129t-125.5 -51t-125.5 51t-49.5 129t49.5 129t125.5 51z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="471" 
d="M308 99h-93l131 191l-131 191h93l133 -191zM113 99h-93l131 191l-131 191h93l133 -191z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="780" 
d="M218 283h-77v245h-86v52q36 1 61.5 20.5t30.5 47.5h71v-365zM173 0h-92l466 648h90zM404 72v73l177 220h85v-228h64v-65h-64v-72h-77v72h-185zM589 137v139l-111 -139h111z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="779" 
d="M172 0h-92l466 648h90zM218 283h-77v245h-86v52q36 1 61.5 20.5t30.5 47.5h71v-365zM543 224h-74q-3 15 -3 31q0 49 36 83t96 34q59 0 95 -33.5t36 -81.5q0 -66 -70 -110l-72 -45q-27 -18 -31 -37h175v-65h-268v13q0 93 85 145l60 37q43 26 43 63q0 21 -14.5 35.5
t-39.5 14.5q-26 0 -41.5 -16t-15.5 -42q0 -12 3 -26z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="850" 
d="M261 0h-92l466 648h90zM173 448l-29 56l95 79h-171v65h271v-63l-100 -80q46 -4 77 -32.5t31 -76.5q0 -51 -40.5 -86t-104.5 -35q-61 0 -99 30t-48 73l67 27q5 -29 26.5 -46.5t52.5 -17.5q30 0 49 15.5t19 39.5q0 29 -20 42.5t-48 13.5q-14 0 -28 -4zM474 72v73l177 220
h85v-228h64v-65h-64v-72h-77v72h-185zM659 137v139l-111 -139h111z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="510" 
d="M211 289h88q1 -12 1 -35q0 -100 -83 -156l-44 -30q-53 -36 -53 -98q0 -48 31 -81t88 -33q61 0 93 38t32 90q0 30 -7 49l97 -12q6 -21 6 -48q0 -49 -22 -94t-74 -78t-125 -33q-100 0 -159.5 60t-59.5 141q0 105 102 174l46 31q44 30 44 96q0 12 -1 19zM320 431
q0 -27 -19 -46.5t-46 -19.5t-46.5 19.5t-19.5 46.5t19.5 46t46.5 19t46 -19t19 -46z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="697" 
d="M581 0l-77 198h-314l-75 -198h-105l282 709h113l282 -709h-106zM346 607l-121 -318h244zM301 759l-138 119h129l102 -119h-93z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="697" 
d="M581 0l-77 198h-314l-75 -198h-105l282 709h113l282 -709h-106zM346 607l-121 -318h244zM531 878l-137 -119h-94l101 119h130z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="697" 
d="M581 0l-77 198h-314l-75 -198h-105l282 709h113l282 -709h-106zM346 607l-121 -318h244zM263 759h-91l112 119h120l117 -119h-92l-85 64z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="697" 
d="M581 0l-77 198h-314l-75 -198h-105l282 709h113l282 -709h-106zM346 607l-121 -318h244zM506 881v-16q0 -54 -26.5 -82.5t-69.5 -28.5q-38 0 -71 23l-15 10q-23 14 -37 14q-35 0 -35 -44h-68v16q0 54 27 82.5t70 28.5q35 0 73 -23l15 -9q25 -14 38 -14q32 0 32 35v8h67z
" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="697" 
d="M142 819q0 25 17 42t42 17t42 -17t17 -42t-17 -42t-42 -17t-42 17t-17 42zM433 819q0 25 17 42t42 17t42 -17t17 -42t-17 -42t-42 -17t-42 17t-17 42zM581 0l-77 198h-314l-75 -198h-105l282 709h113l282 -709h-106zM346 607l-121 -318h244z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="697" 
d="M346 607l-121 -318h244zM397 760q0 21 -15.5 36.5t-37.5 15.5q-21 0 -35.5 -15.5t-14.5 -36.5q0 -22 14.5 -36.5t35.5 -14.5q23 0 38 14.5t15 36.5zM469 760q0 -57 -47 -94l265 -666h-106l-77 198h-314l-75 -198h-105l264 664q-50 37 -50 96q0 50 36.5 85.5t85.5 35.5
q51 0 87 -35t36 -86z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1026" 
d="M966 0h-431v196h-278l-126 -196h-126l472 709h489v-91h-333v-218h302v-92h-302v-217h333v-91zM313 284h222v344z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="750" 
d="M371 -101l-33 39l30 48q-136 10 -229.5 109t-93.5 260q0 114 52 200t131 127.5t169 41.5q120 0 202.5 -60.5t109.5 -164.5l-91 -32q-20 80 -77 123.5t-144 43.5q-104 0 -178 -73.5t-74 -205.5t74 -206t178 -74q85 0 144 47t82 123l87 -32q-27 -93 -98.5 -154t-172.5 -72
l-21 -33q43 0 70 -22.5t27 -59.5q0 -38 -30 -64t-87 -26q-72 0 -110 46l37 45q31 -34 72 -34q20 0 33.5 8.5t13.5 24.5q0 14 -12 23t-32 9q-16 0 -29 -5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="581" 
d="M521 0h-431v709h431v-91h-333v-218h302v-92h-302v-217h333v-91zM287 759l-138 119h129l102 -119h-93z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="581" 
d="M521 0h-431v709h431v-91h-333v-218h302v-92h-302v-217h333v-91zM465 878l-137 -119h-94l101 119h130z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="581" 
d="M521 0h-431v709h431v-91h-333v-218h302v-92h-302v-217h333v-91zM222 759h-91l112 119h120l117 -119h-92l-85 64z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="581" 
d="M133 819q0 25 17 42t42 17t42 -17t17 -42t-17 -42t-42 -17t-42 17t-17 42zM364 819q0 25 17 42t42 17t42 -17t17 -42t-17 -42t-42 -17t-42 17t-17 42zM521 0h-431v709h431v-91h-333v-218h302v-92h-302v-217h333v-91z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="280" 
d="M190 0h-100v709h100v-709zM93 759l-138 119h129l102 -119h-93z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="280" 
d="M190 0h-100v709h100v-709zM325 878l-137 -119h-94l101 119h130z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="280" 
d="M190 0h-100v709h100v-709zM63 759h-91l112 119h120l117 -119h-92l-85 64z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="280" 
d="M11 819q0 23 15.5 38t37.5 15q23 0 38.5 -15.5t15.5 -37.5q0 -23 -16 -38.5t-38 -15.5t-37.5 15.5t-15.5 38.5zM164 819q0 22 15.5 37.5t38.5 15.5q22 0 37.5 -15t15.5 -38t-15.5 -38.5t-37.5 -15.5t-38 15.5t-16 38.5zM190 0h-100v709h100v-709z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="724" 
d="M340 0h-246v325h-76v82h76v302h248q143 0 240 -94.5t97 -261.5q0 -166 -98 -259.5t-241 -93.5zM192 87h145q104 0 172.5 69t68.5 197q0 129 -67.5 199t-171.5 70h-147v-215h122v-82h-122v-238z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="763" 
d="M673 0h-102l-383 578v-578h-98v709h131l354 -544v544h98v-709zM543 881v-16q0 -54 -26.5 -82.5t-69.5 -28.5q-38 0 -71 23l-15 10q-23 14 -37 14q-35 0 -35 -44h-68v16q0 54 27 82.5t70 28.5q35 0 73 -23l15 -9q24 -14 38 -14q32 0 32 35v8h67z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="802" 
d="M145 355q0 -132 75 -206t181 -74t181 74t75 206t-75 205.5t-181 73.5t-181 -73.5t-75 -205.5zM45 355q0 86 30.5 157t81 116.5t113.5 70.5t131 25t131 -25t113.5 -70.5t81 -116.5t30.5 -157q0 -114 -52.5 -200t-132.5 -128t-171 -42t-171 42t-132.5 128t-52.5 200z
M361 759l-138 119h129l102 -119h-93z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="802" 
d="M145 355q0 -132 75 -206t181 -74t181 74t75 206t-75 205.5t-181 73.5t-181 -73.5t-75 -205.5zM45 355q0 86 30.5 157t81 116.5t113.5 70.5t131 25t131 -25t113.5 -70.5t81 -116.5t30.5 -157q0 -114 -52.5 -200t-132.5 -128t-171 -42t-171 42t-132.5 128t-52.5 200z
M577 878l-137 -119h-94l101 119h130z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="802" 
d="M145 355q0 -132 75 -206t181 -74t181 74t75 206t-75 205.5t-181 73.5t-181 -73.5t-75 -205.5zM45 355q0 86 30.5 157t81 116.5t113.5 70.5t131 25t131 -25t113.5 -70.5t81 -116.5t30.5 -157q0 -114 -52.5 -200t-132.5 -128t-171 -42t-171 42t-132.5 128t-52.5 200z
M318 759h-91l112 119h120l117 -119h-92l-85 64z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="802" 
d="M145 355q0 -132 75 -206t181 -74t181 74t75 206t-75 205.5t-181 73.5t-181 -73.5t-75 -205.5zM45 355q0 86 30.5 157t81 116.5t113.5 70.5t131 25t131 -25t113.5 -70.5t81 -116.5t30.5 -157q0 -114 -52.5 -200t-132.5 -128t-171 -42t-171 42t-132.5 128t-52.5 200z
M566 882v-16q0 -54 -26.5 -82.5t-69.5 -28.5q-38 0 -71 23l-15 10q-23 14 -37 14q-35 0 -35 -44h-68v16q0 54 27 82.5t70 28.5q35 0 73 -23l15 -9q25 -14 38 -14q32 0 32 35v8h67z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="802" 
d="M187 819q0 25 17 42t42 17t42 -17t17 -42t-17 -42t-42 -17t-42 17t-17 42zM498 819q0 25 17 42t42 17t42 -17t17 -42t-17 -42t-42 -17t-42 17t-17 42zM145 355q0 -132 75 -206t181 -74t181 74t75 206t-75 205.5t-181 73.5t-181 -73.5t-75 -205.5zM45 355q0 86 30.5 157
t81 116.5t113.5 70.5t131 25t131 -25t113.5 -70.5t81 -116.5t30.5 -157q0 -114 -52.5 -200t-132.5 -128t-171 -42t-171 42t-132.5 128t-52.5 200z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="530" 
d="M434 55l-170 175l-168 -174l-56 56l169 176l-168 174l57 57l167 -174l167 174l57 -56l-169 -175l170 -176z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="802" 
d="M45 355q0 86 30.5 157t81 116.5t113.5 70.5t131 25q104 0 191 -55l72 92l68 -52l-73 -94q98 -103 98 -260q0 -114 -52.5 -200t-132.5 -128t-171 -42q-103 0 -189 53l-68 -89l-70 51l71 92q-100 103 -100 263zM145 355q0 -115 59 -188l332 429q-61 38 -135 38
q-106 0 -181 -73.5t-75 -205.5zM657 355q0 109 -57 184l-332 -429q58 -35 133 -35q106 0 181 74t75 206z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="670" 
d="M335 -15q-112 0 -183.5 68t-71.5 189v467h97v-464q0 -81 41.5 -125t116.5 -44t116 44t41 125v464h98v-467q0 -121 -71.5 -189t-183.5 -68zM311 759l-138 119h129l102 -119h-93z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="670" 
d="M335 -15q-112 0 -183.5 68t-71.5 189v467h97v-464q0 -81 41.5 -125t116.5 -44t116 44t41 125v464h98v-467q0 -121 -71.5 -189t-183.5 -68zM494 878l-137 -119h-94l101 119h130z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="670" 
d="M335 -15q-112 0 -183.5 68t-71.5 189v467h97v-464q0 -81 41.5 -125t116.5 -44t116 44t41 125v464h98v-467q0 -121 -71.5 -189t-183.5 -68zM252 759h-91l112 119h120l117 -119h-92l-85 64z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="670" 
d="M166 819q0 25 17 42t42 17t42 -17t17 -42t-17 -42t-42 -17t-42 17t-17 42zM387 819q0 25 17 42t42 17t42 -17t17 -42t-17 -42t-42 -17t-42 17t-17 42zM335 -15q-112 0 -183.5 68t-71.5 189v467h97v-464q0 -81 41.5 -125t116.5 -44t116 44t41 125v464h98v-467
q0 -121 -71.5 -189t-183.5 -68z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="618" 
d="M260 303l-250 406h118l184 -316l184 316h112l-250 -406v-303h-98v303zM467 878l-137 -119h-94l101 119h130z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="592" 
d="M188 244h144q63 0 99 32t36 87q0 57 -36 89.5t-99 32.5h-144v-241zM344 157h-156v-157h-98v709h98v-137h155q98 0 161 -59t63 -149t-62.5 -148.5t-160.5 -58.5z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="567" 
d="M357 343q0 -18 10.5 -27t35.5 -21l35 -17q46 -22 75 -60t29 -88q0 -56 -42.5 -100.5t-120.5 -44.5q-82 0 -123 44.5t-41 96.5l83 22q0 -31 19.5 -58t61.5 -27q33 0 51.5 18.5t18.5 40.5q0 48 -52 73l-34 16q-22 10 -36.5 19t-32.5 24t-27 36t-9 48q0 67 63 108l32 21
q57 36 57 85q0 38 -29.5 63.5t-80.5 25.5q-54 0 -90 -40t-36 -113v-488h-94v493q0 107 62.5 169t157.5 62q90 0 147.5 -48.5t57.5 -120.5q0 -93 -88 -148l-29 -18q-31 -19 -31 -46z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="523" 
d="M50 129q0 62 40 99.5t106 46.5l130 19q37 5 37 36q0 38 -26 62t-78 24q-48 0 -76 -26.5t-33 -70.5l-90 21q7 69 63 112.5t134 43.5q102 0 151 -49.5t49 -126.5v-241q0 -43 6 -79h-92q-5 30 -5 65q-20 -32 -58.5 -56t-96.5 -24q-71 0 -116 42.5t-45 101.5zM224 64
q63 0 101 34t38 108v22l-147 -22q-32 -5 -51 -23t-19 -49q0 -28 21.5 -49t56.5 -21zM235 543l-126 146h118l86 -146h-78z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="523" 
d="M50 129q0 62 40 99.5t106 46.5l130 19q37 5 37 36q0 38 -26 62t-78 24q-48 0 -76 -26.5t-33 -70.5l-90 21q7 69 63 112.5t134 43.5q102 0 151 -49.5t49 -126.5v-241q0 -43 6 -79h-92q-5 30 -5 65q-20 -32 -58.5 -56t-96.5 -24q-71 0 -116 42.5t-45 101.5zM224 64
q63 0 101 34t38 108v22l-147 -22q-32 -5 -51 -23t-19 -49q0 -28 21.5 -49t56.5 -21zM425 689l-126 -146h-79l87 146h118z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="523" 
d="M50 129q0 62 40 99.5t106 46.5l130 19q37 5 37 36q0 38 -26 62t-78 24q-48 0 -76 -26.5t-33 -70.5l-90 21q7 69 63 112.5t134 43.5q102 0 151 -49.5t49 -126.5v-241q0 -43 6 -79h-92q-5 30 -5 65q-20 -32 -58.5 -56t-96.5 -24q-71 0 -116 42.5t-45 101.5zM224 64
q63 0 101 34t38 108v22l-147 -22q-32 -5 -51 -23t-19 -49q0 -28 21.5 -49t56.5 -21zM195 545h-87l104 139h107l104 -139h-88l-70 81z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="523" 
d="M50 129q0 62 40 99.5t106 46.5l130 19q37 5 37 36q0 38 -26 62t-78 24q-48 0 -76 -26.5t-33 -70.5l-90 21q7 69 63 112.5t134 43.5q102 0 151 -49.5t49 -126.5v-241q0 -43 6 -79h-92q-5 30 -5 65q-20 -32 -58.5 -56t-96.5 -24q-71 0 -116 42.5t-45 101.5zM224 64
q63 0 101 34t38 108v22l-147 -22q-32 -5 -51 -23t-19 -49q0 -28 21.5 -49t56.5 -21zM422 677v-15q0 -53 -27 -81t-70 -28q-31 0 -66 22l-14 9q-23 14 -37 14q-29 0 -29 -43h-72v15q0 53 27.5 81t70.5 28q32 0 67 -22l14 -9q23 -14 36 -14q29 0 29 43h71z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="523" 
d="M50 129q0 62 40 99.5t106 46.5l130 19q37 5 37 36q0 38 -26 62t-78 24q-48 0 -76 -26.5t-33 -70.5l-90 21q7 69 63 112.5t134 43.5q102 0 151 -49.5t49 -126.5v-241q0 -43 6 -79h-92q-5 30 -5 65q-20 -32 -58.5 -56t-96.5 -24q-71 0 -116 42.5t-45 101.5zM224 64
q63 0 101 34t38 108v22l-147 -22q-32 -5 -51 -23t-19 -49q0 -28 21.5 -49t56.5 -21zM110 621q0 24 16.5 41t40.5 17q25 0 42 -17t17 -41q0 -25 -17 -41.5t-42 -16.5q-24 0 -40.5 16.5t-16.5 41.5zM297 621q0 24 16.5 41t41.5 17q24 0 41 -17t17 -41q0 -25 -17 -41.5
t-41 -16.5q-25 0 -41.5 16.5t-16.5 41.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="523" 
d="M50 129q0 62 40 99.5t106 46.5l130 19q37 5 37 36q0 38 -26 62t-78 24q-48 0 -76 -26.5t-33 -70.5l-90 21q7 69 63 112.5t134 43.5q102 0 151 -49.5t49 -126.5v-241q0 -43 6 -79h-92q-5 30 -5 65q-20 -32 -58.5 -56t-96.5 -24q-71 0 -116 42.5t-45 101.5zM224 64
q63 0 101 34t38 108v22l-147 -22q-32 -5 -51 -23t-19 -49q0 -28 21.5 -49t56.5 -21zM373 641q0 -45 -33 -77t-79 -32q-45 0 -78 32t-33 77q0 46 33 78t78 32q46 0 79 -32t33 -78zM309 641q0 20 -14 34.5t-35 14.5q-20 0 -33.5 -14.5t-13.5 -34.5t13.5 -34t33.5 -14
q21 0 35 14t14 34z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="874" 
d="M749 165l82 -28q-21 -66 -80 -109t-136 -43q-62 0 -114.5 27t-85.5 77q-57 -104 -193 -104q-79 0 -125.5 42t-46.5 102q0 62 40 99.5t106 46.5l139 19q38 6 38 36q0 37 -29 62t-81 25q-49 0 -79.5 -27t-35.5 -67l-88 17q6 69 67.5 112.5t135.5 43.5q119 0 171 -80
q69 80 169 80q109 0 170 -68.5t61 -183.5q0 -20 -2 -30h-362q1 -63 43 -104.5t102 -41.5q102 0 134 97zM229 64q58 0 101 34.5t43 106.5v23l-157 -22q-70 -10 -70 -74q0 -28 22.5 -48t60.5 -20zM473 290h263q-2 54 -36.5 88.5t-95.5 34.5q-56 0 -92 -36.5t-39 -86.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="530" 
d="M253 -102l-33 39l32 50q-93 11 -152.5 82.5t-59.5 171.5q0 110 69.5 182.5t172.5 72.5q88 0 142.5 -45t71.5 -108l-85 -36q-11 42 -42 72t-87 30q-60 0 -103 -45t-43 -123t42.5 -123.5t104.5 -45.5q58 0 90.5 30.5t43.5 68.5l83 -36q-18 -53 -62.5 -95t-114.5 -52
l-23 -35q43 0 70 -22.5t27 -59.5q0 -38 -29.5 -63t-87.5 -25q-73 0 -110 44l37 45q31 -34 72 -34q20 0 33.5 8.5t13.5 24.5q0 14 -12 23t-32 9q-16 0 -29 -5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="542" 
d="M141 290h263q-2 54 -36 88.5t-96 34.5q-56 0 -92 -36.5t-39 -86.5zM418 165l81 -28q-21 -67 -78 -109.5t-138 -42.5q-101 0 -171.5 69.5t-70.5 187.5q0 110 68 182t162 72q109 0 170 -68.5t61 -183.5q0 -18 -2 -30h-362q1 -64 42.5 -105t102.5 -41q103 0 135 97zM250 543
l-126 146h118l86 -146h-78z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="542" 
d="M141 290h263q-2 54 -36 88.5t-96 34.5q-56 0 -92 -36.5t-39 -86.5zM418 165l81 -28q-21 -67 -78 -109.5t-138 -42.5q-101 0 -171.5 69.5t-70.5 187.5q0 110 68 182t162 72q109 0 170 -68.5t61 -183.5q0 -18 -2 -30h-362q1 -64 42.5 -105t102.5 -41q103 0 135 97zM435 689
l-126 -146h-79l87 146h118z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="542" 
d="M141 290h263q-2 54 -36 88.5t-96 34.5q-56 0 -92 -36.5t-39 -86.5zM418 165l81 -28q-21 -67 -78 -109.5t-138 -42.5q-101 0 -171.5 69.5t-70.5 187.5q0 110 68 182t162 72q109 0 170 -68.5t61 -183.5q0 -18 -2 -30h-362q1 -64 42.5 -105t102.5 -41q103 0 135 97zM209 545
h-87l104 139h107l104 -139h-88l-70 81z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="542" 
d="M141 290h263q-2 54 -36 88.5t-96 34.5q-56 0 -92 -36.5t-39 -86.5zM418 165l81 -28q-21 -67 -78 -109.5t-138 -42.5q-101 0 -171.5 69.5t-70.5 187.5q0 110 68 182t162 72q109 0 170 -68.5t61 -183.5q0 -18 -2 -30h-362q1 -64 42.5 -105t102.5 -41q103 0 135 97zM126 621
q0 24 16.5 41t40.5 17q25 0 42 -17t17 -41q0 -25 -17 -41.5t-42 -16.5q-24 0 -40.5 16.5t-16.5 41.5zM313 621q0 24 16.5 41t41.5 17q24 0 41 -17t17 -41q0 -25 -17 -41.5t-41 -16.5q-25 0 -41.5 16.5t-16.5 41.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="253" 
d="M93 543l-126 146h118l86 -146h-78zM173 0h-93v481h93v-481z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="253" 
d="M290 689l-126 -146h-79l87 146h118zM173 0h-93v481h93v-481z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="253" 
d="M173 0h-93v481h93v-481zM57 545h-87l104 139h107l104 -139h-88l-70 81z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="253" 
d="M3 621q0 23 15.5 38.5t37.5 15.5t38 -15.5t16 -38.5q0 -22 -15.5 -37.5t-38.5 -15.5q-22 0 -37.5 15t-15.5 38zM144 621q0 23 15.5 38.5t37.5 15.5q23 0 38.5 -15.5t15.5 -38.5q0 -22 -15.5 -37.5t-38.5 -15.5t-38 15t-15 38zM173 0h-93v481h93v-481z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="570" 
d="M475 639l-81 -40q137 -128 137 -338q0 -120 -66.5 -197.5t-179.5 -77.5q-107 0 -176 68.5t-69 172.5q0 106 69.5 171.5t161.5 65.5q96 0 140 -50q-31 83 -101 143l-131 -65l-29 63l94 47q-47 27 -112 46l29 76q92 -21 174 -77l109 55zM285 70q63 0 106 42t43 115
q0 74 -43 115.5t-106 41.5t-106.5 -42t-43.5 -115t43.5 -115t106.5 -42z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="562" 
d="M174 278v-278h-94v481h92v-69q48 83 148 83q83 0 127.5 -53t44.5 -137v-305h-94v289q0 121 -112 121q-53 0 -82.5 -37.5t-29.5 -94.5zM452 677v-15q0 -53 -27 -81t-70 -28q-31 0 -66 22l-14 9q-23 14 -37 14q-29 0 -29 -43h-72v15q0 53 27.5 81t70.5 28q32 0 67 -22
l14 -9q23 -14 36 -14q29 0 29 43h71z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="572" 
d="M286 69q63 0 106.5 45.5t43.5 126.5q0 80 -43.5 125.5t-106.5 45.5t-106.5 -45.5t-43.5 -125.5q0 -81 43.5 -126.5t106.5 -45.5zM286 496q107 0 176.5 -72.5t69.5 -182.5q0 -111 -69 -183.5t-177 -72.5t-177 72.5t-69 183.5q0 110 69.5 182.5t176.5 72.5zM251 543
l-126 146h118l86 -146h-78z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="572" 
d="M286 69q63 0 106.5 45.5t43.5 126.5q0 80 -43.5 125.5t-106.5 45.5t-106.5 -45.5t-43.5 -125.5q0 -81 43.5 -126.5t106.5 -45.5zM286 496q107 0 176.5 -72.5t69.5 -182.5q0 -111 -69 -183.5t-177 -72.5t-177 72.5t-69 183.5q0 110 69.5 182.5t176.5 72.5zM435 689
l-126 -146h-79l87 146h118z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="572" 
d="M286 69q63 0 106.5 45.5t43.5 126.5q0 80 -43.5 125.5t-106.5 45.5t-106.5 -45.5t-43.5 -125.5q0 -81 43.5 -126.5t106.5 -45.5zM286 496q107 0 176.5 -72.5t69.5 -182.5q0 -111 -69 -183.5t-177 -72.5t-177 72.5t-69 183.5q0 110 69.5 182.5t176.5 72.5zM216 545h-87
l104 139h107l104 -139h-88l-70 81z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="572" 
d="M286 69q63 0 106.5 45.5t43.5 126.5q0 80 -43.5 125.5t-106.5 45.5t-106.5 -45.5t-43.5 -125.5q0 -81 43.5 -126.5t106.5 -45.5zM286 496q107 0 176.5 -72.5t69.5 -182.5q0 -111 -69 -183.5t-177 -72.5t-177 72.5t-69 183.5q0 110 69.5 182.5t176.5 72.5zM445 677v-15
q0 -53 -27 -81t-70 -28q-31 0 -66 22l-14 9q-23 14 -37 14q-29 0 -29 -43h-72v15q0 53 27.5 81t70.5 28q32 0 67 -22l14 -9q23 -14 36 -14q29 0 29 43h71z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="572" 
d="M286 69q63 0 106.5 45.5t43.5 126.5q0 80 -43.5 125.5t-106.5 45.5t-106.5 -45.5t-43.5 -125.5q0 -81 43.5 -126.5t106.5 -45.5zM286 496q107 0 176.5 -72.5t69.5 -182.5q0 -111 -69 -183.5t-177 -72.5t-177 72.5t-69 183.5q0 110 69.5 182.5t176.5 72.5zM134 621
q0 24 16.5 41t40.5 17q25 0 42 -17t17 -41q0 -25 -17 -41.5t-42 -16.5q-24 0 -40.5 16.5t-16.5 41.5zM321 621q0 24 16.5 41t41.5 17q24 0 41 -17t17 -41q0 -25 -17 -41.5t-41 -16.5q-25 0 -41.5 16.5t-16.5 41.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="567" 
d="M218 110q0 26 19.5 45t45.5 19t45 -19t19 -45t-19 -45.5t-45 -19.5t-45.5 19.5t-19.5 45.5zM218 465q0 26 19.5 45t45.5 19t45 -19t19 -45t-19 -45.5t-45 -19.5t-45.5 19.5t-19.5 45.5zM55 246v83h457v-83h-457z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="572" 
d="M286 496q67 0 125 -32l50 65l60 -48l-51 -66q62 -72 62 -174q0 -111 -69 -183.5t-177 -72.5q-67 0 -124 31l-49 -63l-60 48l49 64q-62 72 -62 176q0 110 69.5 182.5t176.5 72.5zM136 241q0 -56 25 -101l196 254q-33 18 -71 18q-63 0 -106.5 -45.5t-43.5 -125.5zM286 69
q63 0 106.5 45.5t43.5 126.5q0 57 -24 99l-196 -254q30 -17 70 -17z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="566" 
d="M397 58q-19 -36 -58 -54.5t-84 -18.5q-82 0 -131 54.5t-49 138.5v303h94v-289q0 -54 26.5 -89t82.5 -35t85 33.5t29 89.5v290h94v-392q0 -47 5 -89h-90q-4 22 -4 58zM256 543l-126 146h118l86 -146h-78z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="566" 
d="M397 58q-19 -36 -58 -54.5t-84 -18.5q-82 0 -131 54.5t-49 138.5v303h94v-289q0 -54 26.5 -89t82.5 -35t85 33.5t29 89.5v290h94v-392q0 -47 5 -89h-90q-4 22 -4 58zM423 689l-126 -146h-79l87 146h118z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="566" 
d="M397 58q-19 -36 -58 -54.5t-84 -18.5q-82 0 -131 54.5t-49 138.5v303h94v-289q0 -54 26.5 -89t82.5 -35t85 33.5t29 89.5v290h94v-392q0 -47 5 -89h-90q-4 22 -4 58zM210 545h-87l104 139h107l104 -139h-88l-70 81z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="566" 
d="M397 58q-19 -36 -58 -54.5t-84 -18.5q-82 0 -131 54.5t-49 138.5v303h94v-289q0 -54 26.5 -89t82.5 -35t85 33.5t29 89.5v290h94v-392q0 -47 5 -89h-90q-4 22 -4 58zM128 621q0 24 16.5 41t40.5 17q25 0 42 -17t17 -41q0 -25 -17 -41.5t-42 -16.5q-24 0 -40.5 16.5
t-16.5 41.5zM315 621q0 24 16.5 41t41.5 17q24 0 41 -17t17 -41q0 -25 -17 -41.5t-41 -16.5q-25 0 -41.5 16.5t-16.5 41.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="523" 
d="M200 -201h-103l120 254l-207 428h108l150 -332l144 332h101zM409 689l-126 -146h-79l87 146h118z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="593" 
d="M173 -190h-93v914h94v-313q21 36 62.5 58.5t95.5 22.5q104 0 162.5 -71t58.5 -180q0 -110 -60.5 -182t-163.5 -72q-53 0 -94 22t-62 56v-255zM457 241q0 73 -38.5 120.5t-103.5 47.5q-64 0 -103.5 -47.5t-39.5 -120.5q0 -75 39.5 -122.5t103.5 -47.5t103 47.5t39 122.5z
" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="523" 
d="M200 -201h-103l120 254l-207 428h108l150 -332l144 332h101zM110 621q0 24 16.5 41t40.5 17q25 0 42 -17t17 -41q0 -25 -17 -41.5t-42 -16.5q-24 0 -40.5 16.5t-16.5 41.5zM297 621q0 24 16.5 41t41.5 17q24 0 41 -17t17 -41q0 -25 -17 -41.5t-41 -16.5q-25 0 -41.5 16.5
t-16.5 41.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="253" 
d="M173 0h-93v481h93v-481z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="559" 
d="M534 0h-433v289l-96 -42v87l96 42v333h98v-289l157 69v-87l-157 -70v-240h335v-92z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="288" 
d="M188 0h-94v272l-89 -39v87l89 39v365h94v-323l95 42v-87l-95 -42v-314z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1148" 
d="M145 355q0 -132 75 -206t181 -74t181 74t75 206t-75 205.5t-181 73.5t-181 -73.5t-75 -205.5zM1088 0h-431v93q-102 -108 -256 -108q-91 0 -171 42t-132.5 128t-52.5 200q0 86 30.5 157t81 116.5t113.5 70.5t131 25q154 0 256 -108v93h431v-91h-333v-218h302v-92h-302
v-217h333v-91z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="937" 
d="M286 69q63 0 106.5 45.5t43.5 126.5q0 80 -43.5 125.5t-106.5 45.5t-106.5 -45.5t-43.5 -125.5q0 -81 43.5 -126.5t106.5 -45.5zM536 290h263q-2 54 -36.5 88.5t-95.5 34.5q-56 0 -92 -36.5t-39 -86.5zM813 165l81 -28q-21 -67 -78 -109.5t-138 -42.5q-59 0 -110.5 25
t-84.5 72q-70 -97 -197 -97q-108 0 -177 72.5t-69 183.5q0 110 69.5 182.5t176.5 72.5q127 0 197 -98q32 46 80.5 72t102.5 26q109 0 170 -68.5t61 -183.5q0 -18 -2 -30h-362q1 -63 43 -104.5t102 -41.5q103 0 135 97z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="593" 
d="M542 548l-88 -31q-7 46 -45 83.5t-105 37.5q-59 0 -98 -34t-39 -84q0 -39 23.5 -66t67.5 -37l106 -23q88 -19 136 -71.5t48 -127.5q0 -86 -68 -148t-177 -62q-119 0 -186.5 62.5t-78.5 148.5l94 30q6 -64 50 -108t120 -44q70 0 108 32t38 81q0 40 -27 69.5t-79 40.5
l-101 22q-79 17 -126 67t-47 127q0 84 68.5 147.5t165.5 63.5q109 0 167 -53t73 -123zM133 878h91l81 -64l85 64h92l-117 -119h-120z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="452" 
d="M35 125l85 30q5 -39 34 -64.5t78 -25.5q38 0 60 18.5t22 45.5q0 48 -62 62l-82 18q-56 12 -88 48t-32 87q0 62 50.5 107t120.5 45q46 0 82 -13.5t56 -35.5t30 -41t15 -39l-83 -31q-2 12 -7 23.5t-15.5 25.5t-30.5 22.5t-47 8.5q-35 0 -57.5 -19t-22.5 -45q0 -46 55 -58
l78 -17q64 -14 98.5 -51.5t34.5 -91.5q0 -57 -46.5 -103t-129.5 -46q-89 0 -139 44.5t-57 95.5zM69 687h87l70 -81l70 81h88l-102 -142h-111z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="618" 
d="M135 819q0 25 17 42t42 17t42 -17t17 -42t-17 -42t-42 -17t-42 17t-17 42zM366 819q0 25 17 42t42 17t42 -17t17 -42t-17 -42t-42 -17t-42 17t-17 42zM260 303l-250 406h118l184 -316l184 316h112l-250 -406v-303h-98v303z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="632" 
d="M592 0h-547v98l423 520h-413v91h532v-95l-425 -522h430v-92zM149 878h91l81 -64l85 64h92l-117 -119h-120z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="466" 
d="M78 687h87l70 -81l70 81h88l-102 -142h-111zM426 0h-386v82l264 314h-260v85h377v-82l-264 -314h269v-85z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="481" 
d="M122 20l75 378h-110v83h127l23 113q14 72 60 108.5t105 36.5q42 0 74 -10v-83q-24 7 -53 7q-34 0 -59.5 -18t-36.5 -74l-16 -80h129v-83h-146l-82 -411q-14 -72 -60 -108.5t-105 -36.5q-45 0 -77 10v83q24 -7 56 -7q78 0 96 92z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="385" 
d="M122 545h-87l104 139h107l104 -139h-88l-70 81z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="385" 
d="M35 687h87l70 -81l70 81h88l-102 -142h-111z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="343" 
d="M308 577h-273v81h273v-81z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="362" 
d="M181 545q-67 0 -106 42.5t-40 99.5h73q2 -27 20 -46t53 -19t53 19.5t20 45.5h73q-1 -58 -39 -100t-107 -42z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="174" 
d="M25 616q0 26 18.5 44t43.5 18t43.5 -18t18.5 -44t-18.5 -44t-43.5 -18t-43.5 18t-18.5 44z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="293" 
d="M258 641q0 -45 -33 -77t-79 -32q-45 0 -78 32t-33 77q0 46 33 78t78 32q46 0 79 -32t33 -78zM194 641q0 20 -14 34.5t-35 14.5q-20 0 -33.5 -14.5t-13.5 -34.5t13.5 -34t33.5 -14q21 0 35 14t14 34z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="233" 
d="M33 -115q0 86 102 131l63 -16q-85 -40 -85 -97q0 -24 15.5 -35.5t37.5 -11.5q16 0 32 4v-68q-21 -8 -50 -8q-48 0 -81.5 27.5t-33.5 73.5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="377" 
d="M346 677v-15q0 -53 -27 -81t-70 -28q-31 0 -66 22l-14 9q-23 14 -37 14q-29 0 -29 -43h-72v15q0 53 27.5 81t70.5 28q32 0 67 -22l14 -9q23 -14 36 -14q29 0 29 43h71z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="367" 
d="M191 689l-90 -146h-66l51 146h105zM332 689l-100 -146h-66l61 146h105z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="662" 
d="M0 0l278 709h105l279 -709h-662zM329 597l-190 -507h383z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="796" 
d="M398 636q-100 0 -176.5 -66.5t-76.5 -181.5q0 -101 63.5 -169.5t146.5 -86.5v-132h-305v88h202q-84 31 -145.5 111t-61.5 188q0 153 104 245t249 92t249 -92t104 -245q0 -108 -61.5 -188.5t-146.5 -110.5h203v-88h-305v132q83 18 146.5 86.5t63.5 169.5
q0 115 -76.5 181.5t-176.5 66.5z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="571" 
d="M402 58q-19 -36 -58 -54.5t-84 -18.5q-46 0 -86 20v-195h-94v671h94v-289q0 -54 26.5 -89t82.5 -35t85 33.5t29 89.5v290h94v-392q0 -57 5 -89h-90q-4 22 -4 58z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="615" 
d="M565 5q-29 -12 -61 -12q-58 0 -88 34.5t-30 84.5v287h-165v-399h-94v399h-82v82h518v-82h-83v-277q0 -49 54 -49q18 0 31 4v-72z" />
    <glyph glyph-name="uni2007" unicode="&#x2007;" horiz-adv-x="614" 
 />
    <glyph glyph-name="uni2008" unicode="&#x2008;" horiz-adv-x="279" 
 />
    <glyph glyph-name="uni200B" unicode="&#x200b;" horiz-adv-x="0" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="642" 
d="M577 246h-512v82h512v-82z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1103" 
d="M1028 246h-953v82h953v-82z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="218" 
d="M178 515q0 -25 -18.5 -42.5t-46.5 -17.5q-31 0 -52 22t-21 63q0 81 41.5 125.5t91.5 49.5v-47q-32 -7 -53 -36t-22 -61q8 5 20 5q26 0 43 -16.5t17 -44.5z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="224" 
d="M40 656q0 25 19.5 43.5t48.5 18.5q32 0 54 -23t22 -65q0 -84 -42.5 -130t-95.5 -52v48q33 7 54.5 36.5t22.5 64.5q-7 -5 -20 -5q-27 0 -45 17.5t-18 46.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="224" 
d="M40 61q0 25 19.5 43.5t48.5 18.5q32 0 54 -23t22 -65q0 -84 -42.5 -130t-95.5 -52v48q33 7 54.5 36.5t22.5 64.5q-7 -5 -20 -5q-27 0 -45 17.5t-18 46.5z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="389" 
d="M349 520q0 -25 -18.5 -42.5t-46.5 -17.5q-31 0 -52 22t-21 63q0 81 41.5 125.5t91.5 49.5v-47q-32 -7 -53 -36t-22 -61q7 5 20 5q26 0 43 -16.5t17 -44.5zM178 520q0 -25 -18.5 -42.5t-46.5 -17.5q-31 0 -52 22t-21 63q0 81 41.5 125.5t91.5 49.5v-47q-32 -7 -53 -36
t-22 -61q8 5 20 5q26 0 43 -16.5t17 -44.5z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="389" 
d="M40 655q0 25 18.5 42.5t46.5 17.5q31 0 52 -22t21 -63q0 -80 -41 -124t-92 -51v47q32 7 53 35.5t22 61.5q-8 -5 -20 -5q-26 0 -43 16.5t-17 44.5zM211 655q0 25 18.5 42.5t46.5 17.5q31 0 52 -22t21 -63q0 -80 -41 -124t-92 -51v47q32 7 53 35.5t22 61.5q-7 -5 -20 -5
q-26 0 -43 16.5t-17 44.5z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="389" 
d="M40 58q0 25 18.5 42.5t46.5 17.5q31 0 52 -22t21 -63q0 -80 -41 -124t-92 -51v47q32 7 53 35.5t22 61.5q-8 -5 -20 -5q-26 0 -43 16.5t-17 44.5zM211 58q0 25 18.5 42.5t46.5 17.5q31 0 52 -22t21 -63q0 -80 -41 -124t-92 -51v47q32 7 53 35.5t22 61.5q-7 -5 -20 -5
q-26 0 -43 16.5t-17 44.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="386" 
d="M234 162h-82v317h-127v81h127v149h82v-149h127v-81h-127v-317z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="426" 
d="M45 230h127v249h-127v81h127v149h82v-149h127v-81h-127v-249h127v-81h-127v-149h-82v149h-127v81z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="554" 
d="M70 289q0 86 60.5 147t146.5 61t146.5 -61t60.5 -147q0 -85 -61 -146t-146 -61t-146 61t-61 146z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="932" 
d="M842 59q0 -27 -18.5 -45.5t-45.5 -18.5t-46 18.5t-19 45.5t19 46t46 19t45.5 -19t18.5 -46zM530 59q0 -27 -18.5 -45.5t-45.5 -18.5t-46 18.5t-19 45.5t19 46t46 19t45.5 -19t18.5 -46zM219 59q0 -27 -18.5 -45.5t-45.5 -18.5t-46 18.5t-19 45.5t19 46t46 19t45.5 -19
t18.5 -46z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1055" 
d="M138 498q0 -34 22 -56.5t55 -22.5t55 22.5t22 56.5t-22 56t-55 22t-55 -22t-22 -56zM55 498q0 65 47.5 111t112.5 46t113 -46t48 -111q0 -66 -48 -112t-113 -46t-112.5 46t-47.5 112zM534 150q0 -34 22 -56.5t55 -22.5t55 22.5t22 56.5t-22 56t-55 22t-55 -22t-22 -56z
M227 0h-92l466 648h90zM772 150q0 -34 22 -56.5t55 -22.5t55 22.5t22 56.5t-22 56t-55 22t-55 -22t-22 -56zM451 150q0 65 47.5 111t112.5 46q75 0 119 -60q44 60 119 60q65 0 113 -46t48 -111q0 -66 -48 -112t-113 -46q-75 0 -119 59q-44 -59 -119 -59q-65 0 -112.5 46
t-47.5 112z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="276" 
d="M256 99h-93l-133 191l133 191h93l-131 -191z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="276" 
d="M113 99h-93l131 191l-131 191h93l133 -191z" />
    <glyph glyph-name="uni2042" unicode="&#x2042;" horiz-adv-x="835" 
d="M453 709v-121l111 38l20 -65l-111 -36l74 -95l-55 -41l-75 98l-70 -97l-55 41l71 94l-112 36l21 65l110 -38v121h71zM237 312v-121l111 38l20 -65l-111 -36l74 -95l-55 -41l-75 98l-70 -97l-55 41l71 94l-112 36l21 65l110 -38v121h71zM669 312v-121l111 38l20 -65
l-111 -36l74 -95l-55 -41l-75 98l-70 -97l-55 41l71 94l-112 36l21 65l110 -38v121h71z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="556" 
d="M92 0h-92l466 648h90z" />
    <glyph glyph-name="uni2051" unicode="&#x2051;" horiz-adv-x="423" 
d="M247 709v-121l111 38l20 -65l-111 -36l74 -95l-55 -41l-75 98l-70 -97l-55 41l71 94l-112 36l21 65l110 -38v121h71zM247 312v-121l111 38l20 -65l-111 -36l74 -95l-55 -41l-75 98l-70 -97l-55 41l71 94l-112 36l21 65l110 -38v121h71z" />
    <glyph glyph-name="uni2070" unicode="&#x2070;" horiz-adv-x="426" 
d="M138 526q0 -123 75 -123t75 123q0 124 -75 124t-75 -124zM60 526q0 84 35 132q41 58 118 58t118 -58q35 -48 35 -132q0 -83 -35 -131q-41 -58 -118 -58t-118 58q-35 48 -35 131z" />
    <glyph glyph-name="uni2074" unicode="&#x2074;" horiz-adv-x="446" 
d="M60 416v73l177 220h85v-228h64v-65h-64v-72h-77v72h-185zM245 481v139l-111 -139h111z" />
    <glyph glyph-name="uni2075" unicode="&#x2075;" horiz-adv-x="404" 
d="M60 429l66 30q7 -26 26.5 -41.5t46.5 -15.5q30 0 48.5 16t18.5 45q0 30 -18.5 46t-48.5 16q-37 0 -64 -27l-64 24l49 187h204v-66h-154l-23 -86q25 28 73 28q56 0 90 -32.5t34 -86.5q0 -55 -39 -92t-106 -37q-52 0 -90.5 27.5t-48.5 64.5z" />
    <glyph glyph-name="uni2076" unicode="&#x2076;" horiz-adv-x="418" 
d="M340 698l-18 -57q-29 13 -62 13q-49 0 -82 -27t-41 -78q13 19 38 31t53 12q59 0 94.5 -33.5t35.5 -92.5q0 -57 -42 -93t-105 -36q-61 0 -106 43t-45 124q0 96 54.5 154.5t139.5 58.5q54 0 86 -19zM143 465q0 -29 20.5 -46t48.5 -17q27 0 47.5 17t20.5 46t-20.5 46
t-47.5 17q-28 0 -48.5 -17t-20.5 -46z" />
    <glyph glyph-name="uni2077" unicode="&#x2077;" horiz-adv-x="417" 
d="M357 709v-69q-19 -13 -37.5 -31.5t-45 -53.5t-46 -90t-25.5 -121h-82q5 55 21 106.5t35 85t37 59t30 37.5l13 11h-197v66h297z" />
    <glyph glyph-name="uni2078" unicode="&#x2078;" horiz-adv-x="410" 
d="M205 561q26 0 41.5 13t15.5 35q0 20 -15.5 32.5t-41.5 12.5t-41.5 -12.5t-15.5 -32.5q0 -22 15.5 -35t41.5 -13zM205 401q32 0 49.5 13.5t17.5 36.5q0 22 -17.5 36t-49.5 14t-49.5 -14t-17.5 -36q0 -23 17.5 -36.5t49.5 -13.5zM205 337q-68 0 -106.5 30.5t-38.5 77.5
q0 27 18 50.5t50 35.5q-26 11 -41 33t-15 47q0 46 37.5 75.5t95.5 29.5t95.5 -29.5t37.5 -75.5q0 -25 -15 -47t-41 -33q32 -12 50 -35.5t18 -50.5q0 -47 -38.5 -77.5t-106.5 -30.5z" />
    <glyph glyph-name="uni2079" unicode="&#x2079;" horiz-adv-x="416" 
d="M78 356l18 59q29 -13 62 -13q51 0 84 25.5t39 77.5q-13 -19 -38 -31t-53 -12q-57 0 -93.5 34t-36.5 92q0 56 42 92.5t105 36.5q61 0 105 -43t44 -124q0 -101 -53 -157t-139 -56q-54 0 -86 19zM273 588q0 29 -20 46.5t-49 17.5q-28 0 -48 -17t-20 -47t20 -47t48 -17
q29 0 49 17.5t20 46.5z" />
    <glyph glyph-name="uni2080" unicode="&#x2080;" horiz-adv-x="426" 
d="M138 110q0 -123 75 -123t75 123q0 124 -75 124t-75 -124zM60 110q0 84 35 132q41 58 118 58t118 -58q35 -48 35 -132q0 -83 -35 -131q-41 -58 -118 -58t-118 58q-35 48 -35 131z" />
    <glyph glyph-name="uni2081" unicode="&#x2081;" horiz-adv-x="303" 
d="M213 -72h-77v245h-86v52q36 1 61.5 20.5t30.5 47.5h71v-365z" />
    <glyph glyph-name="uni2082" unicode="&#x2082;" horiz-adv-x="389" 
d="M141 152h-74q-3 15 -3 31q0 49 36 83t96 34q59 0 95 -33.5t36 -81.5q0 -66 -70 -110l-72 -45q-27 -18 -31 -37h175v-65h-268v13q0 93 85 145l60 37q43 26 43 63q0 21 -14.5 35.5t-39.5 14.5q-26 0 -41.5 -16t-15.5 -42q0 -12 3 -26z" />
    <glyph glyph-name="uni2083" unicode="&#x2083;" horiz-adv-x="412" 
d="M178 94l-29 56l95 79h-171v65h271v-63l-100 -80q46 -4 77 -32.5t31 -76.5q0 -51 -40.5 -86t-104.5 -35q-61 0 -99 30t-48 73l67 27q5 -29 26.5 -46.5t52.5 -17.5q30 0 49 15.5t19 39.5q0 29 -20 42.5t-48 13.5q-14 0 -28 -4z" />
    <glyph glyph-name="uni2084" unicode="&#x2084;" horiz-adv-x="446" 
d="M60 0v73l177 220h85v-228h64v-65h-64v-72h-77v72h-185zM245 65v139l-111 -139h111z" />
    <glyph glyph-name="uni2085" unicode="&#x2085;" horiz-adv-x="404" 
d="M60 13l66 30q7 -26 26.5 -41.5t46.5 -15.5q30 0 48.5 16t18.5 45q0 30 -18.5 46t-48.5 16q-37 0 -64 -27l-64 24l49 187h204v-66h-154l-23 -86q25 28 73 28q56 0 90 -32.5t34 -86.5q0 -55 -39 -92t-106 -37q-52 0 -90.5 27.5t-48.5 64.5z" />
    <glyph glyph-name="uni2086" unicode="&#x2086;" horiz-adv-x="418" 
d="M340 282l-18 -57q-29 13 -62 13q-49 0 -82 -27t-41 -78q13 19 38 31t53 12q59 0 94.5 -33.5t35.5 -92.5q0 -57 -42 -93t-105 -36q-61 0 -106 43t-45 124q0 96 54.5 154.5t139.5 58.5q54 0 86 -19zM143 49q0 -29 20.5 -46t48.5 -17q27 0 47.5 17t20.5 46t-20.5 46
t-47.5 17q-28 0 -48.5 -17t-20.5 -46z" />
    <glyph glyph-name="uni2087" unicode="&#x2087;" horiz-adv-x="417" 
d="M357 293v-69q-19 -13 -37.5 -31.5t-45 -53.5t-46 -90t-25.5 -121h-82q5 55 21 106.5t35 85t37 59t30 37.5l13 11h-197v66h297z" />
    <glyph glyph-name="uni2088" unicode="&#x2088;" horiz-adv-x="410" 
d="M205 145q26 0 41.5 13t15.5 35q0 20 -15.5 32.5t-41.5 12.5t-41.5 -12.5t-15.5 -32.5q0 -22 15.5 -35t41.5 -13zM205 -15q32 0 49.5 13.5t17.5 36.5q0 22 -17.5 36t-49.5 14t-49.5 -14t-17.5 -36q0 -23 17.5 -36.5t49.5 -13.5zM205 -79q-68 0 -106.5 30.5t-38.5 77.5
q0 27 18 50.5t50 35.5q-26 11 -41 33t-15 47q0 46 37.5 75.5t95.5 29.5t95.5 -29.5t37.5 -75.5q0 -25 -15 -47t-41 -33q32 -12 50 -35.5t18 -50.5q0 -47 -38.5 -77.5t-106.5 -30.5z" />
    <glyph glyph-name="uni2089" unicode="&#x2089;" horiz-adv-x="416" 
d="M78 -60l18 59q29 -13 62 -13q51 0 84 25.5t39 77.5q-13 -19 -38 -31t-53 -12q-57 0 -93.5 34t-36.5 92q0 56 42 92.5t105 36.5q61 0 105 -43t44 -124q0 -101 -53 -157t-139 -56q-54 0 -86 19zM273 172q0 29 -20 46.5t-49 17.5q-28 0 -48 -17t-20 -47t20 -47t48 -17
q29 0 49 17.5t20 46.5z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="689" 
d="M65 282h79q-2 22 -2 43q0 18 2 40h-79v78h94q34 105 121 162.5t189 57.5q105 0 171 -47l-40 -77q-51 37 -131 37q-69 0 -125 -34t-83 -99h293l-39 -78h-273q-3 -18 -3 -40q0 -28 3 -43h237l-39 -80h-177q27 -64 81 -97t122 -33q95 0 149 54l49 -70q-83 -71 -198 -71
q-100 0 -185 57t-120 160h-96v80z" />
    <glyph glyph-name="uni2113" unicode="&#x2113;" horiz-adv-x="407" 
d="M35 259q31 8 68 25v222q0 73 36.5 115.5t97.5 42.5q64 0 99.5 -39.5t35.5 -110.5q0 -79 -46.5 -155.5t-128.5 -127.5v-71q0 -76 67 -78q56 0 90 29v-85q-7 -10 -31 -20.5t-63 -10.5q-78 0 -117.5 44t-39.5 116v33q-37 -12 -68 -17v88zM278 514q0 64 -39 64q-42 0 -42 -71
v-164q81 74 81 171z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="859" 
d="M330 641h-112v-297h-76v297h-112v68h300v-68zM799 344h-72v233l-101 -233h-59l-98 230v-230h-76v365h99l107 -254l107 254h93v-365z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="796" 
d="M398 636q-100 0 -176.5 -66.5t-76.5 -181.5q0 -101 63.5 -169.5t146.5 -86.5v-132h-305v88h202q-84 31 -145.5 111t-61.5 188q0 153 104 245t249 92t249 -92t104 -245q0 -108 -61.5 -188.5t-146.5 -110.5h203v-88h-305v132q83 18 146.5 86.5t63.5 169.5
q0 115 -76.5 181.5t-176.5 66.5z" />
    <glyph glyph-name="uni2160" unicode="&#x2160;" horiz-adv-x="506" 
d="M451 648v-88h-150v-472h150v-88h-396v88h150v472h-150v88h396z" />
    <glyph glyph-name="uni2161" unicode="&#x2161;" horiz-adv-x="770" 
d="M715 88v-88h-660v88h140v472h-140v88h660v-88h-140v-472h140zM479 88v472h-188v-472h188z" />
    <glyph glyph-name="uni2162" unicode="&#x2162;" horiz-adv-x="1044" 
d="M989 88v-88h-934v88h140v472h-140v88h934v-88h-140v-472h140zM753 88v472h-183v-472h183zM291 560v-472h183v472h-183z" />
    <glyph glyph-name="uni2163" unicode="&#x2163;" horiz-adv-x="1006" 
d="M951 88v-88h-896v88h130v472h-130v88h895v-88h-115l-163 -472h279zM281 560v-472h240l-160 472h-80zM599 141l137 419h-273z" />
    <glyph glyph-name="uni2164" unicode="&#x2164;" horiz-adv-x="803" 
d="M748 648v-88h-110l-163 -472h269v-88h-684v88h264l-160 472h-109v88h693zM402 141l137 419h-273z" />
    <glyph glyph-name="uni2165" unicode="&#x2165;" horiz-adv-x="1006" 
d="M951 88v-88h-896v88h274l-160 472h-114v88h895v-88h-129v-472h130zM643 560l-163 -472h245v472h-82zM407 141l137 419h-273z" />
    <glyph glyph-name="uni2166" unicode="&#x2166;" horiz-adv-x="1293" 
d="M1238 88v-88h-1183v88h273l-159 472h-114v88h1183v-88h-140v-472h140zM1002 88v472h-182v-472h182zM642 560l-163 -472h245v472h-82zM406 141l137 419h-272z" />
    <glyph glyph-name="uni2167" unicode="&#x2167;" horiz-adv-x="1542" 
d="M1487 88v-88h-1432v88h270l-156 472h-114v88h1432v-88h-135v-472h135zM814 560v-472h173v472h-173zM1083 560v-472h173v472h-173zM634 560l-160 -472h244v472h-84zM402 141l132 419h-263z" />
    <glyph glyph-name="uni2168" unicode="&#x2168;" horiz-adv-x="976" 
d="M921 88v-88h-866v88h130v472h-130v88h865v-88h-105l-170 -243l161 -229h115zM538 317l-171 243h-86v-472h92zM477 560l114 -168l115 168h-229zM590 243l-109 -155h215z" />
    <glyph glyph-name="uni2169" unicode="&#x2169;" horiz-adv-x="767" 
d="M711 648v-88h-105l-170 -243l161 -229h115v-88h-657v88h109l165 229l-171 243h-103v88h656zM268 560l114 -168l115 168h-229zM381 243l-109 -155h215z" />
    <glyph glyph-name="uni216C" unicode="&#x216c;" horiz-adv-x="654" 
d="M599 281v-281h-544v88h130v472h-130v88h366v-88h-140v-472h222v193h96z" />
    <glyph glyph-name="uni216D" unicode="&#x216d;" horiz-adv-x="722" 
d="M667 231v-231h-96v67q-65 -81 -192 -81q-130 0 -227 92t-97 247t97 247t227 92q126 0 190 -77v63h96v-226h-96q-8 70 -57 111t-133 41q-98 0 -162.5 -64.5t-64.5 -186.5t64.5 -186.5t162.5 -64.5q90 0 139 47.5t53 109.5h96z" />
    <glyph glyph-name="uni216E" unicode="&#x216e;" horiz-adv-x="735" 
d="M55 648h321q127 0 215.5 -86t88.5 -239q0 -152 -89.5 -237.5t-220.5 -85.5h-315v88h135v472h-135v88zM286 88h86q87 0 148.5 60.5t61.5 174.5q0 113 -60 175t-152 62h-84v-472z" />
    <glyph glyph-name="uni216F" unicode="&#x216f;" horiz-adv-x="1069" 
d="M321 648l210 -515l212 515h271v-88h-132v-472h132v-88h-325v88h99v428l-219 -516h-76l-217 516v-428h104v-88h-325v88h127v472h-127v88h266z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="1026" 
d="M221 102l-221 185l221 186v-144h735v-83h-735v-144z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="449" 
d="M240 800l199 -245h-158v-703h-83v703h-158z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="1026" 
d="M982 287l-252 -211v169h-696v83h696v171z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="449" 
d="M422 90l-212 -252l-211 252h169v696h83v-696h171z" />
    <glyph glyph-name="uni2196" unicode="&#x2196;" horiz-adv-x="874" 
d="M55 709h292l-116 -117l543 -542l-59 -58l-543 541l-117 -118v294z" />
    <glyph glyph-name="uni2197" unicode="&#x2197;" horiz-adv-x="773" 
d="M381 709h292v-294l-118 118l-542 -541l-59 58l543 542z" />
    <glyph glyph-name="uni2198" unicode="&#x2198;" horiz-adv-x="874" 
d="M774 0h-292l116 117l-543 542l59 58l542 -542l118 119v-294z" />
    <glyph glyph-name="uni2199" unicode="&#x2199;" horiz-adv-x="764" 
d="M337 0h-292v294l117 -119l543 542l59 -58l-543 -542z" />
    <glyph glyph-name="uni21A9" unicode="&#x21a9;" horiz-adv-x="833" 
d="M398 581v83h192q90 0 153.5 -60.5t63.5 -148.5q0 -91 -65.5 -150t-155.5 -59h-367v-144l-221 185l221 186v-144h362q59 0 101.5 34t42.5 92t-42 92t-105 34h-180z" />
    <glyph glyph-name="uni21B0" unicode="&#x21b0;" horiz-adv-x="775" 
d="M271 353l-221 185l221 186v-144h414v-580h-83v497h-331v-144z" />
    <glyph glyph-name="uni21B1" unicode="&#x21b1;" horiz-adv-x="720" 
d="M118 0h-83v580h464v144l221 -186l-221 -185v144h-381v-497z" />
    <glyph glyph-name="uni21B2" unicode="&#x21b2;" horiz-adv-x="760" 
d="M592 709h83v-580h-464v-144l-221 186l221 185v-144h381v497z" />
    <glyph glyph-name="uni21B3" unicode="&#x21b3;" horiz-adv-x="840" 
d="M549 356l221 -185l-221 -186v144h-464v580h83v-497h381v144z" />
    <glyph glyph-name="uni21B4" unicode="&#x21b4;" horiz-adv-x="814" 
d="M55 626v83h580v-504h144l-186 -221l-185 221h144v421h-497z" />
    <glyph glyph-name="uni21C6" unicode="&#x21c6;" horiz-adv-x="1106" 
d="M276 355l-221 185l221 186v-144h755v-83h-755v-144zM1051 170l-221 -185v144h-755v83h755v144z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="611" 
d="M247 638q-76 0 -140 -31l-32 74q74 43 180 43q40 0 78.5 -9t81 -34.5t74 -65.5t52.5 -109t21 -158q0 -170 -71 -266.5t-204 -96.5q-99 0 -170 62t-71 174q0 104 71.5 167.5t174.5 63.5q116 0 171 -57q-20 243 -216 243zM306 369q-78 0 -121 -44t-43 -110t44.5 -104.5
t105.5 -38.5q86 0 129 67t43 157q-26 35 -68 54t-90 19z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="662" 
d="M0 0l278 709h105l279 -709h-662zM329 597l-190 -507h383z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="687" 
d="M597 0h-95v622h-314v-622h-98v709h507v-709z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="567" 
d="M45 0v86l273 271l-273 266v86h497v-90h-365l269 -262l-278 -267h374v-90h-497z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="533" 
d="M478 248h-423v79h423v-79z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="404" 
d="M404 648l-313 -648h-91l313 648h91z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="279" 
d="M70 284q0 29 20 49.5t49 20.5t49.5 -20.5t20.5 -49.5t-20.5 -49t-49.5 -20t-49 20t-20 49z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="665" 
d="M700 780h-119l-266 -780h-91l-189 529h108l127 -374l235 713h195v-88z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="801" 
d="M399 228q-48 -54 -90.5 -80t-89.5 -26q-75 0 -124.5 48t-49.5 120t49.5 120.5t124.5 48.5q97 0 183 -107q90 107 180 107q75 0 124.5 -48.5t49.5 -120.5t-49.5 -120t-124.5 -48q-99 0 -183 106zM221 379q-42 0 -67.5 -25.5t-25.5 -64.5t25.5 -64t67.5 -25q58 0 129 87
q-26 32 -42 48.5t-39.5 30t-47.5 13.5zM580 200q42 0 67.5 25t25.5 64t-25.5 64.5t-67.5 25.5q-24 0 -47.5 -13t-39 -28.5t-42.5 -47.5q37 -45 66 -67.5t63 -22.5z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="310" 
d="M109 -33v584q0 74 42.5 116t116.5 42h37v-86h-30q-34 0 -53 -20t-19 -59v-585q0 -74 -41 -116.5t-114 -42.5h-43v86h31q33 0 53 21t20 60z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="569" 
d="M483 429l51 -45q-58 -83 -146 -83q-48 0 -100 30l-47 26q-32 17 -62 17q-52 0 -91 -45l-53 47q59 85 143 85q46 0 103 -31l46 -25q35 -20 70 -20q49 0 86 44zM483 235l51 -45q-58 -83 -146 -83q-48 0 -100 30l-47 26q-32 17 -62 17q-52 0 -91 -45l-53 47q59 85 143 85
q46 0 103 -31l46 -25q35 -20 70 -20q49 0 86 44z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="593" 
d="M528 151h-282l-74 -100h-96l74 100h-85v79h143l85 116h-228v77h285l73 99h96l-73 -99h82v-77h-139l-85 -116h224v-79z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="542" 
d="M65 130h412v-79h-412v79zM65 304v85l412 146v-84l-307 -104l307 -104v-84z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="542" 
d="M65 130h412v-79h-412v79zM477 389v-85l-412 -145v84l307 104l-307 104v84z" />
    <glyph glyph-name="dotmath" unicode="&#x22c5;" horiz-adv-x="279" 
d="M70 284q0 29 20 49.5t49 20.5t49.5 -20.5t20.5 -49.5t-20.5 -49t-49.5 -20t-49 20t-20 49z" />
    <glyph glyph-name="uni24C5" unicode="&#x24c5;" horiz-adv-x="821" 
d="M123 355q0 -126 83 -211.5t203 -85.5t203 85.5t83 211.5t-83 211t-203 85t-203 -85t-83 -211zM45 355q0 153 106.5 261t257.5 108q152 0 259.5 -108.5t107.5 -260.5q0 -153 -107.5 -261.5t-259.5 -108.5q-151 0 -257.5 108.5t-106.5 261.5zM363 367h70q33 0 52.5 17
t19.5 45q0 29 -19 46.5t-53 17.5h-70v-126zM446 301h-83v-144h-76v402h159q58 0 97 -37.5t39 -91.5t-39 -91.5t-97 -37.5z" />
    <glyph glyph-name="filledbox" unicode="&#x25a0;" horiz-adv-x="921" 
d="M110 0v709h701v-709h-701z" />
    <glyph glyph-name="H22073" unicode="&#x25a1;" horiz-adv-x="921" 
d="M811 0h-701v709h701v-709zM192 633v-558h537v558h-537z" />
    <glyph glyph-name="triagup" unicode="&#x25b2;" horiz-adv-x="837" 
d="M420 724l417 -724h-837z" />
    <glyph glyph-name="uni25B3" unicode="&#x25b3;" horiz-adv-x="837" 
d="M420 724l417 -724h-837zM696 75l-277 486l-279 -486h556z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="556" 
d="M137 354l141 -242l140 242l-140 243zM35 354l202 355h82l202 -355l-202 -354h-82z" />
    <glyph glyph-name="circle" unicode="&#x25cb;" horiz-adv-x="831" 
d="M45 354q0 154 108 262.5t262 108.5t262.5 -108.5t108.5 -262.5t-108.5 -262t-262.5 -108t-262 108t-108 262zM129 354q0 -122 83 -206.5t203 -84.5t203 84.5t83 206.5t-83 206.5t-203 84.5t-203 -84.5t-83 -206.5z" />
    <glyph glyph-name="H18533" unicode="&#x25cf;" horiz-adv-x="831" 
d="M45 354q0 154 108 262.5t262 108.5t262.5 -108.5t108.5 -262.5t-108.5 -262t-262.5 -108t-262 108t-108 262z" />
    <glyph glyph-name="uni262E" unicode="&#x262e;" horiz-adv-x="821" 
d="M776 355q0 -153 -107.5 -261.5t-259.5 -108.5q-151 0 -257.5 108.5t-106.5 261.5t106.5 261t257.5 108q152 0 259.5 -108.5t107.5 -260.5zM695 355q0 114 -70 196t-176 97v-240l206 -207q40 72 40 154zM123 355q0 -83 39 -152l205 205v240q-106 -15 -175 -97t-69 -196z
M449 61q89 12 155 75l-155 156v-231zM212 137q64 -64 155 -76v231z" />
    <glyph glyph-name="uni2713" unicode="&#x2713;" horiz-adv-x="970" 
d="M945 670l-638 -687l-282 315l60 57l226 -247l574 621z" />
    <glyph glyph-name="uni2715" unicode="&#x2715;" horiz-adv-x="756" 
d="M672 -5l-294 300l-294 -300l-59 59l294 301l-293 300l60 59l292 -299l292 299l60 -59l-294 -300l295 -301z" />
    <glyph glyph-name="uni2780" unicode="&#x2780;" horiz-adv-x="821" 
d="M123 355q0 -126 83 -211.5t203 -85.5t203 85.5t83 211.5t-83 211t-203 85t-203 -85t-83 -211zM45 355q0 153 106.5 261t257.5 108q152 0 259.5 -108.5t107.5 -260.5q0 -153 -107.5 -261.5t-259.5 -108.5q-151 0 -257.5 108.5t-106.5 261.5zM462 173h-77v245h-86v52
q36 1 61.5 20.5t30.5 47.5h71v-365z" />
    <glyph glyph-name="uni2781" unicode="&#x2781;" horiz-adv-x="821" 
d="M123 355q0 -126 83 -211.5t203 -85.5t203 85.5t83 211.5t-83 211t-203 85t-203 -85t-83 -211zM45 355q0 153 106.5 261t257.5 108q152 0 259.5 -108.5t107.5 -260.5q0 -153 -107.5 -261.5t-259.5 -108.5q-151 0 -257.5 108.5t-106.5 261.5zM350 402h-74q-3 15 -3 31
q0 49 36 83t96 34q59 0 95 -33.5t36 -81.5q0 -66 -70 -110l-72 -45q-27 -18 -31 -37h175v-65h-268v13q0 93 85 145l60 37q43 26 43 63q0 21 -14.5 35.5t-39.5 14.5q-26 0 -41.5 -16t-15.5 -42q0 -12 3 -26z" />
    <glyph glyph-name="uni2782" unicode="&#x2782;" horiz-adv-x="821" 
d="M45 355q0 153 106.5 261t257.5 108q152 0 259.5 -108.5t107.5 -260.5q0 -153 -107.5 -261.5t-259.5 -108.5q-151 0 -257.5 108.5t-106.5 261.5zM123 355q0 -126 83 -211.5t203 -85.5t203 85.5t83 211.5t-83 211t-203 85t-203 -85t-83 -211zM374 337l-29 56l95 79h-171v65
h271v-63l-100 -80q46 -4 77 -32.5t31 -76.5q0 -51 -40.5 -86t-104.5 -35q-61 0 -99 30t-48 73l67 27q5 -29 26.5 -46.5t52.5 -17.5q30 0 49 15.5t19 39.5q0 29 -20 42.5t-48 13.5q-14 0 -28 -4z" />
    <glyph glyph-name="uni2783" unicode="&#x2783;" horiz-adv-x="821" 
d="M123 355q0 -126 83 -211.5t203 -85.5t203 85.5t83 211.5t-83 211t-203 85t-203 -85t-83 -211zM45 355q0 153 106.5 261t257.5 108q152 0 259.5 -108.5t107.5 -260.5q0 -153 -107.5 -261.5t-259.5 -108.5q-151 0 -257.5 108.5t-106.5 261.5zM235 250v73l177 220h85v-228
h64v-65h-64v-72h-77v72h-185zM420 315v139l-111 -139h111z" />
    <glyph glyph-name="uni2784" unicode="&#x2784;" horiz-adv-x="821" 
d="M123 355q0 -126 83 -211.5t203 -85.5t203 85.5t83 211.5t-83 211t-203 85t-203 -85t-83 -211zM45 355q0 153 106.5 261t257.5 108q152 0 259.5 -108.5t107.5 -260.5q0 -153 -107.5 -261.5t-259.5 -108.5q-151 0 -257.5 108.5t-106.5 261.5zM257 255l66 30
q7 -26 26.5 -41.5t46.5 -15.5q30 0 48.5 16t18.5 45q0 30 -18.5 46t-48.5 16q-37 0 -64 -27l-64 24l49 187h204v-66h-154l-23 -86q25 28 73 28q56 0 90 -32.5t34 -86.5q0 -55 -39 -92t-106 -37q-52 0 -90.5 27.5t-48.5 64.5z" />
    <glyph glyph-name="uni2785" unicode="&#x2785;" horiz-adv-x="821" 
d="M123 355q0 -126 83 -211.5t203 -85.5t203 85.5t83 211.5t-83 211t-203 85t-203 -85t-83 -211zM45 355q0 153 106.5 261t257.5 108q152 0 259.5 -108.5t107.5 -260.5q0 -153 -107.5 -261.5t-259.5 -108.5q-151 0 -257.5 108.5t-106.5 261.5zM528 527l-18 -57
q-29 13 -62 13q-49 0 -82 -27t-41 -78q13 19 38 31t53 12q59 0 94.5 -33.5t35.5 -92.5q0 -57 -42 -93t-105 -36q-61 0 -106 43t-45 124q0 96 54.5 154.5t139.5 58.5q54 0 86 -19zM331 294q0 -29 20.5 -46t48.5 -17q27 0 47.5 17t20.5 46t-20.5 46t-47.5 17q-28 0 -48.5 -17
t-20.5 -46z" />
    <glyph glyph-name="uni2786" unicode="&#x2786;" horiz-adv-x="821" 
d="M123 355q0 -126 83 -211.5t203 -85.5t203 85.5t83 211.5t-83 211t-203 85t-203 -85t-83 -211zM45 355q0 153 106.5 261t257.5 108q152 0 259.5 -108.5t107.5 -260.5q0 -153 -107.5 -261.5t-259.5 -108.5q-151 0 -257.5 108.5t-106.5 261.5zM559 533v-69
q-19 -13 -37.5 -31.5t-45 -53.5t-46 -90t-25.5 -121h-82q5 55 21 106.5t35 85t37 59t30 37.5l13 11h-197v66h297z" />
    <glyph glyph-name="uni2787" unicode="&#x2787;" horiz-adv-x="821" 
d="M123 355q0 -126 83 -211.5t203 -85.5t203 85.5t83 211.5t-83 211t-203 85t-203 -85t-83 -211zM45 355q0 153 106.5 261t257.5 108q152 0 259.5 -108.5t107.5 -260.5q0 -153 -107.5 -261.5t-259.5 -108.5q-151 0 -257.5 108.5t-106.5 261.5zM408 390q26 0 41.5 13t15.5 35
q0 20 -15.5 32.5t-41.5 12.5t-41.5 -12.5t-15.5 -32.5q0 -22 15.5 -35t41.5 -13zM408 230q32 0 49.5 13.5t17.5 36.5q0 22 -17.5 36t-49.5 14t-49.5 -14t-17.5 -36q0 -23 17.5 -36.5t49.5 -13.5zM408 166q-68 0 -106.5 30.5t-38.5 77.5q0 27 18 50.5t50 35.5q-26 11 -41 33
t-15 47q0 46 37.5 75.5t95.5 29.5t95.5 -29.5t37.5 -75.5q0 -25 -15 -47t-41 -33q32 -12 50 -35.5t18 -50.5q0 -47 -38.5 -77.5t-106.5 -30.5z" />
    <glyph glyph-name="uni2788" unicode="&#x2788;" horiz-adv-x="821" 
d="M123 355q0 -126 83 -211.5t203 -85.5t203 85.5t83 211.5t-83 211t-203 85t-203 -85t-83 -211zM45 355q0 153 106.5 261t257.5 108q152 0 259.5 -108.5t107.5 -260.5q0 -153 -107.5 -261.5t-259.5 -108.5q-151 0 -257.5 108.5t-106.5 261.5zM281 183l18 59q29 -13 62 -13
q51 0 84 25.5t39 77.5q-13 -19 -38 -31t-53 -12q-57 0 -93.5 34t-36.5 92q0 56 42 92.5t105 36.5q61 0 105 -43t44 -124q0 -101 -53 -157t-139 -56q-54 0 -86 19zM476 415q0 29 -20 46.5t-49 17.5q-28 0 -48 -17t-20 -47t20 -47t48 -17q29 0 49 17.5t20 46.5z" />
    <glyph glyph-name="uniE000" unicode="&#xe000;" horiz-adv-x="814" 
d="M221 -16l-186 221h144v504h580v-83h-497v-421h144z" />
    <glyph glyph-name="uni21B5" unicode="&#xe001;" horiz-adv-x="1080" 
d="M224 145l-179 210h131q1 153 109 261t260 108q97 0 182 -47l-37 -74q-68 38 -144 38q-120 0 -203 -83.5t-83 -202.5h143zM914 354q-1 -154 -109 -261.5t-260 -107.5q-97 0 -182 47l37 74q68 -38 144 -38q120 0 203 83.5t83 202.5h-138l179 210l179 -210h-136z" />
    <glyph glyph-name="uniE002" unicode="&#xe002;" horiz-adv-x="949" 
d="M561 354l179 210l179 -210h-130q0 -153 -108 -261t-262 -108q-153 0 -261 108t-108 261q0 154 108 262t261 108q97 0 182 -47l-37 -74q-68 38 -144 38q-120 0 -203 -83.5t-83 -203.5t83 -204t203 -84t202.5 84t82.5 204h-144z" />
    <glyph glyph-name="uniFB00" unicode="&#xfb00;" horiz-adv-x="659" 
d="M598 639q-31 0 -53.5 -18.5t-22.5 -63.5v-76h116v-85h-116v-396h-97v396h-219v-396h-97v396h-89v85h89v81q0 77 45 121t113 44q43 0 56 -10v-84q-15 6 -41 6q-31 0 -53.5 -18.5t-22.5 -63.5v-76h219v81q0 77 45 121t113 44q43 0 56 -10v-84q-15 6 -41 6z" />
    <glyph glyph-name="fi" unicode="&#xfb01;" horiz-adv-x="604" 
d="M267 727q43 0 56 -10v-84q-15 6 -41 6q-31 0 -53.5 -18t-22.5 -63v-77h318v-481h-93v396h-225v-396h-97v396h-89v85h89v81q0 77 45 121t113 44zM410 665q0 28 19.5 48t47.5 20q29 0 48.5 -19.5t19.5 -48.5q0 -28 -20 -47.5t-48 -19.5t-47.5 19.5t-19.5 47.5z" />
    <glyph glyph-name="fl" unicode="&#xfb02;" horiz-adv-x="602" 
d="M282 639q-31 0 -53.5 -18.5t-22.5 -63.5v-76h222v243h94v-724h-94v396h-222v-396h-97v396h-89v85h89v81q0 77 45 121t113 44q43 0 56 -10v-84q-15 6 -41 6z" />
    <glyph glyph-name="uniFB03" unicode="&#xfb03;" horiz-adv-x="920" 
d="M109 481v81q0 77 45 121t113 44q43 0 56 -10v-84q-15 6 -41 6q-31 0 -53.5 -18.5t-22.5 -63.5v-76h219v81q0 77 45 121t113 44q43 0 56 -10v-84q-15 6 -41 6q-31 0 -53.5 -18.5t-22.5 -63.5v-76h318v-481h-93v396h-225v-396h-97v396h-219v-396h-97v396h-89v85h89z
M726 665q0 28 19.5 48t47.5 20q29 0 48.5 -19.5t19.5 -48.5q0 -28 -20 -47.5t-48 -19.5t-47.5 19.5t-19.5 47.5z" />
    <glyph glyph-name="uniFB04" unicode="&#xfb04;" horiz-adv-x="920" 
d="M109 481v81q0 77 45 121t113 44q43 0 56 -10v-84q-15 6 -41 6q-31 0 -53.5 -18.5t-22.5 -63.5v-76h219v81q0 77 45 121t113 44q43 0 56 -10v-84q-15 6 -41 6q-31 0 -53.5 -18.5t-22.5 -63.5v-76h225v243h93v-724h-93v396h-225v-396h-97v396h-219v-396h-97v396h-89v85h89z
" />
    <glyph glyph-name="uniFEFF" unicode="&#xfeff;" horiz-adv-x="0" 
 />
    <glyph glyph-name="zero.numr" horiz-adv-x="426" 
d="M138 465q0 -123 75 -123t75 123q0 124 -75 124t-75 -124zM60 465q0 84 35 132q41 58 118 58t118 -58q35 -48 35 -132q0 -83 -35 -131q-41 -58 -118 -58t-118 58q-35 48 -35 131z" />
    <glyph glyph-name="one.numr" horiz-adv-x="303" 
d="M213 283h-77v245h-86v52q36 1 61.5 20.5t30.5 47.5h71v-365z" />
    <glyph glyph-name="two.numr" horiz-adv-x="389" 
d="M141 507h-74q-3 15 -3 31q0 49 36 83t96 34q59 0 95 -33.5t36 -81.5q0 -66 -70 -110l-72 -45q-27 -18 -31 -37h175v-65h-268v13q0 93 85 145l60 37q43 26 43 63q0 21 -14.5 35.5t-39.5 14.5q-26 0 -41.5 -16t-15.5 -42q0 -13 3 -26z" />
    <glyph glyph-name="three.numr" horiz-adv-x="412" 
d="M178 448l-29 56l95 79h-171v65h271v-63l-100 -80q46 -4 77 -32.5t31 -76.5q0 -51 -40.5 -86t-104.5 -35q-61 0 -99 30t-48 73l67 27q5 -29 26.5 -46.5t52.5 -17.5q30 0 49 15.5t19 39.5q0 29 -20 42.5t-48 13.5q-14 0 -28 -4z" />
    <glyph glyph-name="four.numr" horiz-adv-x="446" 
d="M60 355v73l177 220h85v-228h64v-65h-64v-72h-77v72h-185zM245 420v139l-111 -139h111z" />
    <glyph glyph-name="five.numr" horiz-adv-x="379" 
d="M60 368l66 30q7 -26 26.5 -41.5t46.5 -15.5q30 0 48.5 16t18.5 45q0 30 -18.5 46t-48.5 16q-37 0 -64 -27l-64 24l49 187h204v-66h-154l-23 -86q25 28 73 28q56 0 90 -32.5t34 -86.5q0 -55 -39 -92t-106 -37q-52 0 -90.5 27.5t-48.5 64.5z" />
    <glyph glyph-name="six.numr" horiz-adv-x="418" 
d="M340 637l-18 -57q-29 13 -62 13q-49 0 -82 -27t-41 -78q13 19 38 31t53 12q59 0 94.5 -33.5t35.5 -92.5q0 -57 -42 -93t-105 -36q-61 0 -106 43t-45 124q0 96 54.5 154.5t139.5 58.5q54 0 86 -19zM143 404q0 -29 20.5 -46t48.5 -17q27 0 47.5 17t20.5 46t-20.5 46
t-47.5 17q-28 0 -48.5 -17t-20.5 -46z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="417" 
d="M357 648v-69q-19 -13 -37.5 -31.5t-45 -53.5t-46 -90t-25.5 -121h-82q5 55 21 106.5t35 85t37 59t30 36.5l13 12h-197v66h297z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="410" 
d="M205 500q26 0 41.5 13t15.5 35q0 20 -15.5 32.5t-41.5 12.5t-41.5 -12.5t-15.5 -32.5q0 -22 15.5 -35t41.5 -13zM205 340q32 0 49.5 13.5t17.5 36.5q0 22 -17.5 36t-49.5 14t-49.5 -14t-17.5 -36q0 -23 17.5 -36.5t49.5 -13.5zM205 276q-68 0 -106.5 30.5t-38.5 77.5
q0 27 18 50.5t50 35.5q-26 11 -41 33t-15 47q0 46 37.5 75.5t95.5 29.5t95.5 -29.5t37.5 -75.5q0 -25 -15 -47t-41 -33q32 -12 50 -35.5t18 -50.5q0 -47 -38.5 -77.5t-106.5 -30.5z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="416" 
d="M78 293l18 59q29 -13 62 -13q51 0 84 25.5t39 77.5q-13 -19 -38 -31t-53 -12q-57 0 -93.5 34t-36.5 92q0 56 42 92.5t105 36.5q61 0 105 -43t44 -124q0 -101 -53 -157t-139 -56q-54 0 -86 19zM273 525q0 29 -20 46.5t-49 17.5q-28 0 -48 -17t-20 -47t20 -47t48 -17
q29 0 49 17.5t20 46.5z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="426" 
d="M138 182q0 -123 75 -123t75 123q0 124 -75 124t-75 -124zM60 182q0 84 35 132q41 58 118 58t118 -58q35 -48 35 -132q0 -83 -35 -131q-41 -58 -118 -58t-118 58q-35 48 -35 131z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="303" 
d="M213 0h-77v245h-86v52q36 1 61.5 20.5t30.5 47.5h71v-365z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="389" 
d="M141 224h-74q-3 15 -3 31q0 49 36 83t96 34q59 0 95 -33.5t36 -81.5q0 -66 -70 -110l-72 -45q-27 -18 -31 -37h175v-65h-268v13q0 93 85 145l60 37q43 26 43 63q0 21 -14.5 35.5t-39.5 14.5q-26 0 -41.5 -16t-15.5 -42q0 -12 3 -26z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="412" 
d="M178 166l-29 56l95 79h-171v65h271v-63l-100 -80q46 -4 77 -32.5t31 -76.5q0 -51 -40.5 -86t-104.5 -35q-61 0 -99 30t-48 73l67 27q5 -29 26.5 -46.5t52.5 -17.5q30 0 49 15.5t19 39.5q0 29 -20 42.5t-48 13.5q-14 0 -28 -4z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="446" 
d="M60 72v73l177 220h85v-228h64v-65h-64v-72h-77v72h-185zM245 137v139l-111 -139h111z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="404" 
d="M60 85l66 30q7 -26 26.5 -41.5t46.5 -15.5q30 0 48.5 16t18.5 45q0 30 -18.5 46t-48.5 16q-37 0 -64 -27l-64 24l49 187h204v-66h-154l-23 -86q25 28 73 28q56 0 90 -32.5t34 -86.5q0 -55 -39 -92t-106 -37q-52 0 -90.5 27.5t-48.5 64.5z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="418" 
d="M340 354l-18 -57q-29 13 -62 13q-49 0 -82 -27t-41 -78q13 19 38 31t53 12q59 0 94.5 -33.5t35.5 -92.5q0 -57 -42 -93t-105 -36q-61 0 -106 43t-45 124q0 96 54.5 154.5t139.5 58.5q54 0 86 -19zM143 121q0 -29 20.5 -46t48.5 -17q27 0 47.5 17t20.5 46t-20.5 46
t-47.5 17q-28 0 -48.5 -17t-20.5 -46z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="417" 
d="M357 365v-69q-19 -13 -37.5 -31.5t-45 -53.5t-46 -90t-25.5 -121h-82q5 55 21 106.5t35 85t37 59t30 37.5l13 11h-197v66h297z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="410" 
d="M205 217q26 0 41.5 13t15.5 35q0 20 -15.5 32.5t-41.5 12.5t-41.5 -12.5t-15.5 -32.5q0 -22 15.5 -35t41.5 -13zM205 57q32 0 49.5 13.5t17.5 36.5q0 22 -17.5 36t-49.5 14t-49.5 -14t-17.5 -36q0 -23 17.5 -36.5t49.5 -13.5zM205 -7q-68 0 -106.5 30.5t-38.5 77.5
q0 27 18 50.5t50 35.5q-26 11 -41 33t-15 47q0 46 37.5 75.5t95.5 29.5t95.5 -29.5t37.5 -75.5q0 -25 -15 -47t-41 -33q32 -12 50 -35.5t18 -50.5q0 -47 -38.5 -77.5t-106.5 -30.5z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="416" 
d="M78 12l18 59q29 -13 62 -13q51 0 84 25.5t39 77.5q-13 -19 -38 -31t-53 -12q-57 0 -93.5 34t-36.5 92q0 56 42 92.5t105 36.5q61 0 105 -43t44 -124q0 -101 -53 -157t-139 -56q-54 0 -86 19zM273 244q0 29 -20 46.5t-49 17.5q-28 0 -48 -17t-20 -47t20 -47t48 -17
q29 0 49 17.5t20 46.5z" />
    <glyph glyph-name="space.frac" horiz-adv-x="50" 
 />
    <glyph glyph-name="a.ordn" horiz-adv-x="428" 
d="M66 401q0 43 29 70t74 33l88 12q26 4 26 25q0 24 -17 40t-51 16q-32 0 -51 -18t-22 -47l-69 16q5 47 43.5 79t97.5 32q73 0 108 -35.5t35 -90.5v-169q0 -39 4 -55h-71q-4 16 -4 44q-32 -54 -105 -54q-52 0 -83.5 30t-31.5 72zM193 359q90 0 90 93v15l-95 -15
q-47 -7 -47 -47q0 -19 14 -32.5t38 -13.5z" />
    <glyph glyph-name="b.ordn" horiz-adv-x="494" 
d="M161 309h-73v507h74v-212q12 23 41.5 38.5t67.5 15.5q74 0 115 -50t41 -128t-43 -129t-116 -51q-72 0 -107 56v-47zM351 480q0 52 -25.5 81.5t-68.5 29.5q-42 0 -69 -30t-27 -81q0 -52 27 -82.5t69 -30.5t68 30.5t26 82.5z" />
    <glyph glyph-name="c.ordn" horiz-adv-x="462" 
d="M239 590q-40 0 -68.5 -30t-28.5 -81q0 -52 28.5 -81.5t69.5 -29.5q38 0 59.5 19.5t29.5 45.5l67 -26q-15 -44 -55 -76t-101 -32q-74 0 -123.5 51.5t-49.5 128.5q0 78 49 129t123 51q63 0 102.5 -32.5t51.5 -78.5l-67 -26q-19 68 -87 68z" />
    <glyph glyph-name="d.ordn" horiz-adv-x="495" 
d="M143 480q0 -51 25.5 -82.5t68.5 -31.5q42 0 67.5 32t25.5 82q0 51 -25.5 80.5t-67.5 29.5t-68 -30.5t-26 -79.5zM403 816v-445q0 -41 3 -62h-72q-4 32 -4 46q-12 -23 -39 -39t-62 -16q-73 0 -117.5 51t-44.5 129q0 74 45 125.5t116 51.5q80 0 101 -52v211h74z" />
    <glyph glyph-name="e.ordn" horiz-adv-x="461" 
d="M144 515h174q-1 34 -24 57t-63 23q-38 0 -61.5 -24t-25.5 -56zM327 427l64 -21q-14 -47 -54.5 -77t-98.5 -30q-71 0 -121.5 49.5t-50.5 131.5q0 78 49 128.5t115 50.5q77 0 121 -48t44 -129q0 -6 -0.5 -11.5t-0.5 -8.5l-1 -4h-251q1 -41 28.5 -67.5t67.5 -26.5
q68 0 89 63z" />
    <glyph glyph-name="f.ordn" horiz-adv-x="350" 
d="M283 582h-80v-274h-76v274h-61v67h61v50q0 56 32 88t84 32q32 0 41 -7v-65q-12 4 -29 4q-21 0 -36.5 -12.5t-15.5 -42.5v-47h80v-67z" />
    <glyph glyph-name="g.ordn" horiz-adv-x="465" 
d="M67 291l71 17q4 -34 28 -55.5t60 -21.5q99 0 99 105v40q-27 -51 -101 -51q-68 0 -112 46.5t-44 118.5q0 70 43.5 117.5t112.5 47.5q76 0 102 -51v44h73v-310q0 -74 -41.5 -123t-130.5 -49q-65 0 -109 35.5t-51 89.5zM236 388q41 0 66 28t25 74q0 45 -25.5 72.5
t-65.5 27.5q-41 0 -66.5 -27.5t-25.5 -72.5q0 -47 25 -74.5t67 -27.5z" />
    <glyph glyph-name="h.ordn" horiz-adv-x="473" 
d="M163 508v-199h-75v507h75v-203q34 45 99 45q60 0 91.5 -37.5t31.5 -96.5v-215h-75v202q0 79 -74 79q-33 0 -52.5 -23t-20.5 -59z" />
    <glyph glyph-name="i.ordn" horiz-adv-x="278" 
d="M176 309h-74v339h74v-339zM89 771q0 21 14.5 36t35.5 15t35.5 -15t14.5 -36t-14.5 -35.5t-35.5 -14.5t-35.5 14.5t-14.5 35.5z" />
    <glyph glyph-name="j.ordn" horiz-adv-x="297" 
d="M137 278v370h75v-375q0 -48 -26.5 -77.5t-71.5 -29.5q-29 0 -44 5v62q12 -2 23 -2q44 0 44 47zM124 772q0 21 14.5 36t35.5 15t35.5 -15t14.5 -36t-14.5 -35.5t-35.5 -14.5t-35.5 14.5t-14.5 35.5z" />
    <glyph glyph-name="k.ordn" horiz-adv-x="430" 
d="M400 648l-141 -143l144 -196h-93l-104 143l-44 -45v-98h-74v507h74v-310l137 142h101z" />
    <glyph glyph-name="l.ordn" horiz-adv-x="251" 
d="M163 309h-75v507h75v-507z" />
    <glyph glyph-name="m.ordn" horiz-adv-x="681" 
d="M162 309h-74v339h71v-44q15 26 43 40t59 14q76 0 101 -61q37 61 112 61q49 0 84 -33t35 -96v-220h-73v209q0 33 -17 53t-51 20q-33 0 -53.5 -22.5t-20.5 -56.5v-203h-74v209q0 73 -68 73q-33 0 -53.5 -22.5t-20.5 -57.5v-202z" />
    <glyph glyph-name="n.ordn" horiz-adv-x="473" 
d="M163 505v-196h-75v339h73v-46q33 56 102 56q59 0 90.5 -37.5t31.5 -96.5v-215h-75v202q0 79 -74 79q-34 0 -53.5 -24t-19.5 -61z" />
    <glyph glyph-name="o.ordn" horiz-adv-x="484" 
d="M242 366q41 0 70 30t29 83t-28.5 82.5t-70.5 29.5t-71 -29.5t-29 -82.5t29 -83t71 -30zM242 659q76 0 125.5 -51t49.5 -129t-49.5 -129t-125.5 -51t-125.5 51t-49.5 129t49.5 129t125.5 51z" />
    <glyph glyph-name="p.ordn" horiz-adv-x="494" 
d="M162 176h-74v472h73v-49q13 25 42 41t68 16q73 0 114.5 -50t41.5 -127t-43.5 -128t-115.5 -51q-73 0 -106 50v-174zM352 479q0 49 -26 80t-69 31q-42 0 -69 -31t-27 -80t27 -80.5t69 -31.5t68.5 31t26.5 81z" />
    <glyph glyph-name="q.ordn" horiz-adv-x="477" 
d="M405 176h-74v176q-29 -52 -102 -52q-72 0 -117 52t-45 128q0 74 44 125.5t116 51.5q41 0 68 -17t38 -40v48h72v-472zM143 480q0 -50 26 -82t68 -32t68.5 32t26.5 82q0 49 -26.5 79.5t-68.5 30.5t-68 -30.5t-26 -79.5z" />
    <glyph glyph-name="r.ordn" horiz-adv-x="288" 
d="M266 651v-78q-15 3 -29 3q-90 0 -90 -102v-165h-75v339h73v-56q27 61 98 61q9 0 23 -2z" />
    <glyph glyph-name="s.ordn" horiz-adv-x="401" 
d="M67 400l67 20q2 -26 21 -43.5t51 -17.5q25 0 39.5 12.5t14.5 29.5q0 31 -41 40l-54 12q-41 9 -64.5 35.5t-23.5 61.5q0 45 36 77t87 32q62 0 93 -30t37 -64l-64 -20q-3 20 -18.5 36.5t-47.5 16.5q-23 0 -37.5 -12.5t-14.5 -28.5q0 -30 36 -38l53 -11q47 -10 71.5 -37
t24.5 -65q0 -42 -33.5 -74.5t-93.5 -32.5q-65 0 -99.5 32t-39.5 69z" />
    <glyph glyph-name="t.ordn" horiz-adv-x="324" 
d="M193 753v-105h72v-67h-72v-165q0 -44 45 -44q14 0 27 3v-63q-17 -7 -48 -7q-45 0 -72 26.5t-27 72.5v177h-63v67h18q26 0 39 14.5t13 38.5v52h68z" />
    <glyph glyph-name="u.ordn" horiz-adv-x="440" 
d="M293 349q-13 -24 -40 -37t-58 -13q-58 0 -93 38.5t-35 96.5v214h75v-201q0 -36 18 -58.5t54 -22.5t55.5 22t19.5 58v202h75v-277q0 -42 4 -62h-72q-3 12 -3 40z" />
    <glyph glyph-name="v.ordn" horiz-adv-x="404" 
d="M374 648l-134 -339h-75l-138 339h83l93 -250l91 250h80z" />
    <glyph glyph-name="w.ordn" horiz-adv-x="603" 
d="M263 648h77l84 -242l70 242h77l-109 -339h-76l-86 249l-85 -249h-78l-110 339h80l72 -242z" />
    <glyph glyph-name="x.ordn" horiz-adv-x="413" 
d="M37 309l124 172l-122 167h90l78 -113l77 113h87l-120 -166q77 -109 124 -173h-89l-82 118q-3 -5 -40 -59.5t-41 -58.5h-86z" />
    <glyph glyph-name="y.ordn" horiz-adv-x="418" 
d="M171 170h-81l83 178l-146 300h86l100 -222l96 222h79z" />
    <glyph glyph-name="z.ordn" horiz-adv-x="409" 
d="M342 309h-275v66l178 207h-174v66h268v-64l-179 -209h182v-66z" />
    <glyph glyph-name="space.tf" 
 />
    <glyph glyph-name="numbersign.tf" 
d="M491 648l-36 -167h97v-81h-114l-32 -150h97v-81h-115l-36 -169h-88l36 169h-116l-36 -169h-88l36 169h-88v81h106l32 150h-89v81h106l36 167h88l-36 -167h116l36 167h88zM234 400l-32 -150h116l32 150h-116z" />
    <glyph glyph-name="dollar.tf" 
d="M327 -104h-76v100q-94 10 -148.5 65.5t-62.5 126.5l92 30q4 -51 34 -89t85 -47v202l-24 5q-76 16 -118.5 61.5t-42.5 115.5t52 122t133 62v102h76v-103q81 -10 123.5 -55t57.5 -103l-87 -32q-5 32 -29 62t-65 42v-199l14 -3q80 -16 126.5 -65t46.5 -115
q0 -69 -49.5 -121.5t-137.5 -63.5v-100zM419 176q0 32 -23 57t-69 36v-187q46 8 69 33.5t23 60.5zM162 470q0 -71 89 -89v185q-42 -8 -65.5 -34t-23.5 -62z" />
    <glyph glyph-name="percent.tf" 
d="M526 540v-92l-482 -327v90zM94 520q0 -27 17.5 -44.5t42.5 -17.5q26 0 43.5 17.5t17.5 44.5q0 26 -17.5 43t-43.5 17q-25 0 -42.5 -17t-17.5 -43zM18 520q0 55 40.5 94t95.5 39t96 -39t41 -94q0 -56 -41 -95.5t-96 -39.5t-95.5 39.5t-40.5 95.5zM345 127
q0 -27 17.5 -44.5t42.5 -17.5q26 0 43.5 17.5t17.5 44.5q0 26 -17.5 43t-43.5 17q-25 0 -42.5 -17t-17.5 -43zM269 127q0 55 40.5 94t95.5 39t96 -39t41 -94q0 -56 -41 -95.5t-96 -39.5t-95.5 39.5t-40.5 95.5z" />
    <glyph glyph-name="plus.tf" 
d="M52 327h187v195h82v-195h188v-79h-188v-197h-82v197h-187v79z" />
    <glyph glyph-name="comma.tf" 
d="M191 74q0 31 24.5 54t59.5 23q40 0 67 -29t27 -81q0 -103 -53 -159.5t-118 -64.5v60q42 9 68.5 45t27.5 79q-12 -6 -25 -6q-33 0 -55.5 21.5t-22.5 57.5z" />
    <glyph glyph-name="period.tf" 
d="M200 74q0 33 23 56.5t57 23.5t57 -23.5t23 -56.5q0 -34 -23 -57t-57 -23t-57 23t-23 57z" />
    <glyph glyph-name="zero.tf" 
d="M131 324q0 -37 2 -65.5t11 -65.5t24 -61.5t43.5 -42t68.5 -17.5t68.5 17.5t43.5 42t24 61.5t11 65.5t2 65.5t-2 65.5t-11 65.5t-24 61.5t-43.5 42t-68.5 17.5t-68.5 -17.5t-43.5 -42t-24 -61.5t-11 -65.5t-2 -65.5zM34 324q0 158 52 238q67 101 194 101t194 -101
q52 -80 52 -238t-52 -238q-67 -101 -194 -101t-194 101q-52 80 -52 238z" />
    <glyph glyph-name="one.tf" 
d="M105 88h163v369h-157v69q66 1 111 34.5t56 87.5h86v-560h156v-88h-415v88z" />
    <glyph glyph-name="two.tf" 
d="M159 415l-98 12q-1 9 -1 26q0 87 61 148.5t163 61.5q101 0 160 -57.5t59 -141.5q0 -123 -122 -200l-128 -82q-69 -44 -79 -91h333v-91h-454q2 82 37.5 144t116.5 114l108 70q89 57 89 135q0 48 -32 81t-90 33q-61 0 -93 -37t-32 -96q0 -9 2 -29z" />
    <glyph glyph-name="three.tf" 
d="M218 301l-48 81l191 176h-310v90h440v-88l-185 -171q79 0 139.5 -52t60.5 -145q0 -86 -63 -147t-172 -61q-107 0 -171.5 59.5t-69.5 144.5l96 22q3 -63 43.5 -101.5t100.5 -38.5q64 0 100.5 34.5t36.5 85.5q0 59 -39 90t-93 31q-29 0 -57 -10z" />
    <glyph glyph-name="four.tf" 
d="M37 146v113l278 389h135v-411h101v-91h-101v-146h-95v146h-318zM355 237v317l-228 -317h228z" />
    <glyph glyph-name="five.tf" 
d="M40 176l94 28q4 -60 44.5 -97t101.5 -37q59 0 99 36t40 95q0 64 -40 99t-100 35q-73 0 -118 -47q-14 5 -46 18t-50 20l84 322h342v-90h-277l-48 -186q44 48 132 48q97 0 158.5 -58t61.5 -157q0 -94 -66 -157.5t-172 -63.5q-100 0 -166.5 56t-73.5 136z" />
    <glyph glyph-name="six.tf" 
d="M512 627l-30 -80q-48 28 -116 28q-99 0 -160.5 -64.5t-67.5 -166.5q22 36 66.5 59.5t104.5 23.5q98 0 160.5 -59.5t62.5 -161.5q0 -99 -71 -160t-170 -61q-104 0 -178.5 75.5t-74.5 224.5q0 176 94 277t240 101q89 0 140 -36zM152 206q0 -61 42 -98.5t98 -37.5
q59 0 100 37t41 99q0 63 -41 100t-100 37t-99.5 -37t-40.5 -100z" />
    <glyph glyph-name="seven.tf" 
d="M526 648v-94q-252 -202 -284 -554h-101q6 77 24.5 149.5t43 126.5t53.5 101.5t56.5 80t50 55.5t35.5 34l14 11h-385v90h493z" />
    <glyph glyph-name="eight.tf" 
d="M280 372q52 1 86 30t34 76q0 44 -33 74t-87 30t-87 -30t-33 -74q0 -47 34 -76t86 -30zM280 70q63 0 99.5 31t36.5 78q0 48 -36.5 79.5t-99.5 31.5t-99.5 -31.5t-36.5 -79.5q0 -47 36.5 -78t99.5 -31zM280 -15q-109 0 -172 52.5t-63 133.5q0 57 34.5 99.5t88.5 60.5
q-46 17 -76 58.5t-30 93.5q0 79 62 129.5t156 50.5t156 -50.5t62 -129.5q0 -51 -30 -93t-76 -59q54 -17 88.5 -60t34.5 -100q0 -81 -63 -133.5t-172 -52.5z" />
    <glyph glyph-name="nine.tf" 
d="M44 23l32 81q51 -31 118 -31q111 0 166.5 63t58.5 166q-19 -35 -62.5 -58.5t-105.5 -23.5q-90 0 -157 58.5t-67 160.5q0 98 70.5 161t168.5 63q104 0 178 -76t74 -222q0 -182 -81.5 -281t-242.5 -99q-38 0 -82 11t-68 27zM406 441q0 60 -42 98t-98 38q-57 0 -98.5 -37.5
t-41.5 -98.5q0 -63 41 -100t99 -37q57 0 98.5 37.5t41.5 99.5z" />
    <glyph glyph-name="colon.tf" 
d="M203 412q0 32 22 54.5t54 22.5t54.5 -22.5t22.5 -54.5t-22.5 -54t-54.5 -22t-54 22t-22 54zM203 65q0 32 22 54.5t54 22.5t54.5 -22.5t22.5 -54.5t-22.5 -54t-54.5 -22t-54 22t-22 54z" />
    <glyph glyph-name="semicolon.tf" 
d="M204 412q0 32 22 54.5t54 22.5t54.5 -22.5t22.5 -54.5t-22.5 -54t-54.5 -22t-54 22t-22 54zM202 70q0 29 22.5 50t55.5 21q36 0 60.5 -26.5t24.5 -74.5q0 -95 -48.5 -147t-107.5 -60v56q37 8 61.5 41t26.5 73q-10 -5 -24 -5q-30 0 -50.5 19.5t-20.5 52.5z" />
    <glyph glyph-name="less.tf" 
d="M74 244v85l412 206v-95l-310 -153l310 -153v-95z" />
    <glyph glyph-name="equal.tf" 
d="M512 346h-463v77h463v-77zM512 151h-463v79h463v-79z" />
    <glyph glyph-name="greater.tf" 
d="M486 329v-85l-412 -205v95l312 153l-312 153v95z" />
    <glyph glyph-name="cent.tf" 
d="M328 0h-76v94q-84 15 -136.5 80t-52.5 154q0 90 52.5 154t136.5 79v88h76v-86q71 -8 114.5 -47.5t58.5 -92.5l-81 -33q-9 32 -31.5 57.5t-60.5 33.5v-307q75 15 97 89l80 -33q-17 -51 -62.5 -91t-114.5 -47v-92zM156 328q0 -58 26.5 -97t69.5 -53v299
q-43 -14 -69.5 -52.5t-26.5 -96.5z" />
    <glyph glyph-name="sterling.tf" 
d="M60 359h70q-30 66 -30 118q0 80 55.5 133t139.5 53q55 0 95.5 -16t62.5 -43.5t32.5 -56.5t12.5 -62l-98 -17q-1 54 -28.5 81.5t-74.5 27.5q-42 0 -70.5 -26.5t-28.5 -73.5q0 -45 33 -118h184v-86h-158q2 -18 2 -27q0 -52 -25 -94t-70 -65h171q48 0 72 30.5t24 75.5
l94 -14q0 -79 -44.5 -129t-121.5 -50h-305v95q50 21 80 59.5t30 80.5q0 22 -4 38h-100v86z" />
    <glyph glyph-name="currency.tf" 
d="M71 239q0 66 37 121l-75 77l60 59l75 -78q54 35 116 35q60 0 111 -31l72 74l60 -59l-70 -73q41 -57 41 -125q0 -70 -40 -123l69 -71l-59 -59l-70 72q-52 -33 -114 -33q-64 0 -119 37l-73 -76l-59 59l73 76q-35 54 -35 118zM154 239q0 -58 37.5 -95t92.5 -37t91.5 37
t36.5 95q0 57 -36.5 94t-91.5 37t-92.5 -37t-37.5 -94z" />
    <glyph glyph-name="yen.tf" 
d="M502 155h-168v-155h-97v155h-169v72h169v80h-169v71h117l-171 270h114l157 -261l149 261h112l-163 -270h119v-71h-168v-80h168v-72z" />
    <glyph glyph-name="section.tf" horiz-adv-x="558" 
d="M474 244q0 -42 -26 -76t-57 -47q41 -26 64 -64.5t23 -79.5q0 -80 -59.5 -130.5t-143.5 -50.5q-92 0 -142 50t-52 133l88 18q0 -56 27.5 -89.5t80.5 -33.5q48 0 76.5 24.5t28.5 68.5q0 56 -58 86l-103 54q-66 33 -101.5 70.5t-35.5 98.5q0 43 26.5 77t57.5 45
q-42 26 -65 64.5t-23 80.5q0 80 59.5 130.5t142.5 50.5q92 0 143.5 -52t51.5 -131l-89 -18q0 57 -27.5 89.5t-80.5 32.5q-47 0 -76 -25.5t-29 -67.5q0 -56 59 -85l102 -54q67 -35 102.5 -72t35.5 -97zM320 316l-99 54q-18 -8 -32.5 -30.5t-14.5 -48.5q0 -55 63 -87l99 -54
q18 8 32.5 30.5t14.5 48.5q0 55 -63 87z" />
    <glyph glyph-name="logicalnot.tf" 
d="M491 149h-84v195h-379v82h463v-277z" />
    <glyph glyph-name="plusminus.tf" 
d="M62 386h177v136h82v-136h178v-77h-178v-134h-82v134h-177v77zM62 130h437v-79h-437v79z" />
    <glyph glyph-name="multiply.tf" 
d="M449 55l-170 175l-168 -174l-56 56l169 176l-168 174l57 57l167 -174l167 174l57 -56l-169 -175l170 -176z" />
    <glyph glyph-name="divide.tf" 
d="M214 465q0 26 19.5 45t45.5 19t45 -19t19 -45t-19 -45.5t-45 -19.5t-45.5 19.5t-19.5 45.5zM51 246v83h457v-83h-457zM214 110q0 26 19.5 45t45.5 19t45 -19t19 -45t-19 -45.5t-45 -19.5t-45.5 19.5t-19.5 45.5z" />
    <glyph glyph-name="florin.tf" 
d="M179 20l75 378h-110v83h127l23 113q14 72 60 108.5t105 36.5q42 0 74 -10v-83q-25 7 -53 7q-34 0 -59.5 -18t-36.5 -74l-16 -80h129v-83h-146l-82 -411q-14 -72 -60 -108.5t-105 -36.5q-45 0 -77 10v83q25 -7 56 -7q78 0 96 92z" />
    <glyph glyph-name="uni2007.tf" 
 />
    <glyph glyph-name="uni2008.tf" 
 />
    <glyph glyph-name="perthousand.tf" 
d="M528 632v-92l-482 -376v90zM82 536q0 -24 16 -39.5t39 -15.5t38 15.5t15 39.5t-15 39.5t-38 15.5t-39 -15.5t-16 -39.5zM17 536q0 49 35.5 83.5t84.5 34.5t84.5 -34.5t35.5 -83.5t-35.5 -83.5t-84.5 -34.5t-84.5 34.5t-35.5 83.5zM132 107q0 49 35.5 83.5t84.5 34.5
q50 0 85 -38q35 38 86 38q49 0 84.5 -34.5t35.5 -83.5t-35.5 -83.5t-84.5 -34.5q-53 0 -86 38q-33 -38 -85 -38q-49 0 -84.5 34.5t-35.5 83.5zM252 162q-23 0 -39 -15.5t-16 -39.5t16 -39.5t39 -15.5t38 15.5t15 39.5t-15 39.5t-38 15.5zM423 162q-23 0 -39 -15.5t-16 -39.5
t16 -39.5t39 -15.5t38 15.5t15 39.5t-15 39.5t-38 15.5z" />
    <glyph glyph-name="Euro.tf" 
d="M115 325q0 30 2 40h-74v78h89q34 105 120.5 162.5t189.5 57.5q57 0 108 -17l-23 -82q-42 12 -85 12q-70 0 -125.5 -34t-82.5 -99h257l-24 -78h-252q-3 -17 -3 -40q0 -29 3 -43h235l-24 -80h-190q28 -63 82.5 -95.5t123.5 -32.5q46 0 85 12l23 -82q-51 -17 -108 -17
q-101 0 -187.5 56t-120.5 159h-91v80h74q-2 12 -2 43z" />
    <glyph glyph-name="minus.tf" 
d="M491 248h-423v79h423v-79z" />
    <glyph glyph-name="approxequal.tf" 
d="M478 429l51 -45q-58 -83 -146 -83q-47 0 -100 30l-47 26q-32 17 -62 17q-52 0 -91 -45l-53 47q59 85 143 85q46 0 103 -31l46 -25q35 -20 70 -20q49 0 86 44zM478 235l51 -45q-58 -83 -146 -83q-47 0 -100 30l-47 26q-32 17 -62 17q-52 0 -91 -45l-53 47q59 85 143 85
q46 0 103 -31l46 -25q35 -20 70 -20q49 0 86 44z" />
    <glyph glyph-name="notequal.tf" 
d="M511 151h-272l-74 -100h-96l74 100h-95v79h153l85 116h-238v77h295l73 99h96l-73 -99h72v-77h-129l-85 -116h214v-79z" />
    <glyph glyph-name="lessequal.tf" 
d="M74 130h412v-79h-412v79zM74 304v85l412 146v-84l-307 -104l307 -104v-84z" />
    <glyph glyph-name="greaterequal.tf" 
d="M74 130h412v-79h-412v79zM486 389v-85l-412 -145v84l307 104l-307 104v84z" />
    <glyph glyph-name="parenleft.case" horiz-adv-x="297" 
d="M55 352q0 255 187 442l55 -49q-80 -88 -120.5 -181.5t-40.5 -211.5t40.5 -211.5t120.5 -181.5l-55 -49q-187 187 -187 442z" />
    <glyph glyph-name="parenright.case" horiz-adv-x="297" 
d="M242 352q0 -255 -187 -442l-55 49q80 88 120.5 181.5t40.5 211.5t-40.5 211.5t-120.5 181.5l55 49q187 -187 187 -442z" />
    <glyph glyph-name="hyphen.case" horiz-adv-x="361" 
d="M316 296h-271v82h271v-82z" />
    <glyph glyph-name="bracketleft.case" horiz-adv-x="288" 
d="M283 -90h-193v890h193v-76h-111v-739h111v-75z" />
    <glyph glyph-name="bracketright.case" horiz-adv-x="288" 
d="M198 -90h-193v75h111v739h-111v76h193v-890z" />
    <glyph glyph-name="braceleft.case" horiz-adv-x="353" 
d="M45 311v86q48 0 79 24.5t31 76.5v116q0 186 180 186h13v-75h-13q-54 0 -76 -24.5t-22 -80.5v-143q0 -50 -27.5 -82t-76.5 -41q49 -9 76.5 -41t27.5 -82v-141q0 -56 22 -80.5t76 -24.5h13v-75h-13q-180 0 -180 186v114q0 52 -31 76.5t-79 24.5z" />
    <glyph glyph-name="braceright.case" horiz-adv-x="353" 
d="M308 397v-86q-48 0 -79 -24.5t-31 -76.5v-114q0 -186 -180 -186h-13v75h13q54 0 76 24.5t22 80.5v141q0 50 27.5 82t76.5 41q-49 9 -76.5 41t-27.5 82v143q0 56 -22 80.5t-76 24.5h-13v75h13q180 0 180 -186v-116q0 -52 31 -76.5t79 -24.5z" />
    <glyph glyph-name="exclamdown.case" horiz-adv-x="311" 
d="M189 505l25 -505h-118l26 505h67zM90 647q0 27 19 46.5t46 19.5t46.5 -19.5t19.5 -46.5t-19.5 -46t-46.5 -19t-46 19t-19 46z" />
    <glyph glyph-name="guillemotleft.case" horiz-adv-x="471" 
d="M256 149h-93l-133 191l133 191h93l-131 -191zM451 149h-93l-133 191l133 191h93l-131 -191z" />
    <glyph glyph-name="uni00AD.case" horiz-adv-x="361" 
d="M316 313h-271v82h271v-82z" />
    <glyph glyph-name="guillemotright.case" horiz-adv-x="471" 
d="M308 149h-93l131 191l-131 191h93l133 -191zM113 149h-93l131 191l-131 191h93l133 -191z" />
    <glyph glyph-name="questiondown.case" horiz-adv-x="510" 
d="M226 508h88q1 -12 1 -35q0 -100 -83 -156l-44 -30q-53 -36 -53 -98q0 -48 31 -81t88 -33q61 0 93 38t32 90q0 30 -7 49l97 -12q6 -21 6 -48q0 -49 -22 -94t-74 -78t-125 -33q-100 0 -159.5 60t-59.5 141q0 105 102 174l46 31q44 30 44 96q0 12 -1 19zM335 650
q0 -27 -19 -46.5t-46 -19.5t-46.5 19.5t-19.5 46.5t19.5 46t46.5 19t46 -19t19 -46z" />
    <glyph glyph-name="endash.case" horiz-adv-x="622" 
d="M567 296h-512v82h512v-82z" />
    <glyph glyph-name="emdash.case" horiz-adv-x="1083" 
d="M1018 296h-953v82h953v-82z" />
    <glyph glyph-name="guilsinglleft.case" horiz-adv-x="276" 
d="M256 149h-93l-133 191l133 191h93l-131 -191z" />
    <glyph glyph-name="guilsinglright.case" horiz-adv-x="276" 
d="M113 149h-93l131 191l-131 191h93l133 -191z" />
    <glyph glyph-name="G.ss01" horiz-adv-x="767" 
d="M401 -15q-91 0 -171 42t-132.5 128t-52.5 200q0 86 30.5 157t81 116.5t113.5 70.5t131 25q118 0 203 -61t112 -164l-91 -31q-22 81 -80 123.5t-144 42.5q-107 0 -181.5 -72.5t-74.5 -206.5t75 -207.5t181 -73.5q95 0 155.5 50.5t70.5 126.5h-286v88h385q1 -8 1 -25
q0 -142 -85.5 -235.5t-240.5 -93.5z" />
    <glyph glyph-name="a.ss02" horiz-adv-x="583" 
d="M136 244q0 -76 37.5 -125t103.5 -49q63 0 101 49t38 125q0 75 -37 121t-101 46t-103 -46t-39 -121zM417 65v8q-19 -37 -57.5 -61.5t-91.5 -24.5q-103 0 -165.5 73.5t-62.5 183.5q0 104 64 177t164 73q59 0 96 -23.5t51 -57.5v68h93v-392q0 -44 5 -89h-91q-5 28 -5 65z
" />
    <glyph glyph-name="agrave.ss02" horiz-adv-x="583" 
d="M136 244q0 -76 37.5 -125t103.5 -49q63 0 101 49t38 125q0 75 -37 121t-101 46t-103 -46t-39 -121zM417 65v8q-19 -37 -57.5 -61.5t-91.5 -24.5q-103 0 -165.5 73.5t-62.5 183.5q0 104 64 177t164 73q59 0 96 -23.5t51 -57.5v68h93v-392q0 -44 5 -89h-91q-5 28 -5 65z
M238 543l-126 146h118l86 -146h-78z" />
    <glyph glyph-name="aacute.ss02" horiz-adv-x="583" 
d="M136 244q0 -76 37.5 -125t103.5 -49q63 0 101 49t38 125q0 75 -37 121t-101 46t-103 -46t-39 -121zM417 65v8q-19 -37 -57.5 -61.5t-91.5 -24.5q-103 0 -165.5 73.5t-62.5 183.5q0 104 64 177t164 73q59 0 96 -23.5t51 -57.5v68h93v-392q0 -44 5 -89h-91q-5 28 -5 65z
M418 689l-126 -146h-79l87 146h118z" />
    <glyph glyph-name="acircumflex.ss02" horiz-adv-x="583" 
d="M136 244q0 -76 37.5 -125t103.5 -49q63 0 101 49t38 125q0 75 -37 121t-101 46t-103 -46t-39 -121zM417 65v8q-19 -37 -57.5 -61.5t-91.5 -24.5q-103 0 -165.5 73.5t-62.5 183.5q0 104 64 177t164 73q59 0 96 -23.5t51 -57.5v68h93v-392q0 -44 5 -89h-91q-5 28 -5 65z
M214 545h-87l104 139h107l104 -139h-88l-70 81z" />
    <glyph glyph-name="atilde.ss02" horiz-adv-x="583" 
d="M136 244q0 -76 37.5 -125t103.5 -49q63 0 101 49t38 125q0 75 -37 121t-101 46t-103 -46t-39 -121zM417 65v8q-19 -37 -57.5 -61.5t-91.5 -24.5q-103 0 -165.5 73.5t-62.5 183.5q0 104 64 177t164 73q59 0 96 -23.5t51 -57.5v68h93v-392q0 -44 5 -89h-91q-5 28 -5 65z
M436 677v-15q0 -53 -27 -81t-70 -28q-31 0 -66 22l-14 9q-23 14 -37 14q-29 0 -29 -43h-72v15q0 53 27.5 81t70.5 28q32 0 67 -22l14 -9q23 -14 36 -14q29 0 29 43h71z" />
    <glyph glyph-name="adieresis.ss02" horiz-adv-x="583" 
d="M136 244q0 -76 37.5 -125t103.5 -49q63 0 101 49t38 125q0 75 -37 121t-101 46t-103 -46t-39 -121zM417 65v8q-19 -37 -57.5 -61.5t-91.5 -24.5q-103 0 -165.5 73.5t-62.5 183.5q0 104 64 177t164 73q59 0 96 -23.5t51 -57.5v68h93v-392q0 -44 5 -89h-91q-5 28 -5 65z
M130 621q0 24 16.5 41t40.5 17q25 0 42 -17t17 -41q0 -25 -17 -41.5t-42 -16.5q-24 0 -40.5 16.5t-16.5 41.5zM317 621q0 24 16.5 41t41.5 17q24 0 41 -17t17 -41q0 -25 -17 -41.5t-41 -16.5q-25 0 -41.5 16.5t-16.5 41.5z" />
    <glyph glyph-name="aring.ss02" horiz-adv-x="583" 
d="M136 244q0 -76 37.5 -125t103.5 -49q63 0 101 49t38 125q0 75 -37 121t-101 46t-103 -46t-39 -121zM417 65v8q-19 -37 -57.5 -61.5t-91.5 -24.5q-103 0 -165.5 73.5t-62.5 183.5q0 104 64 177t164 73q59 0 96 -23.5t51 -57.5v68h93v-392q0 -44 5 -89h-91q-5 28 -5 65z
M386 641q0 -45 -33 -77t-79 -32q-45 0 -78 32t-33 77q0 46 33 78t78 32q46 0 79 -32t33 -78zM322 641q0 20 -14 34.5t-35 14.5q-20 0 -33.5 -14.5t-13.5 -34.5t13.5 -34t33.5 -14q21 0 35 14t14 34z" />
    <glyph glyph-name="r.ss03" horiz-adv-x="330" 
d="M174 481v-481h-94v481h94zM175 415q0 30 20.5 50.5t50.5 20.5q29 0 49.5 -20.5t20.5 -50.5q0 -29 -20.5 -49.5t-49.5 -20.5q-30 0 -50.5 20.5t-20.5 49.5z" />
    <glyph glyph-name="ampersand.ss04" horiz-adv-x="677" 
d="M387 243v89h275v-89h-86v-10q0 -106 -68 -177t-187 -71q-115 0 -185.5 58.5t-70.5 150.5q0 73 42.5 123.5t103.5 68.5q-50 18 -80.5 57.5t-30.5 97.5q0 77 59.5 130t148.5 53q106 0 160 -55.5t60 -137.5l-96 -17q-4 58 -35 90t-88 32q-45 0 -77 -28.5t-32 -71.5
q0 -48 34 -75.5t74 -27.5h13v-90q-60 0 -108 -34t-48 -107q0 -52 41.5 -89.5t115.5 -37.5q72 0 113.5 42.5t41.5 115.5v10h-90z" />
    <glyph glyph-name="one.ss05" horiz-adv-x="465" 
d="M45 88h163v369h-157v69q66 1 111 34.5t56 87.5h86v-560h156v-88h-415v88z" />
    <glyph glyph-name="registered.ss06" horiz-adv-x="429" 
d="M25 526q0 78 55.5 133.5t133.5 55.5t134 -55.5t56 -133.5t-56 -133.5t-134 -55.5t-133.5 55.5t-55.5 133.5zM74 526q0 -60 40.5 -101.5t99.5 -41.5t100 41.5t41 101.5t-41 101.5t-100 41.5t-99.5 -41.5t-40.5 -101.5zM197 537h20q26 0 26 24q0 22 -26 22h-20v-46z
M242 430l-30 65h-15v-65h-52v196h83q28 0 48 -19.5t20 -45.5q0 -37 -36 -56l36 -75h-54z" />
    <glyph glyph-name="caron.alt" horiz-adv-x="203" 
d="M168 709l-64 -179h-69l28 179h105z" />
    <glyph glyph-name="commaaccent" horiz-adv-x="194" 
d="M35 659q0 23 17.5 37.5t41.5 14.5q26 0 45.5 -18t19.5 -55q0 -54 -34.5 -83t-79.5 -29v40q25 0 44 15t21 33q-6 -6 -21 -6q-24 0 -39 13t-15 38z" />
    <glyph glyph-name="commaturn" horiz-adv-x="172" 
d="M149 586q0 -23 -17 -37.5t-42 -14.5t-45 18.5t-20 54.5q0 51 35 81.5t79 30.5v-40q-25 0 -44 -15t-21 -33q6 6 21 6q24 0 39 -13.5t15 -37.5z" />
    <glyph glyph-name="dieresis.narrow" horiz-adv-x="294" 
d="M23 621q0 23 15.5 38.5t37.5 15.5t38 -15.5t16 -38.5q0 -22 -15.5 -37.5t-38.5 -15.5q-22 0 -37.5 15t-15.5 38zM164 621q0 23 15.5 38.5t37.5 15.5q23 0 38.5 -15.5t15.5 -38.5q0 -22 -15.5 -37.5t-38.5 -15.5t-38 15t-15 38z" />
    <glyph glyph-name="dieresis.uc.narrow" horiz-adv-x="310" 
d="M25 819q0 23 15.5 38t37.5 15q23 0 38.5 -15.5t15.5 -37.5q0 -23 -16 -38.5t-38 -15.5t-37.5 15.5t-15.5 38.5zM178 819q0 22 15.5 37.5t38.5 15.5q22 0 37.5 -15t15.5 -38t-15.5 -38.5t-37.5 -15.5t-38 15.5t-16 38.5z" />
    <glyph glyph-name="grave.uc" horiz-adv-x="273" 
d="M156 759l-138 119h129l102 -119h-93z" />
    <glyph glyph-name="dieresis.uc" horiz-adv-x="407" 
d="M29 819q0 25 17 42t42 17t42 -17t17 -42t-17 -42t-42 -17t-42 17t-17 42zM260 819q0 25 17 42t42 17t42 -17t17 -42t-17 -42t-42 -17t-42 17t-17 42z" />
    <glyph glyph-name="macron.uc" horiz-adv-x="343" 
d="M318 779h-293v83h293v-83z" />
    <glyph glyph-name="acute.uc" horiz-adv-x="273" 
d="M255 878l-137 -119h-94l101 119h130z" />
    <glyph glyph-name="circumflex.uc" horiz-adv-x="403" 
d="M120 759h-91l112 119h120l117 -119h-92l-85 64z" />
    <glyph glyph-name="caron.uc" horiz-adv-x="403" 
d="M29 878h91l81 -64l85 64h92l-117 -119h-120z" />
    <glyph glyph-name="breve.uc" horiz-adv-x="371" 
d="M186 754q-71 0 -112.5 38.5t-44.5 90.5h81q2 -21 20.5 -38.5t55.5 -17.5t55 17.5t20 38.5h81q-3 -53 -44 -91t-112 -38z" />
    <glyph glyph-name="dotaccent.uc" horiz-adv-x="177" 
d="M25 818q0 26 18.5 45t44.5 19t45 -19t19 -45t-19 -44.5t-45 -18.5t-44.5 18.5t-18.5 44.5z" />
    <glyph glyph-name="ring.uc" horiz-adv-x="273" 
d="M248 848q0 -45 -33 -77t-79 -32q-45 0 -78 32t-33 77q0 46 33 78t78 32q46 0 79 -32t33 -78zM183 848q0 20 -14 33.5t-34 13.5q-19 0 -32.5 -13.5t-13.5 -33.5t13.5 -33t32.5 -13q20 0 34 13.5t14 32.5z" />
    <glyph glyph-name="tilde.uc" horiz-adv-x="373" 
d="M349 881v-16q0 -54 -26.5 -82.5t-69.5 -28.5q-38 0 -71 23l-15 10q-23 14 -37 14q-35 0 -35 -44h-68v16q0 54 27 82.5t70 28.5q35 0 73 -23l15 -9q24 -14 38 -14q32 0 32 35v8h67z" />
    <glyph glyph-name="hungarumlaut.uc" horiz-adv-x="367" 
d="M191 878l-96 -119h-70l61 119h105zM342 878l-106 -119h-70l71 119h105z" />
    <glyph glyph-name="caron.alt.uc" horiz-adv-x="203" 
d="M176 709l-68 -190h-73l30 190h111z" />
    <glyph glyph-name="undercommaaccent" horiz-adv-x="174" 
d="M25 -88q0 22 17.5 37t41.5 15q26 0 45.5 -18t19.5 -55q0 -54 -34.5 -83t-79.5 -29v40q25 0 44 15t21 33q-9 -6 -21 -6q-24 0 -39 13t-15 38z" />
    <hkern u1="&#x20;" g2="ampersand.ss04" k="40" />
    <hkern u1="&#x20;" u2="&#x26;" k="40" />
    <hkern u1="&#x21;" g2="one.ss05" k="20" />
    <hkern u1="&#x22;" u2="&#xef;" k="-40" />
    <hkern u1="&#x22;" u2="&#xee;" k="-40" />
    <hkern u1="&#x22;" u2="&#xec;" k="-40" />
    <hkern u1="&#x23;" g2="one.ss05" k="50" />
    <hkern u1="&#x23;" u2="&#x39;" k="20" />
    <hkern u1="&#x23;" u2="&#x38;" k="20" />
    <hkern u1="&#x23;" u2="&#x37;" k="10" />
    <hkern u1="&#x23;" u2="&#x35;" k="30" />
    <hkern u1="&#x23;" u2="&#x34;" k="40" />
    <hkern u1="&#x23;" u2="&#x33;" k="30" />
    <hkern u1="&#x23;" u2="&#x32;" k="10" />
    <hkern u1="&#x23;" u2="&#x31;" k="10" />
    <hkern u1="&#x24;" g2="one.ss05" k="20" />
    <hkern u1="&#x24;" u2="&#x39;" k="10" />
    <hkern u1="&#x24;" u2="&#x38;" k="10" />
    <hkern u1="&#x24;" u2="&#x37;" k="10" />
    <hkern u1="&#x24;" u2="&#x35;" k="10" />
    <hkern u1="&#x24;" u2="&#x34;" k="10" />
    <hkern u1="&#x24;" u2="&#x33;" k="10" />
    <hkern u1="&#x25;" u2="&#x31;" k="10" />
    <hkern u1="&#x26;" u2="V" k="75" />
    <hkern u1="&#x26;" u2="&#x35;" k="10" />
    <hkern u1="&#x26;" u2="&#x34;" k="10" />
    <hkern u1="&#x26;" u2="&#x33;" k="10" />
    <hkern u1="&#x26;" u2="&#x31;" k="10" />
    <hkern u1="&#x26;" u2="&#x20;" k="40" />
    <hkern u1="&#x27;" u2="&#xef;" k="-40" />
    <hkern u1="&#x27;" u2="&#xee;" k="-40" />
    <hkern u1="&#x27;" u2="&#xec;" k="-40" />
    <hkern u1="&#x28;" u2="&#x2212;" k="40" />
    <hkern u1="&#x28;" u2="&#x192;" k="-50" />
    <hkern u1="&#x28;" u2="&#xec;" k="-20" />
    <hkern u1="&#x28;" u2="v" k="20" />
    <hkern u1="&#x28;" u2="X" k="-20" />
    <hkern u1="&#x28;" u2="V" k="-20" />
    <hkern u1="&#x28;" u2="&#x37;" k="-40" />
    <hkern u1="&#x28;" u2="&#x35;" k="25" />
    <hkern u1="&#x28;" u2="&#x34;" k="20" />
    <hkern u1="&#x28;" u2="&#x32;" k="-5" />
    <hkern u1="&#x28;" u2="&#x31;" k="5" />
    <hkern u1="&#x28;" u2="&#x2b;" k="50" />
    <hkern u1="&#x29;" u2="&#x141;" k="-20" />
    <hkern u1="&#x2a;" g2="one.ss05" k="40" />
    <hkern u1="&#x2a;" u2="&#xee;" k="-20" />
    <hkern u1="&#x2a;" u2="v" k="-10" />
    <hkern u1="&#x2a;" u2="V" k="-20" />
    <hkern u1="&#x2a;" u2="&#x37;" k="-40" />
    <hkern u1="&#x2a;" u2="&#x34;" k="70" />
    <hkern u1="&#x2b;" g2="one.ss05" k="70" />
    <hkern u1="&#x2b;" u2="X" k="10" />
    <hkern u1="&#x2b;" u2="V" k="40" />
    <hkern u1="&#x2b;" u2="&#x39;" k="10" />
    <hkern u1="&#x2b;" u2="&#x37;" k="10" />
    <hkern u1="&#x2b;" u2="&#x33;" k="20" />
    <hkern u1="&#x2b;" u2="&#x32;" k="10" />
    <hkern u1="&#x2b;" u2="&#x31;" k="30" />
    <hkern u1="&#x2b;" u2="&#x29;" k="50" />
    <hkern u1="&#x2c;" u2="g" k="10" />
    <hkern u1="&#x2e;" u2="g" k="10" />
    <hkern u1="&#x2f;" g2="one.ss05" k="40" />
    <hkern u1="&#x2f;" u2="&#x17e;" k="50" />
    <hkern u1="&#x2f;" u2="&#x161;" k="60" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="90" />
    <hkern u1="&#x2f;" u2="&#xef;" k="-30" />
    <hkern u1="&#x2f;" u2="&#xee;" k="-30" />
    <hkern u1="&#x2f;" u2="&#xed;" k="10" />
    <hkern u1="&#x2f;" u2="&#xec;" k="-70" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="105" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="105" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="110" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="95" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="100" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="105" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="95" />
    <hkern u1="&#x2f;" u2="x" k="20" />
    <hkern u1="&#x2f;" u2="v" k="10" />
    <hkern u1="&#x2f;" u2="X" k="-10" />
    <hkern u1="&#x2f;" u2="V" k="-20" />
    <hkern u1="&#x2f;" u2="&#x39;" k="20" />
    <hkern u1="&#x2f;" u2="&#x38;" k="30" />
    <hkern u1="&#x2f;" u2="&#x37;" k="-30" />
    <hkern u1="&#x2f;" u2="&#x35;" k="20" />
    <hkern u1="&#x2f;" u2="&#x34;" k="80" />
    <hkern u1="&#x2f;" u2="&#x32;" k="20" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="140" />
    <hkern u1="&#x31;" g2="registered.ss06" k="10" />
    <hkern u1="&#x31;" g2="one.ss05" k="20" />
    <hkern u1="&#x31;" g2="parenright.case" k="-10" />
    <hkern u1="&#x31;" u2="&#x2265;" k="-10" />
    <hkern u1="&#x31;" u2="&#x2260;" k="-20" />
    <hkern u1="&#x31;" u2="&#x2248;" k="-20" />
    <hkern u1="&#x31;" u2="&#x221e;" k="-20" />
    <hkern u1="&#x31;" u2="&#xf7;" k="10" />
    <hkern u1="&#x31;" u2="&#xb0;" k="10" />
    <hkern u1="&#x31;" u2="&#x7c;" k="-10" />
    <hkern u1="&#x31;" u2="&#x3e;" k="-30" />
    <hkern u1="&#x31;" u2="&#x3c;" k="10" />
    <hkern u1="&#x31;" u2="&#x37;" k="-5" />
    <hkern u1="&#x31;" u2="&#x32;" k="-5" />
    <hkern u1="&#x31;" u2="&#x25;" k="10" />
    <hkern u1="&#x31;" u2="&#x23;" k="20" />
    <hkern u1="&#x32;" g2="registered.ss06" k="-10" />
    <hkern u1="&#x32;" u2="&#x2265;" k="-10" />
    <hkern u1="&#x32;" u2="&#x2260;" k="-10" />
    <hkern u1="&#x32;" u2="&#x2248;" k="-20" />
    <hkern u1="&#x32;" u2="&#x2215;" k="-20" />
    <hkern u1="&#x32;" u2="&#xd7;" k="-10" />
    <hkern u1="&#x32;" u2="&#xb1;" k="-10" />
    <hkern u1="&#x32;" u2="&#xb0;" k="-10" />
    <hkern u1="&#x32;" u2="_" k="-10" />
    <hkern u1="&#x32;" u2="\" k="10" />
    <hkern u1="&#x32;" u2="&#x3e;" k="-30" />
    <hkern u1="&#x32;" u2="&#x34;" k="15" />
    <hkern u1="&#x32;" u2="&#x31;" k="-10" />
    <hkern u1="&#x33;" g2="one.ss05" k="10" />
    <hkern u1="&#x33;" g2="florin.tf" k="50" />
    <hkern u1="&#x33;" u2="&#x2265;" k="-20" />
    <hkern u1="&#x33;" u2="&#x2248;" k="-20" />
    <hkern u1="&#x33;" u2="&#x2212;" k="-10" />
    <hkern u1="&#x33;" u2="&#x2030;" k="10" />
    <hkern u1="&#x33;" u2="&#xd7;" k="-10" />
    <hkern u1="&#x33;" u2="&#xb1;" k="-10" />
    <hkern u1="&#x33;" u2="&#xb0;" k="-10" />
    <hkern u1="&#x33;" u2="&#xa6;" k="-20" />
    <hkern u1="&#x33;" u2="&#x7e;" k="-10" />
    <hkern u1="&#x33;" u2="&#x7c;" k="-20" />
    <hkern u1="&#x33;" u2="_" k="10" />
    <hkern u1="&#x33;" u2="&#x3e;" k="-30" />
    <hkern u1="&#x33;" u2="&#x37;" k="-10" />
    <hkern u1="&#x33;" u2="&#x31;" k="5" />
    <hkern u1="&#x33;" u2="&#x2f;" k="10" />
    <hkern u1="&#x33;" u2="&#x2a;" k="-10" />
    <hkern u1="&#x33;" u2="&#x25;" k="10" />
    <hkern u1="&#x33;" u2="&#x23;" k="20" />
    <hkern u1="&#x34;" g2="registered.ss06" k="20" />
    <hkern u1="&#x34;" g2="one.ss05" k="5" />
    <hkern u1="&#x34;" g2="florin.tf" k="40" />
    <hkern u1="&#x34;" g2="t.ordn" k="50" />
    <hkern u1="&#x34;" g2="e.ordn" k="30" />
    <hkern u1="&#x34;" u2="&#x2265;" k="-30" />
    <hkern u1="&#x34;" u2="&#x2264;" k="-30" />
    <hkern u1="&#x34;" u2="&#x2260;" k="-10" />
    <hkern u1="&#x34;" u2="&#x2248;" k="-30" />
    <hkern u1="&#x34;" u2="&#x221e;" k="-10" />
    <hkern u1="&#x34;" u2="&#x2215;" k="-10" />
    <hkern u1="&#x34;" u2="&#x2122;" k="70" />
    <hkern u1="&#x34;" u2="&#x2030;" k="30" />
    <hkern u1="&#x34;" u2="&#xd7;" k="-10" />
    <hkern u1="&#x34;" u2="&#xba;" k="40" />
    <hkern u1="&#x34;" u2="&#xb1;" k="-10" />
    <hkern u1="&#x34;" u2="&#xb0;" k="10" />
    <hkern u1="&#x34;" u2="&#xaa;" k="40" />
    <hkern u1="&#x34;" u2="&#xa6;" k="-10" />
    <hkern u1="&#x34;" u2="&#x7e;" k="-20" />
    <hkern u1="&#x34;" u2="&#x3e;" k="-30" />
    <hkern u1="&#x34;" u2="&#x3d;" k="-20" />
    <hkern u1="&#x34;" u2="&#x37;" k="5" />
    <hkern u1="&#x34;" u2="&#x2a;" k="20" />
    <hkern u1="&#x34;" u2="&#x25;" k="10" />
    <hkern u1="&#x35;" g2="registered.ss06" k="-10" />
    <hkern u1="&#x35;" g2="one.ss05" k="10" />
    <hkern u1="&#x35;" g2="questiondown.case" k="-10" />
    <hkern u1="&#x35;" g2="florin.tf" k="70" />
    <hkern u1="&#x35;" u2="&#x2265;" k="-20" />
    <hkern u1="&#x35;" u2="&#x2264;" k="-10" />
    <hkern u1="&#x35;" u2="&#x2260;" k="-10" />
    <hkern u1="&#x35;" u2="&#x2248;" k="-20" />
    <hkern u1="&#x35;" u2="&#x2215;" k="10" />
    <hkern u1="&#x35;" u2="&#x2212;" k="-10" />
    <hkern u1="&#x35;" u2="&#x2030;" k="10" />
    <hkern u1="&#x35;" u2="&#xb0;" k="-10" />
    <hkern u1="&#x35;" u2="&#x7e;" k="-10" />
    <hkern u1="&#x35;" u2="_" k="10" />
    <hkern u1="&#x35;" u2="&#x40;" k="5" />
    <hkern u1="&#x35;" u2="&#x3e;" k="-20" />
    <hkern u1="&#x35;" u2="&#x3d;" k="-10" />
    <hkern u1="&#x35;" u2="&#x3c;" k="-10" />
    <hkern u1="&#x35;" u2="&#x37;" k="-5" />
    <hkern u1="&#x35;" u2="&#x2f;" k="20" />
    <hkern u1="&#x35;" u2="&#x25;" k="10" />
    <hkern u1="&#x36;" g2="one.ss05" k="10" />
    <hkern u1="&#x36;" g2="questiondown.case" k="-10" />
    <hkern u1="&#x36;" g2="florin.tf" k="70" />
    <hkern u1="&#x36;" u2="&#x2248;" k="-10" />
    <hkern u1="&#x36;" u2="&#x2212;" k="-10" />
    <hkern u1="&#x36;" u2="&#x2030;" k="20" />
    <hkern u1="&#x36;" u2="&#x7e;" k="-10" />
    <hkern u1="&#x36;" u2="_" k="20" />
    <hkern u1="&#x36;" u2="\" k="10" />
    <hkern u1="&#x36;" u2="&#x40;" k="10" />
    <hkern u1="&#x36;" u2="&#x3e;" k="-10" />
    <hkern u1="&#x36;" u2="&#x3d;" k="-10" />
    <hkern u1="&#x36;" u2="&#x37;" k="-5" />
    <hkern u1="&#x36;" u2="&#x2b;" k="-10" />
    <hkern u1="&#x36;" u2="&#x23;" k="20" />
    <hkern u1="&#x37;" g2="registered.ss06" k="-30" />
    <hkern u1="&#x37;" g2="one.ss05" k="15" />
    <hkern u1="&#x37;" g2="questiondown.case" k="40" />
    <hkern u1="&#x37;" g2="florin.tf" k="70" />
    <hkern u1="&#x37;" g2="e.ordn" k="-20" />
    <hkern u1="&#x37;" u2="&#x2265;" k="-20" />
    <hkern u1="&#x37;" u2="&#x2260;" k="10" />
    <hkern u1="&#x37;" u2="&#x221e;" k="10" />
    <hkern u1="&#x37;" u2="&#x2215;" k="60" />
    <hkern u1="&#x37;" u2="&#x2212;" k="30" />
    <hkern u1="&#x37;" u2="&#x2122;" k="-20" />
    <hkern u1="&#x37;" u2="&#x2030;" k="-10" />
    <hkern u1="&#x37;" u2="&#x2021;" k="-50" />
    <hkern u1="&#x37;" u2="&#x2020;" k="-50" />
    <hkern u1="&#x37;" u2="&#xf7;" k="20" />
    <hkern u1="&#x37;" u2="&#xbf;" k="60" />
    <hkern u1="&#x37;" u2="&#xb7;" k="40" />
    <hkern u1="&#x37;" u2="&#xb1;" k="10" />
    <hkern u1="&#x37;" u2="&#xb0;" k="-30" />
    <hkern u1="&#x37;" u2="&#xa6;" k="-20" />
    <hkern u1="&#x37;" u2="&#x7e;" k="40" />
    <hkern u1="&#x37;" u2="&#x7c;" k="-20" />
    <hkern u1="&#x37;" u2="_" k="90" />
    <hkern u1="&#x37;" u2="\" k="-30" />
    <hkern u1="&#x37;" u2="&#x40;" k="5" />
    <hkern u1="&#x37;" u2="&#x3f;" k="-20" />
    <hkern u1="&#x37;" u2="&#x3e;" k="-30" />
    <hkern u1="&#x37;" u2="&#x3d;" k="-5" />
    <hkern u1="&#x37;" u2="&#x3c;" k="30" />
    <hkern u1="&#x37;" u2="&#x39;" k="-10" />
    <hkern u1="&#x37;" u2="&#x37;" k="-30" />
    <hkern u1="&#x37;" u2="&#x35;" k="15" />
    <hkern u1="&#x37;" u2="&#x34;" k="55" />
    <hkern u1="&#x37;" u2="&#x31;" k="-5" />
    <hkern u1="&#x37;" u2="&#x2f;" k="60" />
    <hkern u1="&#x37;" u2="&#x2b;" k="50" />
    <hkern u1="&#x37;" u2="&#x2a;" k="-20" />
    <hkern u1="&#x37;" u2="&#x23;" k="40" />
    <hkern u1="&#x38;" g2="one.ss05" k="15" />
    <hkern u1="&#x38;" g2="florin.tf" k="60" />
    <hkern u1="&#x38;" g2="t.ordn" k="30" />
    <hkern u1="&#x38;" u2="&#x2265;" k="-10" />
    <hkern u1="&#x38;" u2="&#x2260;" k="-10" />
    <hkern u1="&#x38;" u2="&#x2248;" k="-20" />
    <hkern u1="&#x38;" u2="&#xa3;" k="20" />
    <hkern u1="&#x38;" u2="_" k="10" />
    <hkern u1="&#x38;" u2="\" k="40" />
    <hkern u1="&#x38;" u2="&#x3e;" k="-10" />
    <hkern u1="&#x38;" u2="&#x26;" k="10" />
    <hkern u1="&#x38;" u2="&#x25;" k="5" />
    <hkern u1="&#x38;" u2="&#x23;" k="10" />
    <hkern u1="&#x3c;" g2="one.ss05" k="-10" />
    <hkern u1="&#x3c;" u2="&#x39;" k="-30" />
    <hkern u1="&#x3c;" u2="&#x38;" k="-30" />
    <hkern u1="&#x3c;" u2="&#x37;" k="-70" />
    <hkern u1="&#x3c;" u2="&#x35;" k="-20" />
    <hkern u1="&#x3c;" u2="&#x33;" k="-10" />
    <hkern u1="&#x3c;" u2="&#x32;" k="-30" />
    <hkern u1="&#x3c;" u2="&#x31;" k="-30" />
    <hkern u1="&#x3d;" g2="one.ss05" k="40" />
    <hkern u1="&#x3d;" u2="X" k="10" />
    <hkern u1="&#x3d;" u2="V" k="10" />
    <hkern u1="&#x3d;" u2="&#x37;" k="10" />
    <hkern u1="&#x3d;" u2="&#x35;" k="-10" />
    <hkern u1="&#x3e;" g2="one.ss05" k="50" />
    <hkern u1="&#x3e;" u2="X" k="20" />
    <hkern u1="&#x3e;" u2="V" k="20" />
    <hkern u1="&#x3e;" u2="&#x33;" k="20" />
    <hkern u1="&#x3e;" u2="&#x32;" k="10" />
    <hkern u1="&#x3e;" u2="&#x31;" k="10" />
    <hkern u1="&#x3f;" g2="one.ss05" k="10" />
    <hkern u1="&#x3f;" u2="v" k="-20" />
    <hkern u1="&#x3f;" u2="&#x37;" k="-30" />
    <hkern u1="&#x3f;" u2="&#x34;" k="20" />
    <hkern u1="&#x3f;" u2="&#x31;" k="-10" />
    <hkern u1="&#x40;" g2="one.ss05" k="30" />
    <hkern u1="&#x40;" u2="&#x141;" k="-10" />
    <hkern u1="&#x40;" u2="x" k="-5" />
    <hkern u1="&#x40;" u2="v" k="-15" />
    <hkern u1="&#x40;" u2="X" k="20" />
    <hkern u1="&#x40;" u2="V" k="20" />
    <hkern u1="&#x40;" u2="&#x37;" k="-10" />
    <hkern u1="&#x40;" u2="&#x34;" k="10" />
    <hkern u1="&#x40;" u2="&#x33;" k="10" />
    <hkern u1="B" g2="questiondown.case" k="-10" />
    <hkern u1="B" g2="braceleft.case" k="20" />
    <hkern u1="B" u2="&#x2122;" k="10" />
    <hkern u1="B" u2="&#x2086;" k="10" />
    <hkern u1="B" u2="&#x2085;" k="10" />
    <hkern u1="B" u2="&#x2084;" k="40" />
    <hkern u1="B" u2="&#x2077;" k="30" />
    <hkern u1="B" u2="&#x2022;" k="10" />
    <hkern u1="B" u2="&#xdf;" k="15" />
    <hkern u1="B" u2="&#xbf;" k="25" />
    <hkern u1="B" u2="&#xba;" k="10" />
    <hkern u1="B" u2="&#xaa;" k="15" />
    <hkern u1="B" u2="v" k="5" />
    <hkern u1="B" u2="_" k="10" />
    <hkern u1="B" u2="\" k="25" />
    <hkern u1="B" u2="&#x3e;" k="-10" />
    <hkern u1="B" u2="&#x2f;" k="15" />
    <hkern u1="B" u2="&#x2b;" k="5" />
    <hkern u1="B" u2="&#x26;" k="5" />
    <hkern u1="C" u2="&#xee;" k="-15" />
    <hkern u1="E" u2="&#xec;" k="-10" />
    <hkern u1="F" g2="registered.ss06" k="-20" />
    <hkern u1="F" g2="braceleft.case" k="30" />
    <hkern u1="F" g2="parenright.case" k="-20" />
    <hkern u1="F" g2="parenleft.case" k="10" />
    <hkern u1="F" u2="&#x2122;" k="-20" />
    <hkern u1="F" u2="&#x2089;" k="50" />
    <hkern u1="F" u2="&#x2088;" k="60" />
    <hkern u1="F" u2="&#x2087;" k="50" />
    <hkern u1="F" u2="&#x2086;" k="70" />
    <hkern u1="F" u2="&#x2085;" k="60" />
    <hkern u1="F" u2="&#x2084;" k="100" />
    <hkern u1="F" u2="&#x2083;" k="50" />
    <hkern u1="F" u2="&#x2082;" k="40" />
    <hkern u1="F" u2="&#x2081;" k="40" />
    <hkern u1="F" u2="&#x2080;" k="50" />
    <hkern u1="F" u2="&#x2022;" k="10" />
    <hkern u1="F" u2="&#x2021;" k="-20" />
    <hkern u1="F" u2="&#x2020;" k="-20" />
    <hkern u1="F" u2="&#x192;" k="70" />
    <hkern u1="F" u2="&#xef;" k="-20" />
    <hkern u1="F" u2="&#xee;" k="-25" />
    <hkern u1="F" u2="&#xec;" k="-35" />
    <hkern u1="F" u2="&#xdf;" k="20" />
    <hkern u1="F" u2="&#xbf;" k="70" />
    <hkern u1="F" u2="&#xb6;" k="-20" />
    <hkern u1="F" u2="&#xaa;" k="15" />
    <hkern u1="F" u2="&#xa7;" k="-20" />
    <hkern u1="F" u2="&#xa6;" k="-20" />
    <hkern u1="F" u2="&#x7e;" k="20" />
    <hkern u1="F" u2="&#x7c;" k="-20" />
    <hkern u1="F" u2="&#x7b;" k="20" />
    <hkern u1="F" u2="x" k="15" />
    <hkern u1="F" u2="v" k="20" />
    <hkern u1="F" u2="_" k="100" />
    <hkern u1="F" u2="^" k="-10" />
    <hkern u1="F" u2="\" k="-30" />
    <hkern u1="F" u2="&#x40;" k="10" />
    <hkern u1="F" u2="&#x3f;" k="-10" />
    <hkern u1="F" u2="&#x3e;" k="-10" />
    <hkern u1="F" u2="&#x2f;" k="55" />
    <hkern u1="F" u2="&#x2b;" k="20" />
    <hkern u1="F" u2="&#x2a;" k="-15" />
    <hkern u1="F" u2="&#x29;" k="-30" />
    <hkern u1="F" u2="&#x26;" k="10" />
    <hkern u1="G" u2="&#xee;" k="-5" />
    <hkern u1="J" u2="&#xee;" k="-5" />
    <hkern u1="K" u2="&#xec;" k="-30" />
    <hkern u1="P" g2="registered.ss06" k="-30" />
    <hkern u1="P" g2="questiondown.case" k="10" />
    <hkern u1="P" g2="braceleft.case" k="10" />
    <hkern u1="P" g2="y.ordn" k="-20" />
    <hkern u1="P" g2="x.ordn" k="-10" />
    <hkern u1="P" g2="w.ordn" k="-15" />
    <hkern u1="P" g2="v.ordn" k="-15" />
    <hkern u1="P" g2="j.ordn" k="20" />
    <hkern u1="P" u2="&#x2089;" k="80" />
    <hkern u1="P" u2="&#x2088;" k="80" />
    <hkern u1="P" u2="&#x2087;" k="60" />
    <hkern u1="P" u2="&#x2086;" k="80" />
    <hkern u1="P" u2="&#x2085;" k="80" />
    <hkern u1="P" u2="&#x2084;" k="110" />
    <hkern u1="P" u2="&#x2083;" k="70" />
    <hkern u1="P" u2="&#x2082;" k="80" />
    <hkern u1="P" u2="&#x2081;" k="80" />
    <hkern u1="P" u2="&#x2080;" k="80" />
    <hkern u1="P" u2="&#x2022;" k="20" />
    <hkern u1="P" u2="&#x2021;" k="-20" />
    <hkern u1="P" u2="&#x2020;" k="-20" />
    <hkern u1="P" u2="&#x192;" k="60" />
    <hkern u1="P" u2="&#x161;" k="15" />
    <hkern u1="P" u2="&#x142;" k="5" />
    <hkern u1="P" u2="&#xfc;" k="20" />
    <hkern u1="P" u2="&#xfb;" k="20" />
    <hkern u1="P" u2="&#xfa;" k="20" />
    <hkern u1="P" u2="&#xf9;" k="20" />
    <hkern u1="P" u2="&#xf7;" k="10" />
    <hkern u1="P" u2="&#xef;" k="-20" />
    <hkern u1="P" u2="&#xee;" k="-40" />
    <hkern u1="P" u2="&#xec;" k="-15" />
    <hkern u1="P" u2="&#xdf;" k="15" />
    <hkern u1="P" u2="&#xd7;" k="-10" />
    <hkern u1="P" u2="&#xbf;" k="80" />
    <hkern u1="P" u2="&#xb7;" k="20" />
    <hkern u1="P" u2="&#xb6;" k="-20" />
    <hkern u1="P" u2="&#xb3;" k="10" />
    <hkern u1="P" u2="&#xa7;" k="-10" />
    <hkern u1="P" u2="&#x7e;" k="20" />
    <hkern u1="P" u2="&#x7b;" k="20" />
    <hkern u1="P" u2="_" k="90" />
    <hkern u1="P" u2="X" k="5" />
    <hkern u1="P" u2="&#x40;" k="5" />
    <hkern u1="P" u2="&#x3f;" k="-20" />
    <hkern u1="P" u2="&#x3e;" k="-20" />
    <hkern u1="P" u2="&#x2f;" k="70" />
    <hkern u1="P" u2="&#x2b;" k="30" />
    <hkern u1="P" u2="&#x2a;" k="-10" />
    <hkern u1="P" u2="&#x26;" k="10" />
    <hkern u1="Q" g2="ampersand.ss04" k="5" />
    <hkern u1="Q" g2="parenright.case" k="10" />
    <hkern u1="Q" u2="&#x2122;" k="20" />
    <hkern u1="Q" u2="&#x2084;" k="10" />
    <hkern u1="Q" u2="&#x2077;" k="10" />
    <hkern u1="Q" u2="&#x192;" k="20" />
    <hkern u1="Q" u2="&#x141;" k="-5" />
    <hkern u1="Q" u2="&#xdf;" k="10" />
    <hkern u1="Q" u2="\" k="35" />
    <hkern u1="Q" u2="X" k="15" />
    <hkern u1="Q" u2="V" k="25" />
    <hkern u1="Q" u2="&#x3e;" k="-20" />
    <hkern u1="Q" u2="&#x3c;" k="-10" />
    <hkern u1="Q" u2="&#x29;" k="10" />
    <hkern u1="R" u2="&#xee;" k="-10" />
    <hkern u1="T" g2="atilde.ss02" k="60" />
    <hkern u1="T" g2="agrave.ss02" k="70" />
    <hkern u1="T" u2="&#x17e;" k="30" />
    <hkern u1="T" u2="&#x161;" k="10" />
    <hkern u1="T" u2="&#x131;" k="80" />
    <hkern u1="T" u2="&#xff;" k="50" />
    <hkern u1="T" u2="&#xfc;" k="70" />
    <hkern u1="T" u2="&#xfb;" k="65" />
    <hkern u1="T" u2="&#xef;" k="-30" />
    <hkern u1="T" u2="&#xee;" k="-15" />
    <hkern u1="T" u2="&#xed;" k="50" />
    <hkern u1="T" u2="&#xec;" k="-40" />
    <hkern u1="T" u2="&#xe5;" k="65" />
    <hkern u1="T" u2="&#xe4;" k="45" />
    <hkern u1="T" u2="&#xe3;" k="45" />
    <hkern u1="T" u2="&#xe2;" k="55" />
    <hkern u1="T" u2="&#xe0;" k="40" />
    <hkern u1="U" u2="&#xee;" k="-5" />
    <hkern u1="V" g2="questiondown.case" k="20" />
    <hkern u1="V" g2="exclamdown.case" k="-10" />
    <hkern u1="V" g2="braceleft.case" k="20" />
    <hkern u1="V" g2="parenright.case" k="-20" />
    <hkern u1="V" g2="y.ordn" k="-40" />
    <hkern u1="V" g2="x.ordn" k="-20" />
    <hkern u1="V" g2="w.ordn" k="-20" />
    <hkern u1="V" g2="v.ordn" k="-20" />
    <hkern u1="V" u2="&#x2122;" k="-30" />
    <hkern u1="V" u2="&#x2089;" k="100" />
    <hkern u1="V" u2="&#x2088;" k="100" />
    <hkern u1="V" u2="&#x2087;" k="70" />
    <hkern u1="V" u2="&#x2086;" k="90" />
    <hkern u1="V" u2="&#x2085;" k="90" />
    <hkern u1="V" u2="&#x2084;" k="90" />
    <hkern u1="V" u2="&#x2083;" k="60" />
    <hkern u1="V" u2="&#x2082;" k="70" />
    <hkern u1="V" u2="&#x2081;" k="60" />
    <hkern u1="V" u2="&#x2080;" k="100" />
    <hkern u1="V" u2="&#x2079;" k="-20" />
    <hkern u1="V" u2="&#x2078;" k="-20" />
    <hkern u1="V" u2="&#x2077;" k="-20" />
    <hkern u1="V" u2="&#x2074;" k="20" />
    <hkern u1="V" u2="&#x2022;" k="40" />
    <hkern u1="V" u2="&#x2021;" k="-10" />
    <hkern u1="V" u2="&#x2020;" k="-20" />
    <hkern u1="V" u2="&#x192;" k="60" />
    <hkern u1="V" u2="&#xf7;" k="40" />
    <hkern u1="V" u2="&#xef;" k="-10" />
    <hkern u1="V" u2="&#xee;" k="-10" />
    <hkern u1="V" u2="&#xec;" k="-35" />
    <hkern u1="V" u2="&#xdf;" k="15" />
    <hkern u1="V" u2="&#xd7;" k="10" />
    <hkern u1="V" u2="&#xbf;" k="60" />
    <hkern u1="V" u2="&#xb9;" k="-10" />
    <hkern u1="V" u2="&#xb7;" k="30" />
    <hkern u1="V" u2="&#xb3;" k="-10" />
    <hkern u1="V" u2="&#xb2;" k="-10" />
    <hkern u1="V" u2="&#xa6;" k="-20" />
    <hkern u1="V" u2="&#xa1;" k="20" />
    <hkern u1="V" u2="&#x7e;" k="40" />
    <hkern u1="V" u2="&#x7c;" k="-20" />
    <hkern u1="V" u2="&#x7b;" k="30" />
    <hkern u1="V" u2="x" k="5" />
    <hkern u1="V" u2="v" k="5" />
    <hkern u1="V" u2="_" k="50" />
    <hkern u1="V" u2="\" k="-20" />
    <hkern u1="V" u2="X" k="-10" />
    <hkern u1="V" u2="V" k="-10" />
    <hkern u1="V" u2="&#x40;" k="20" />
    <hkern u1="V" u2="&#x3f;" k="-10" />
    <hkern u1="V" u2="&#x3d;" k="10" />
    <hkern u1="V" u2="&#x3c;" k="20" />
    <hkern u1="V" u2="&#x2f;" k="60" />
    <hkern u1="V" u2="&#x2b;" k="40" />
    <hkern u1="V" u2="&#x2a;" k="-20" />
    <hkern u1="V" u2="&#x29;" k="-20" />
    <hkern u1="V" u2="&#x26;" k="45" />
    <hkern u1="W" u2="&#xef;" k="-20" />
    <hkern u1="W" u2="&#xee;" k="-10" />
    <hkern u1="W" u2="&#xec;" k="-25" />
    <hkern u1="X" g2="braceleft.case" k="30" />
    <hkern u1="X" g2="parenright.case" k="-20" />
    <hkern u1="X" u2="&#x2089;" k="10" />
    <hkern u1="X" u2="&#x2088;" k="-10" />
    <hkern u1="X" u2="&#x2086;" k="-10" />
    <hkern u1="X" u2="&#x2085;" k="-10" />
    <hkern u1="X" u2="&#x2084;" k="-10" />
    <hkern u1="X" u2="&#x2082;" k="-10" />
    <hkern u1="X" u2="&#x2077;" k="-10" />
    <hkern u1="X" u2="&#x2074;" k="10" />
    <hkern u1="X" u2="&#x2022;" k="30" />
    <hkern u1="X" u2="&#xf7;" k="10" />
    <hkern u1="X" u2="&#xef;" k="-10" />
    <hkern u1="X" u2="&#xee;" k="-10" />
    <hkern u1="X" u2="&#xec;" k="-40" />
    <hkern u1="X" u2="&#xd7;" k="10" />
    <hkern u1="X" u2="&#xb7;" k="30" />
    <hkern u1="X" u2="&#xa6;" k="-10" />
    <hkern u1="X" u2="&#x7e;" k="20" />
    <hkern u1="X" u2="&#x7c;" k="-20" />
    <hkern u1="X" u2="&#x7b;" k="30" />
    <hkern u1="X" u2="x" k="-20" />
    <hkern u1="X" u2="v" k="15" />
    <hkern u1="X" u2="_" k="-15" />
    <hkern u1="X" u2="\" k="-10" />
    <hkern u1="X" u2="V" k="-10" />
    <hkern u1="X" u2="&#x40;" k="25" />
    <hkern u1="X" u2="&#x3d;" k="10" />
    <hkern u1="X" u2="&#x3c;" k="20" />
    <hkern u1="X" u2="&#x2f;" k="-10" />
    <hkern u1="X" u2="&#x2b;" k="10" />
    <hkern u1="X" u2="&#x29;" k="-20" />
    <hkern u1="X" u2="&#x26;" k="10" />
    <hkern u1="Y" g2="aring.ss02" k="60" />
    <hkern u1="Y" g2="adieresis.ss02" k="50" />
    <hkern u1="Y" g2="atilde.ss02" k="60" />
    <hkern u1="Y" g2="agrave.ss02" k="50" />
    <hkern u1="Y" u2="&#x161;" k="45" />
    <hkern u1="Y" u2="&#xf5;" k="85" />
    <hkern u1="Y" u2="&#xf2;" k="75" />
    <hkern u1="Y" u2="&#xf0;" k="75" />
    <hkern u1="Y" u2="&#xef;" k="-10" />
    <hkern u1="Y" u2="&#xed;" k="30" />
    <hkern u1="Y" u2="&#xec;" k="-40" />
    <hkern u1="Z" u2="&#xef;" k="-5" />
    <hkern u1="Z" u2="&#xee;" k="-5" />
    <hkern u1="Z" u2="&#xec;" k="-15" />
    <hkern u1="[" u2="&#xec;" k="-40" />
    <hkern u1="\" g2="one.ss05" k="-20" />
    <hkern u1="\" u2="x" k="-10" />
    <hkern u1="\" u2="v" k="10" />
    <hkern u1="\" u2="X" k="-10" />
    <hkern u1="\" u2="V" k="60" />
    <hkern u1="\" u2="&#x38;" k="20" />
    <hkern u1="\" u2="&#x34;" k="10" />
    <hkern u1="\" u2="&#x33;" k="10" />
    <hkern u1="\" u2="&#x32;" k="-10" />
    <hkern u1="^" g2="one.ss05" k="40" />
    <hkern u1="_" g2="one.ss05" k="-20" />
    <hkern u1="_" u2="&#x192;" k="-60" />
    <hkern u1="_" u2="x" k="-30" />
    <hkern u1="_" u2="v" k="40" />
    <hkern u1="_" u2="X" k="-15" />
    <hkern u1="_" u2="V" k="50" />
    <hkern u1="_" u2="&#x38;" k="10" />
    <hkern u1="_" u2="&#x34;" k="50" />
    <hkern u1="f" u2="&#xef;" k="-20" />
    <hkern u1="f" u2="&#xee;" k="-20" />
    <hkern u1="f" u2="&#xec;" k="-30" />
    <hkern u1="v" g2="registered.ss06" k="-20" />
    <hkern u1="v" u2="&#x2089;" k="30" />
    <hkern u1="v" u2="&#x2088;" k="30" />
    <hkern u1="v" u2="&#x2087;" k="30" />
    <hkern u1="v" u2="&#x2086;" k="30" />
    <hkern u1="v" u2="&#x2085;" k="40" />
    <hkern u1="v" u2="&#x2084;" k="50" />
    <hkern u1="v" u2="&#x2083;" k="30" />
    <hkern u1="v" u2="&#x2082;" k="30" />
    <hkern u1="v" u2="&#x2081;" k="30" />
    <hkern u1="v" u2="&#x2080;" k="30" />
    <hkern u1="v" u2="&#x2021;" k="-30" />
    <hkern u1="v" u2="&#x2020;" k="-30" />
    <hkern u1="v" u2="&#x142;" k="-5" />
    <hkern u1="v" u2="&#xbf;" k="20" />
    <hkern u1="v" u2="&#xb6;" k="-20" />
    <hkern u1="v" u2="&#xa6;" k="-20" />
    <hkern u1="v" u2="x" k="-10" />
    <hkern u1="v" u2="v" k="-5" />
    <hkern u1="v" u2="_" k="40" />
    <hkern u1="v" u2="\" k="20" />
    <hkern u1="v" u2="&#x40;" k="-10" />
    <hkern u1="v" u2="&#x3f;" k="-20" />
    <hkern u1="v" u2="&#x2f;" k="20" />
    <hkern u1="v" u2="&#x2a;" k="-10" />
    <hkern u1="v" u2="&#x29;" k="20" />
    <hkern u1="x" g2="registered.ss06" k="-10" />
    <hkern u1="x" u2="&#x2122;" k="10" />
    <hkern u1="x" u2="&#x2087;" k="20" />
    <hkern u1="x" u2="&#x2021;" k="-10" />
    <hkern u1="x" u2="&#x2020;" k="-10" />
    <hkern u1="x" u2="&#xb7;" k="10" />
    <hkern u1="x" u2="&#xa1;" k="-10" />
    <hkern u1="x" u2="&#x7c;" k="-20" />
    <hkern u1="x" u2="x" k="-5" />
    <hkern u1="x" u2="v" k="-10" />
    <hkern u1="x" u2="_" k="-30" />
    <hkern u1="x" u2="\" k="20" />
    <hkern u1="x" u2="&#x40;" k="-5" />
    <hkern u1="x" u2="&#x2f;" k="-20" />
    <hkern u1="x" u2="&#x26;" k="10" />
    <hkern u1="&#x7b;" u2="&#xec;" k="-40" />
    <hkern u1="&#x7b;" u2="&#xe6;" k="5" />
    <hkern u1="&#x7b;" u2="&#xe5;" k="5" />
    <hkern u1="&#x7b;" u2="&#xe4;" k="5" />
    <hkern u1="&#x7b;" u2="&#xe3;" k="5" />
    <hkern u1="&#x7b;" u2="&#xe2;" k="5" />
    <hkern u1="&#x7b;" u2="&#xe1;" k="5" />
    <hkern u1="&#x7b;" u2="&#xe0;" k="5" />
    <hkern u1="&#x7b;" u2="a" k="5" />
    <hkern u1="&#x7c;" g2="one.ss05" k="20" />
    <hkern u1="&#x7c;" u2="x" k="-20" />
    <hkern u1="&#x7c;" u2="X" k="-20" />
    <hkern u1="&#x7c;" u2="V" k="-20" />
    <hkern u1="&#x7c;" u2="&#x37;" k="-20" />
    <hkern u1="&#x7d;" u2="&#x141;" k="-10" />
    <hkern u1="&#x7d;" u2="X" k="10" />
    <hkern u1="&#x7d;" u2="V" k="10" />
    <hkern u1="&#x7e;" g2="one.ss05" k="80" />
    <hkern u1="&#x7e;" u2="X" k="20" />
    <hkern u1="&#x7e;" u2="V" k="40" />
    <hkern u1="&#x7e;" u2="&#x37;" k="20" />
    <hkern u1="&#x7e;" u2="&#x32;" k="10" />
    <hkern u1="&#x7e;" u2="&#x31;" k="10" />
    <hkern u1="&#xa1;" u2="x" k="-10" />
    <hkern u1="&#xa1;" u2="V" k="20" />
    <hkern u1="&#xa1;" u2="&#x37;" k="-10" />
    <hkern u1="&#xa2;" g2="one.ss05" k="30" />
    <hkern u1="&#xa2;" u2="&#x33;" k="10" />
    <hkern u1="&#xa4;" g2="one.ss05" k="20" />
    <hkern u1="&#xa5;" g2="one.ss05" k="30" />
    <hkern u1="&#xa5;" u2="&#x37;" k="-30" />
    <hkern u1="&#xa5;" u2="&#x34;" k="10" />
    <hkern u1="&#xa6;" u2="v" k="-20" />
    <hkern u1="&#xa6;" u2="X" k="-10" />
    <hkern u1="&#xa6;" u2="V" k="-20" />
    <hkern u1="&#xa6;" u2="&#x38;" k="10" />
    <hkern u1="&#xa6;" u2="&#x37;" k="-10" />
    <hkern u1="&#xa7;" g2="one.ss05" k="20" />
    <hkern u1="&#xac;" u2="&#x33;" k="10" />
    <hkern u1="&#xb0;" g2="one.ss05" k="20" />
    <hkern u1="&#xb0;" u2="&#x37;" k="-40" />
    <hkern u1="&#xb0;" u2="&#x34;" k="40" />
    <hkern u1="&#xb0;" u2="&#x31;" k="-30" />
    <hkern u1="&#xb1;" u2="&#x37;" k="10" />
    <hkern u1="&#xb1;" u2="&#x33;" k="10" />
    <hkern u1="&#xb6;" g2="one.ss05" k="30" />
    <hkern u1="&#xb7;" g2="one.ss05" k="50" />
    <hkern u1="&#xb7;" u2="x" k="10" />
    <hkern u1="&#xb7;" u2="X" k="30" />
    <hkern u1="&#xb7;" u2="V" k="30" />
    <hkern u1="&#xb7;" u2="&#x37;" k="20" />
    <hkern u1="&#xb7;" u2="&#x31;" k="30" />
    <hkern u1="&#xbf;" g2="one.ss05" k="10" />
    <hkern u1="&#xbf;" u2="v" k="30" />
    <hkern u1="&#xbf;" u2="V" k="60" />
    <hkern u1="&#xbf;" u2="&#x34;" k="20" />
    <hkern u1="&#xbf;" u2="&#x33;" k="10" />
    <hkern u1="&#xbf;" u2="&#x31;" k="10" />
    <hkern u1="&#xc6;" u2="&#xec;" k="-10" />
    <hkern u1="&#xc7;" u2="&#xee;" k="-15" />
    <hkern u1="&#xc8;" u2="&#xec;" k="-10" />
    <hkern u1="&#xc9;" u2="&#xec;" k="-10" />
    <hkern u1="&#xca;" u2="&#xec;" k="-10" />
    <hkern u1="&#xcb;" u2="&#xec;" k="-10" />
    <hkern u1="&#xcd;" u2="&#xcf;" k="-50" />
    <hkern u1="&#xcd;" u2="&#xcc;" k="-90" />
    <hkern u1="&#xce;" u2="&#xcf;" k="-40" />
    <hkern u1="&#xce;" u2="&#xce;" k="-60" />
    <hkern u1="&#xce;" u2="&#xcc;" k="-30" />
    <hkern u1="&#xcf;" u2="&#xcf;" k="-20" />
    <hkern u1="&#xcf;" u2="&#xce;" k="-35" />
    <hkern u1="&#xcf;" u2="&#xcc;" k="-40" />
    <hkern u1="&#xd7;" u2="X" k="10" />
    <hkern u1="&#xd7;" u2="V" k="10" />
    <hkern u1="&#xd9;" u2="&#xee;" k="-5" />
    <hkern u1="&#xda;" u2="&#xee;" k="-5" />
    <hkern u1="&#xdb;" u2="&#xee;" k="-5" />
    <hkern u1="&#xdc;" u2="&#xee;" k="-5" />
    <hkern u1="&#xdd;" g2="aring.ss02" k="60" />
    <hkern u1="&#xdd;" g2="adieresis.ss02" k="50" />
    <hkern u1="&#xdd;" g2="atilde.ss02" k="60" />
    <hkern u1="&#xdd;" g2="agrave.ss02" k="50" />
    <hkern u1="&#xdd;" u2="&#x161;" k="45" />
    <hkern u1="&#xdd;" u2="&#xf5;" k="85" />
    <hkern u1="&#xdd;" u2="&#xf2;" k="75" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="75" />
    <hkern u1="&#xdd;" u2="&#xef;" k="-10" />
    <hkern u1="&#xdd;" u2="&#xed;" k="30" />
    <hkern u1="&#xdd;" u2="&#xec;" k="-40" />
    <hkern u1="&#xde;" g2="parenright.case" k="10" />
    <hkern u1="&#xde;" u2="&#x2122;" k="30" />
    <hkern u1="&#xde;" u2="&#x2086;" k="10" />
    <hkern u1="&#xde;" u2="&#x2084;" k="40" />
    <hkern u1="&#xde;" u2="&#x2021;" k="-10" />
    <hkern u1="&#xde;" u2="&#x2020;" k="-10" />
    <hkern u1="&#xde;" u2="&#x192;" k="40" />
    <hkern u1="&#xde;" u2="&#x142;" k="-20" />
    <hkern u1="&#xde;" u2="&#x141;" k="-15" />
    <hkern u1="&#xde;" u2="&#xdf;" k="15" />
    <hkern u1="&#xde;" u2="&#xbf;" k="20" />
    <hkern u1="&#xde;" u2="&#xb7;" k="-10" />
    <hkern u1="&#xde;" u2="&#x7e;" k="-10" />
    <hkern u1="&#xde;" u2="x" k="-10" />
    <hkern u1="&#xde;" u2="v" k="-10" />
    <hkern u1="&#xde;" u2="_" k="30" />
    <hkern u1="&#xde;" u2="\" k="40" />
    <hkern u1="&#xde;" u2="X" k="20" />
    <hkern u1="&#xde;" u2="V" k="15" />
    <hkern u1="&#xde;" u2="&#x40;" k="-5" />
    <hkern u1="&#xde;" u2="&#x3f;" k="-10" />
    <hkern u1="&#xde;" u2="&#x3e;" k="-10" />
    <hkern u1="&#xde;" u2="&#x2f;" k="40" />
    <hkern u1="&#xde;" u2="&#x29;" k="10" />
    <hkern u1="&#xde;" u2="&#x26;" k="10" />
    <hkern u1="&#xdf;" g2="registered.ss06" k="20" />
    <hkern u1="&#xdf;" u2="&#x142;" k="-5" />
    <hkern u1="&#xdf;" u2="&#xdf;" k="15" />
    <hkern u1="&#xdf;" u2="&#xba;" k="10" />
    <hkern u1="&#xdf;" u2="&#xaa;" k="20" />
    <hkern u1="&#xdf;" u2="\" k="10" />
    <hkern u1="&#xdf;" u2="&#x2f;" k="-10" />
    <hkern u1="&#xdf;" u2="&#x29;" k="-20" />
    <hkern u1="&#xec;" u2="\" k="20" />
    <hkern u1="&#xed;" u2="&#x201d;" k="-60" />
    <hkern u1="&#xed;" u2="&#x201c;" k="-30" />
    <hkern u1="&#xed;" u2="&#x2019;" k="-60" />
    <hkern u1="&#xed;" u2="&#x2018;" k="-30" />
    <hkern u1="&#xed;" u2="&#xef;" k="-40" />
    <hkern u1="&#xed;" u2="&#xee;" k="-40" />
    <hkern u1="&#xed;" u2="&#xec;" k="-80" />
    <hkern u1="&#xed;" u2="&#x7d;" k="-40" />
    <hkern u1="&#xed;" u2="]" k="-40" />
    <hkern u1="&#xed;" u2="\" k="-70" />
    <hkern u1="&#xed;" u2="&#x3f;" k="-30" />
    <hkern u1="&#xed;" u2="&#x29;" k="-30" />
    <hkern u1="&#xed;" u2="&#x27;" k="-40" />
    <hkern u1="&#xed;" u2="&#x22;" k="-40" />
    <hkern u1="&#xee;" u2="&#x201d;" k="-30" />
    <hkern u1="&#xee;" u2="&#x201c;" k="-35" />
    <hkern u1="&#xee;" u2="&#x2019;" k="-30" />
    <hkern u1="&#xee;" u2="&#x2018;" k="-35" />
    <hkern u1="&#xee;" u2="&#xef;" k="-50" />
    <hkern u1="&#xee;" u2="&#xee;" k="-70" />
    <hkern u1="&#xee;" u2="\" k="-20" />
    <hkern u1="&#xee;" u2="&#x3f;" k="-40" />
    <hkern u1="&#xee;" u2="&#x2a;" k="-30" />
    <hkern u1="&#xee;" u2="&#x27;" k="-40" />
    <hkern u1="&#xee;" u2="&#x22;" k="-40" />
    <hkern u1="&#xef;" u2="&#x201d;" k="-30" />
    <hkern u1="&#xef;" u2="&#x201c;" k="-25" />
    <hkern u1="&#xef;" u2="&#x2019;" k="-30" />
    <hkern u1="&#xef;" u2="&#x2018;" k="-25" />
    <hkern u1="&#xef;" u2="&#xef;" k="-35" />
    <hkern u1="&#xef;" u2="&#xee;" k="-40" />
    <hkern u1="&#xef;" u2="&#xec;" k="-35" />
    <hkern u1="&#xef;" u2="\" k="-10" />
    <hkern u1="&#xef;" u2="&#x3f;" k="-30" />
    <hkern u1="&#xef;" u2="&#x2a;" k="-25" />
    <hkern u1="&#xef;" u2="&#x27;" k="-40" />
    <hkern u1="&#xef;" u2="&#x22;" k="-40" />
    <hkern u1="&#xf0;" u2="\" k="40" />
    <hkern u1="&#xf7;" g2="one.ss05" k="50" />
    <hkern u1="&#xf7;" u2="X" k="10" />
    <hkern u1="&#xf7;" u2="V" k="40" />
    <hkern u1="&#xf7;" u2="&#x34;" k="10" />
    <hkern u1="&#xf7;" u2="&#x33;" k="20" />
    <hkern u1="&#xf7;" u2="&#x31;" k="10" />
    <hkern u1="&#x142;" g2="registered.ss06" k="-20" />
    <hkern u1="&#x142;" u2="&#x2084;" k="20" />
    <hkern u1="&#x142;" u2="&#x2021;" k="-20" />
    <hkern u1="&#x142;" u2="&#x142;" k="-10" />
    <hkern u1="&#x142;" u2="&#xb7;" k="-10" />
    <hkern u1="&#x142;" u2="&#xb6;" k="-20" />
    <hkern u1="&#x142;" u2="x" k="-20" />
    <hkern u1="&#x142;" u2="v" k="-30" />
    <hkern u1="&#x142;" u2="&#x40;" k="-30" />
    <hkern u1="&#x142;" u2="&#x2f;" k="-10" />
    <hkern u1="&#x152;" u2="&#xec;" k="-10" />
    <hkern u1="&#x178;" g2="aring.ss02" k="60" />
    <hkern u1="&#x178;" g2="adieresis.ss02" k="50" />
    <hkern u1="&#x178;" g2="atilde.ss02" k="60" />
    <hkern u1="&#x178;" g2="agrave.ss02" k="50" />
    <hkern u1="&#x178;" u2="&#x161;" k="45" />
    <hkern u1="&#x178;" u2="&#xf5;" k="85" />
    <hkern u1="&#x178;" u2="&#xf2;" k="75" />
    <hkern u1="&#x178;" u2="&#xf0;" k="75" />
    <hkern u1="&#x178;" u2="&#xef;" k="-10" />
    <hkern u1="&#x178;" u2="&#xed;" k="30" />
    <hkern u1="&#x178;" u2="&#xec;" k="-40" />
    <hkern u1="&#x17d;" u2="&#xef;" k="-5" />
    <hkern u1="&#x17d;" u2="&#xee;" k="-5" />
    <hkern u1="&#x17d;" u2="&#xec;" k="-15" />
    <hkern u1="&#x192;" g2="registered.ss06" k="-20" />
    <hkern u1="&#x192;" g2="ampersand.ss04" k="10" />
    <hkern u1="&#x192;" u2="&#x2089;" k="80" />
    <hkern u1="&#x192;" u2="&#x2088;" k="90" />
    <hkern u1="&#x192;" u2="&#x2087;" k="60" />
    <hkern u1="&#x192;" u2="&#x2086;" k="80" />
    <hkern u1="&#x192;" u2="&#x2085;" k="50" />
    <hkern u1="&#x192;" u2="&#x2084;" k="100" />
    <hkern u1="&#x192;" u2="&#x2083;" k="70" />
    <hkern u1="&#x192;" u2="&#x2082;" k="60" />
    <hkern u1="&#x192;" u2="&#x2081;" k="40" />
    <hkern u1="&#x192;" u2="&#x2080;" k="80" />
    <hkern u1="&#x192;" u2="&#x2079;" k="-10" />
    <hkern u1="&#x192;" u2="&#x2078;" k="-10" />
    <hkern u1="&#x192;" u2="&#x2077;" k="-20" />
    <hkern u1="&#x192;" u2="&#x2075;" k="-10" />
    <hkern u1="&#x192;" u2="&#x2021;" k="-20" />
    <hkern u1="&#x192;" u2="&#x2020;" k="-20" />
    <hkern u1="&#x192;" u2="&#x192;" k="90" />
    <hkern u1="&#x192;" u2="&#xf7;" k="30" />
    <hkern u1="&#x192;" u2="&#xb9;" k="-30" />
    <hkern u1="&#x192;" u2="&#xb6;" k="10" />
    <hkern u1="&#x192;" u2="&#xb3;" k="-10" />
    <hkern u1="&#x192;" u2="&#xb2;" k="-10" />
    <hkern u1="&#x192;" u2="&#x7e;" k="20" />
    <hkern u1="&#x192;" u2="x" k="10" />
    <hkern u1="&#x192;" u2="v" k="10" />
    <hkern u1="&#x192;" u2="_" k="70" />
    <hkern u1="&#x192;" u2="\" k="-40" />
    <hkern u1="&#x192;" u2="&#x40;" k="20" />
    <hkern u1="&#x192;" u2="&#x3e;" k="-40" />
    <hkern u1="&#x192;" u2="&#x3d;" k="10" />
    <hkern u1="&#x192;" u2="&#x3c;" k="20" />
    <hkern u1="&#x192;" u2="&#x2f;" k="40" />
    <hkern u1="&#x192;" u2="&#x2b;" k="20" />
    <hkern u1="&#x192;" u2="&#x2a;" k="-30" />
    <hkern u1="&#x192;" u2="&#x29;" k="-10" />
    <hkern u1="&#x192;" u2="&#x26;" k="20" />
    <hkern u1="&#x2018;" u2="&#xef;" k="-30" />
    <hkern u1="&#x2018;" u2="&#xee;" k="-30" />
    <hkern u1="&#x2018;" u2="&#xec;" k="-40" />
    <hkern u1="&#x2019;" u2="&#xef;" k="-30" />
    <hkern u1="&#x2019;" u2="&#xee;" k="-40" />
    <hkern u1="&#x2019;" u2="&#xec;" k="-30" />
    <hkern u1="&#x201a;" u2="g" k="-10" />
    <hkern u1="&#x201c;" u2="&#xef;" k="-30" />
    <hkern u1="&#x201c;" u2="&#xee;" k="-30" />
    <hkern u1="&#x201c;" u2="&#xec;" k="-40" />
    <hkern u1="&#x201d;" u2="&#xef;" k="-30" />
    <hkern u1="&#x201d;" u2="&#xee;" k="-40" />
    <hkern u1="&#x201d;" u2="&#xec;" k="-30" />
    <hkern u1="&#x201e;" u2="g" k="-10" />
    <hkern u1="&#x2020;" u2="x" k="-10" />
    <hkern u1="&#x2020;" u2="v" k="-30" />
    <hkern u1="&#x2020;" u2="V" k="-20" />
    <hkern u1="&#x2021;" g2="one.ss05" k="20" />
    <hkern u1="&#x2021;" u2="x" k="-10" />
    <hkern u1="&#x2021;" u2="v" k="-30" />
    <hkern u1="&#x2021;" u2="V" k="-10" />
    <hkern u1="&#x2022;" g2="one.ss05" k="60" />
    <hkern u1="&#x2022;" u2="X" k="30" />
    <hkern u1="&#x2022;" u2="V" k="40" />
    <hkern u1="&#x2026;" u2="g" k="10" />
    <hkern u1="&#x2044;" g2="nine.dnom" k="230" />
    <hkern u1="&#x2044;" g2="eight.dnom" k="230" />
    <hkern u1="&#x2044;" g2="seven.dnom" k="200" />
    <hkern u1="&#x2044;" g2="six.dnom" k="245" />
    <hkern u1="&#x2044;" g2="five.dnom" k="230" />
    <hkern u1="&#x2044;" g2="four.dnom" k="280" />
    <hkern u1="&#x2044;" g2="three.dnom" k="210" />
    <hkern u1="&#x2044;" g2="two.dnom" k="235" />
    <hkern u1="&#x2044;" g2="one.dnom" k="200" />
    <hkern u1="&#x2044;" g2="zero.dnom" k="235" />
    <hkern u1="&#x2080;" u2="v" k="30" />
    <hkern u1="&#x2080;" u2="V" k="100" />
    <hkern u1="&#x2081;" u2="v" k="30" />
    <hkern u1="&#x2081;" u2="V" k="60" />
    <hkern u1="&#x2082;" u2="v" k="30" />
    <hkern u1="&#x2082;" u2="V" k="80" />
    <hkern u1="&#x2083;" u2="v" k="30" />
    <hkern u1="&#x2083;" u2="V" k="80" />
    <hkern u1="&#x2084;" u2="v" k="30" />
    <hkern u1="&#x2084;" u2="V" k="90" />
    <hkern u1="&#x2085;" u2="v" k="30" />
    <hkern u1="&#x2085;" u2="V" k="90" />
    <hkern u1="&#x2086;" u2="v" k="30" />
    <hkern u1="&#x2086;" u2="V" k="90" />
    <hkern u1="&#x2087;" u2="x" k="20" />
    <hkern u1="&#x2087;" u2="v" k="30" />
    <hkern u1="&#x2087;" u2="V" k="90" />
    <hkern u1="&#x2088;" u2="v" k="40" />
    <hkern u1="&#x2088;" u2="V" k="100" />
    <hkern u1="&#x2089;" u2="v" k="30" />
    <hkern u1="&#x2089;" u2="V" k="100" />
    <hkern u1="&#x20ac;" u2="&#x37;" k="-10" />
    <hkern u1="&#x20ac;" u2="&#x34;" k="20" />
    <hkern u1="&#x2113;" u2="&#x39;" k="-10" />
    <hkern u1="&#x2113;" u2="&#x38;" k="-10" />
    <hkern u1="&#x2113;" u2="&#x37;" k="-40" />
    <hkern u1="&#x2122;" g2="one.ss05" k="10" />
    <hkern u1="&#x2202;" g2="one.ss05" k="20" />
    <hkern u1="&#x2206;" g2="one.ss05" k="-20" />
    <hkern u1="&#x220f;" g2="one.ss05" k="20" />
    <hkern u1="&#x2212;" g2="one.ss05" k="40" />
    <hkern u1="&#x2212;" u2="&#x33;" k="20" />
    <hkern u1="&#x2212;" u2="&#x32;" k="10" />
    <hkern u1="&#x2212;" u2="&#x29;" k="40" />
    <hkern u1="&#x2215;" u2="&#x37;" k="-40" />
    <hkern u1="&#x2215;" u2="&#x35;" k="20" />
    <hkern u1="&#x2215;" u2="&#x34;" k="50" />
    <hkern u1="&#x221a;" g2="one.ss05" k="110" />
    <hkern u1="&#x221a;" u2="&#x39;" k="40" />
    <hkern u1="&#x221a;" u2="&#x38;" k="40" />
    <hkern u1="&#x221a;" u2="&#x35;" k="50" />
    <hkern u1="&#x221a;" u2="&#x34;" k="100" />
    <hkern u1="&#x221a;" u2="&#x33;" k="40" />
    <hkern u1="&#x221a;" u2="&#x32;" k="50" />
    <hkern u1="&#x221a;" u2="&#x31;" k="30" />
    <hkern u1="&#x221e;" g2="one.ss05" k="60" />
    <hkern u1="&#x221e;" u2="&#x37;" k="10" />
    <hkern u1="&#x222b;" u2="&#x39;" k="-20" />
    <hkern u1="&#x222b;" u2="&#x38;" k="-10" />
    <hkern u1="&#x222b;" u2="&#x37;" k="-90" />
    <hkern u1="&#x222b;" u2="&#x35;" k="-20" />
    <hkern u1="&#x222b;" u2="&#x33;" k="-40" />
    <hkern u1="&#x222b;" u2="&#x32;" k="-40" />
    <hkern u1="&#x222b;" u2="&#x31;" k="-40" />
    <hkern u1="&#x2248;" g2="one.ss05" k="40" />
    <hkern u1="&#x2248;" u2="&#x38;" k="-20" />
    <hkern u1="&#x2248;" u2="&#x37;" k="20" />
    <hkern u1="&#x2248;" u2="&#x34;" k="-10" />
    <hkern u1="&#x2260;" u2="&#x38;" k="-10" />
    <hkern u1="&#x2264;" g2="one.ss05" k="20" />
    <hkern u1="&#x2265;" g2="one.ss05" k="30" />
    <hkern u1="&#x2265;" u2="&#x31;" k="10" />
    <hkern u1="&#xfb00;" u2="&#xef;" k="-20" />
    <hkern u1="&#xfb00;" u2="&#xee;" k="-20" />
    <hkern u1="&#xfb00;" u2="&#xec;" k="-30" />
    <hkern g1="zero.numr" g2="eight.numr" k="5" />
    <hkern g1="zero.numr" g2="six.numr" k="5" />
    <hkern g1="zero.numr" g2="five.numr" k="5" />
    <hkern g1="zero.numr" g2="three.numr" k="5" />
    <hkern g1="zero.numr" g2="one.numr" k="10" />
    <hkern g1="zero.numr" g2="zero.numr" k="5" />
    <hkern g1="zero.numr" u2="&#x2044;" k="235" />
    <hkern g1="one.numr" g2="seven.numr" k="-5" />
    <hkern g1="one.numr" g2="six.numr" k="-5" />
    <hkern g1="one.numr" g2="two.numr" k="-5" />
    <hkern g1="one.numr" g2="zero.numr" k="-5" />
    <hkern g1="one.numr" u2="&#x2044;" k="190" />
    <hkern g1="two.numr" g2="four.numr" k="5" />
    <hkern g1="two.numr" u2="&#x2044;" k="200" />
    <hkern g1="three.numr" g2="seven.numr" k="-10" />
    <hkern g1="three.numr" g2="one.numr" k="5" />
    <hkern g1="three.numr" u2="&#x2044;" k="225" />
    <hkern g1="four.numr" u2="&#x2044;" k="230" />
    <hkern g1="five.numr" g2="seven.numr" k="-5" />
    <hkern g1="five.numr" u2="&#x2044;" k="200" />
    <hkern g1="six.numr" g2="seven.numr" k="-5" />
    <hkern g1="six.numr" u2="&#x2044;" k="225" />
    <hkern g1="seven.numr" g2="nine.numr" k="-10" />
    <hkern g1="seven.numr" g2="seven.numr" k="-30" />
    <hkern g1="seven.numr" g2="six.numr" k="10" />
    <hkern g1="seven.numr" g2="four.numr" k="35" />
    <hkern g1="seven.numr" g2="one.numr" k="-5" />
    <hkern g1="seven.numr" g2="zero.numr" k="10" />
    <hkern g1="seven.numr" u2="&#x2044;" k="280" />
    <hkern g1="eight.numr" g2="six.numr" k="5" />
    <hkern g1="eight.numr" g2="zero.numr" k="5" />
    <hkern g1="eight.numr" u2="&#x2044;" k="225" />
    <hkern g1="nine.numr" g2="eight.numr" k="5" />
    <hkern g1="nine.numr" g2="six.numr" k="5" />
    <hkern g1="nine.numr" g2="five.numr" k="5" />
    <hkern g1="nine.numr" g2="three.numr" k="5" />
    <hkern g1="nine.numr" g2="one.numr" k="10" />
    <hkern g1="nine.numr" g2="zero.numr" k="5" />
    <hkern g1="nine.numr" u2="&#x2044;" k="245" />
    <hkern g1="zero.dnom" g2="eight.dnom" k="5" />
    <hkern g1="zero.dnom" g2="six.dnom" k="5" />
    <hkern g1="zero.dnom" g2="five.dnom" k="5" />
    <hkern g1="zero.dnom" g2="three.dnom" k="5" />
    <hkern g1="zero.dnom" g2="one.dnom" k="10" />
    <hkern g1="zero.dnom" g2="zero.dnom" k="5" />
    <hkern g1="one.dnom" g2="seven.dnom" k="-5" />
    <hkern g1="one.dnom" g2="six.dnom" k="-5" />
    <hkern g1="one.dnom" g2="two.dnom" k="-5" />
    <hkern g1="one.dnom" g2="zero.dnom" k="-5" />
    <hkern g1="two.dnom" g2="four.dnom" k="5" />
    <hkern g1="three.dnom" g2="seven.dnom" k="-10" />
    <hkern g1="three.dnom" g2="one.dnom" k="5" />
    <hkern g1="five.dnom" g2="seven.dnom" k="-5" />
    <hkern g1="six.dnom" g2="seven.dnom" k="-5" />
    <hkern g1="seven.dnom" g2="nine.dnom" k="-10" />
    <hkern g1="seven.dnom" g2="seven.dnom" k="-30" />
    <hkern g1="seven.dnom" g2="six.dnom" k="10" />
    <hkern g1="seven.dnom" g2="four.dnom" k="35" />
    <hkern g1="seven.dnom" g2="one.dnom" k="-5" />
    <hkern g1="seven.dnom" g2="zero.dnom" k="10" />
    <hkern g1="eight.dnom" g2="six.dnom" k="5" />
    <hkern g1="eight.dnom" g2="zero.dnom" k="5" />
    <hkern g1="nine.dnom" g2="eight.dnom" k="5" />
    <hkern g1="nine.dnom" g2="six.dnom" k="5" />
    <hkern g1="nine.dnom" g2="five.dnom" k="5" />
    <hkern g1="nine.dnom" g2="three.dnom" k="5" />
    <hkern g1="nine.dnom" g2="one.dnom" k="10" />
    <hkern g1="nine.dnom" g2="zero.dnom" k="5" />
    <hkern g1="a.ordn" g2="u.ordn" k="10" />
    <hkern g1="b.ordn" g2="z.ordn" k="5" />
    <hkern g1="b.ordn" g2="y.ordn" k="15" />
    <hkern g1="b.ordn" g2="x.ordn" k="10" />
    <hkern g1="b.ordn" g2="w.ordn" k="10" />
    <hkern g1="b.ordn" g2="v.ordn" k="15" />
    <hkern g1="b.ordn" g2="u.ordn" k="5" />
    <hkern g1="b.ordn" g2="t.ordn" k="5" />
    <hkern g1="c.ordn" g2="y.ordn" k="5" />
    <hkern g1="c.ordn" g2="x.ordn" k="5" />
    <hkern g1="c.ordn" g2="w.ordn" k="5" />
    <hkern g1="c.ordn" g2="v.ordn" k="5" />
    <hkern g1="c.ordn" g2="u.ordn" k="5" />
    <hkern g1="c.ordn" g2="q.ordn" k="5" />
    <hkern g1="c.ordn" g2="o.ordn" k="5" />
    <hkern g1="c.ordn" g2="g.ordn" k="5" />
    <hkern g1="c.ordn" g2="e.ordn" k="5" />
    <hkern g1="c.ordn" g2="d.ordn" k="5" />
    <hkern g1="c.ordn" g2="c.ordn" k="5" />
    <hkern g1="c.ordn" g2="a.ordn" k="5" />
    <hkern g1="e.ordn" g2="y.ordn" k="10" />
    <hkern g1="e.ordn" g2="x.ordn" k="5" />
    <hkern g1="e.ordn" g2="w.ordn" k="5" />
    <hkern g1="e.ordn" g2="v.ordn" k="10" />
    <hkern g1="e.ordn" g2="u.ordn" k="15" />
    <hkern g1="f.ordn" g2="z.ordn" k="-10" />
    <hkern g1="f.ordn" g2="y.ordn" k="-15" />
    <hkern g1="f.ordn" g2="x.ordn" k="-10" />
    <hkern g1="f.ordn" g2="w.ordn" k="-10" />
    <hkern g1="f.ordn" g2="v.ordn" k="-10" />
    <hkern g1="f.ordn" g2="u.ordn" k="5" />
    <hkern g1="f.ordn" g2="q.ordn" k="20" />
    <hkern g1="f.ordn" g2="o.ordn" k="20" />
    <hkern g1="f.ordn" g2="j.ordn" k="-20" />
    <hkern g1="f.ordn" g2="g.ordn" k="20" />
    <hkern g1="f.ordn" g2="f.ordn" k="5" />
    <hkern g1="f.ordn" g2="e.ordn" k="20" />
    <hkern g1="f.ordn" g2="d.ordn" k="20" />
    <hkern g1="f.ordn" g2="c.ordn" k="20" />
    <hkern g1="f.ordn" g2="a.ordn" k="15" />
    <hkern g1="g.ordn" g2="y.ordn" k="5" />
    <hkern g1="g.ordn" g2="w.ordn" k="5" />
    <hkern g1="g.ordn" g2="u.ordn" k="5" />
    <hkern g1="g.ordn" g2="a.ordn" k="5" />
    <hkern g1="h.ordn" g2="u.ordn" k="10" />
    <hkern g1="k.ordn" g2="z.ordn" k="-5" />
    <hkern g1="k.ordn" g2="u.ordn" k="5" />
    <hkern g1="k.ordn" g2="q.ordn" k="20" />
    <hkern g1="k.ordn" g2="o.ordn" k="20" />
    <hkern g1="k.ordn" g2="g.ordn" k="20" />
    <hkern g1="k.ordn" g2="e.ordn" k="20" />
    <hkern g1="k.ordn" g2="d.ordn" k="20" />
    <hkern g1="k.ordn" g2="c.ordn" k="20" />
    <hkern g1="k.ordn" g2="a.ordn" k="5" />
    <hkern g1="m.ordn" g2="u.ordn" k="10" />
    <hkern g1="n.ordn" g2="u.ordn" k="10" />
    <hkern g1="o.ordn" g2="z.ordn" k="5" />
    <hkern g1="o.ordn" g2="y.ordn" k="15" />
    <hkern g1="o.ordn" g2="x.ordn" k="10" />
    <hkern g1="o.ordn" g2="w.ordn" k="10" />
    <hkern g1="o.ordn" g2="v.ordn" k="15" />
    <hkern g1="o.ordn" g2="u.ordn" k="5" />
    <hkern g1="o.ordn" g2="t.ordn" k="5" />
    <hkern g1="p.ordn" g2="z.ordn" k="5" />
    <hkern g1="p.ordn" g2="y.ordn" k="15" />
    <hkern g1="p.ordn" g2="x.ordn" k="10" />
    <hkern g1="p.ordn" g2="w.ordn" k="10" />
    <hkern g1="p.ordn" g2="v.ordn" k="15" />
    <hkern g1="p.ordn" g2="u.ordn" k="5" />
    <hkern g1="p.ordn" g2="t.ordn" k="5" />
    <hkern g1="r.ordn" g2="z.ordn" k="-10" />
    <hkern g1="r.ordn" g2="y.ordn" k="-15" />
    <hkern g1="r.ordn" g2="x.ordn" k="-10" />
    <hkern g1="r.ordn" g2="w.ordn" k="-10" />
    <hkern g1="r.ordn" g2="v.ordn" k="-15" />
    <hkern g1="r.ordn" g2="t.ordn" k="-10" />
    <hkern g1="r.ordn" g2="q.ordn" k="10" />
    <hkern g1="r.ordn" g2="o.ordn" k="10" />
    <hkern g1="r.ordn" g2="g.ordn" k="10" />
    <hkern g1="r.ordn" g2="f.ordn" k="-15" />
    <hkern g1="r.ordn" g2="e.ordn" k="10" />
    <hkern g1="r.ordn" g2="d.ordn" k="10" />
    <hkern g1="r.ordn" g2="c.ordn" k="10" />
    <hkern g1="r.ordn" u2="&#x37;" k="-40" />
    <hkern g1="s.ordn" g2="y.ordn" k="5" />
    <hkern g1="s.ordn" g2="x.ordn" k="5" />
    <hkern g1="s.ordn" g2="w.ordn" k="5" />
    <hkern g1="s.ordn" g2="v.ordn" k="5" />
    <hkern g1="s.ordn" g2="u.ordn" k="5" />
    <hkern g1="s.ordn" g2="q.ordn" k="5" />
    <hkern g1="s.ordn" g2="o.ordn" k="5" />
    <hkern g1="s.ordn" g2="g.ordn" k="5" />
    <hkern g1="s.ordn" g2="e.ordn" k="5" />
    <hkern g1="s.ordn" g2="d.ordn" k="5" />
    <hkern g1="s.ordn" g2="c.ordn" k="5" />
    <hkern g1="s.ordn" g2="a.ordn" k="10" />
    <hkern g1="t.ordn" g2="z.ordn" k="-20" />
    <hkern g1="t.ordn" g2="y.ordn" k="-5" />
    <hkern g1="t.ordn" g2="x.ordn" k="-20" />
    <hkern g1="t.ordn" g2="w.ordn" k="-10" />
    <hkern g1="t.ordn" g2="v.ordn" k="-10" />
    <hkern g1="t.ordn" g2="s.ordn" k="-5" />
    <hkern g1="u.ordn" g2="u.ordn" k="5" />
    <hkern g1="u.ordn" g2="q.ordn" k="5" />
    <hkern g1="u.ordn" g2="o.ordn" k="5" />
    <hkern g1="u.ordn" g2="g.ordn" k="5" />
    <hkern g1="u.ordn" g2="e.ordn" k="5" />
    <hkern g1="u.ordn" g2="d.ordn" k="5" />
    <hkern g1="u.ordn" g2="c.ordn" k="5" />
    <hkern g1="v.ordn" g2="z.ordn" k="-10" />
    <hkern g1="v.ordn" g2="y.ordn" k="-10" />
    <hkern g1="v.ordn" g2="x.ordn" k="-10" />
    <hkern g1="v.ordn" g2="w.ordn" k="-5" />
    <hkern g1="v.ordn" g2="v.ordn" k="-5" />
    <hkern g1="v.ordn" g2="t.ordn" k="-5" />
    <hkern g1="v.ordn" g2="q.ordn" k="15" />
    <hkern g1="v.ordn" g2="o.ordn" k="15" />
    <hkern g1="v.ordn" g2="g.ordn" k="15" />
    <hkern g1="v.ordn" g2="f.ordn" k="-15" />
    <hkern g1="v.ordn" g2="e.ordn" k="15" />
    <hkern g1="v.ordn" g2="d.ordn" k="15" />
    <hkern g1="v.ordn" g2="c.ordn" k="15" />
    <hkern g1="v.ordn" g2="a.ordn" k="5" />
    <hkern g1="w.ordn" g2="z.ordn" k="-5" />
    <hkern g1="w.ordn" g2="y.ordn" k="-5" />
    <hkern g1="w.ordn" g2="x.ordn" k="-5" />
    <hkern g1="w.ordn" g2="v.ordn" k="-5" />
    <hkern g1="w.ordn" g2="t.ordn" k="-5" />
    <hkern g1="w.ordn" g2="q.ordn" k="10" />
    <hkern g1="w.ordn" g2="o.ordn" k="10" />
    <hkern g1="w.ordn" g2="g.ordn" k="10" />
    <hkern g1="w.ordn" g2="f.ordn" k="-15" />
    <hkern g1="w.ordn" g2="e.ordn" k="10" />
    <hkern g1="w.ordn" g2="d.ordn" k="10" />
    <hkern g1="w.ordn" g2="c.ordn" k="10" />
    <hkern g1="w.ordn" g2="a.ordn" k="5" />
    <hkern g1="x.ordn" g2="z.ordn" k="-20" />
    <hkern g1="x.ordn" g2="y.ordn" k="-5" />
    <hkern g1="x.ordn" g2="x.ordn" k="-5" />
    <hkern g1="x.ordn" g2="w.ordn" k="-5" />
    <hkern g1="x.ordn" g2="v.ordn" k="-10" />
    <hkern g1="x.ordn" g2="u.ordn" k="5" />
    <hkern g1="x.ordn" g2="t.ordn" k="-5" />
    <hkern g1="x.ordn" g2="q.ordn" k="10" />
    <hkern g1="x.ordn" g2="o.ordn" k="10" />
    <hkern g1="x.ordn" g2="g.ordn" k="10" />
    <hkern g1="x.ordn" g2="f.ordn" k="-15" />
    <hkern g1="x.ordn" g2="e.ordn" k="10" />
    <hkern g1="x.ordn" g2="d.ordn" k="10" />
    <hkern g1="x.ordn" g2="c.ordn" k="10" />
    <hkern g1="y.ordn" g2="y.ordn" k="-5" />
    <hkern g1="y.ordn" g2="v.ordn" k="-5" />
    <hkern g1="y.ordn" g2="u.ordn" k="5" />
    <hkern g1="y.ordn" g2="t.ordn" k="-5" />
    <hkern g1="y.ordn" g2="s.ordn" k="10" />
    <hkern g1="y.ordn" g2="q.ordn" k="20" />
    <hkern g1="y.ordn" g2="o.ordn" k="20" />
    <hkern g1="y.ordn" g2="g.ordn" k="20" />
    <hkern g1="y.ordn" g2="f.ordn" k="-10" />
    <hkern g1="y.ordn" g2="e.ordn" k="20" />
    <hkern g1="y.ordn" g2="d.ordn" k="20" />
    <hkern g1="y.ordn" g2="c.ordn" k="20" />
    <hkern g1="y.ordn" g2="a.ordn" k="10" />
    <hkern g1="z.ordn" g2="x.ordn" k="-5" />
    <hkern g1="florin.tf" u2="&#x37;" k="-50" />
    <hkern g1="florin.tf" u2="&#x35;" k="20" />
    <hkern g1="florin.tf" u2="&#x34;" k="60" />
    <hkern g1="parenleft.case" g2="one.ss05" k="20" />
    <hkern g1="parenleft.case" u2="&#x192;" k="-40" />
    <hkern g1="parenleft.case" u2="X" k="-20" />
    <hkern g1="parenleft.case" u2="V" k="-20" />
    <hkern g1="parenleft.case" u2="&#x39;" k="-10" />
    <hkern g1="parenleft.case" u2="&#x37;" k="-20" />
    <hkern g1="parenleft.case" u2="&#x35;" k="20" />
    <hkern g1="parenleft.case" u2="&#x34;" k="30" />
    <hkern g1="parenleft.case" u2="&#x32;" k="-10" />
    <hkern g1="parenleft.case" u2="&#x31;" k="10" />
    <hkern g1="braceleft.case" u2="&#xe6;" k="-5" />
    <hkern g1="braceleft.case" u2="&#xe5;" k="-5" />
    <hkern g1="braceleft.case" u2="&#xe4;" k="-5" />
    <hkern g1="braceleft.case" u2="&#xe3;" k="-5" />
    <hkern g1="braceleft.case" u2="&#xe2;" k="-5" />
    <hkern g1="braceleft.case" u2="&#xe1;" k="-5" />
    <hkern g1="braceleft.case" u2="&#xe0;" k="-5" />
    <hkern g1="braceleft.case" u2="a" k="-5" />
    <hkern g1="exclamdown.case" u2="V" k="-10" />
    <hkern g1="exclamdown.case" u2="&#x37;" k="-20" />
    <hkern g1="questiondown.case" g2="one.ss05" k="10" />
    <hkern g1="questiondown.case" u2="V" k="40" />
    <hkern g1="G.ss01" u2="&#xee;" k="-10" />
    <hkern g1="r.ss03" u2="&#x142;" k="-10" />
    <hkern g1="ampersand.ss04" g2="one.ss05" k="40" />
    <hkern g1="ampersand.ss04" u2="&#x142;" k="-20" />
    <hkern g1="ampersand.ss04" u2="&#x141;" k="-20" />
    <hkern g1="ampersand.ss04" u2="X" k="10" />
    <hkern g1="ampersand.ss04" u2="V" k="20" />
    <hkern g1="ampersand.ss04" u2="&#x39;" k="-10" />
    <hkern g1="ampersand.ss04" u2="&#x34;" k="-10" />
    <hkern g1="ampersand.ss04" u2="&#x20;" k="40" />
    <hkern g1="one.ss05" g2="registered.ss06" k="40" />
    <hkern g1="one.ss05" g2="questiondown.case" k="-20" />
    <hkern g1="one.ss05" g2="parenright.case" k="-10" />
    <hkern g1="one.ss05" u2="&#x2265;" k="-10" />
    <hkern g1="one.ss05" u2="&#x2206;" k="-40" />
    <hkern g1="one.ss05" u2="&#x2122;" k="20" />
    <hkern g1="one.ss05" u2="&#x20ac;" k="40" />
    <hkern g1="one.ss05" u2="&#x2021;" k="-20" />
    <hkern g1="one.ss05" u2="&#xbf;" k="-40" />
    <hkern g1="one.ss05" u2="&#xb7;" k="-10" />
    <hkern g1="one.ss05" u2="_" k="-70" />
    <hkern g1="one.ss05" u2="\" k="20" />
    <hkern g1="one.ss05" u2="&#x3e;" k="-30" />
    <hkern g1="one.ss05" u2="&#x39;" k="-25" />
    <hkern g1="one.ss05" u2="&#x38;" k="-10" />
    <hkern g1="one.ss05" u2="&#x37;" k="-10" />
    <hkern g1="one.ss05" u2="&#x35;" k="-15" />
    <hkern g1="one.ss05" u2="&#x33;" k="-20" />
    <hkern g1="one.ss05" u2="&#x32;" k="-30" />
    <hkern g1="one.ss05" u2="&#x2f;" k="-50" />
    <hkern g1="one.ss05" u2="&#x2a;" k="20" />
    <hkern g1="one.ss05" u2="&#x29;" k="-30" />
    <hkern g1="one.ss05" u2="&#x23;" k="10" />
    <hkern g1="registered.ss06" u2="&#x37;" k="-40" />
    <hkern g1="registered.ss06" u2="&#x35;" k="20" />
    <hkern g1="registered.ss06" u2="&#x34;" k="60" />
    <hkern g1="registered.ss06" u2="&#x31;" k="-10" />
    <hkern g1="B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="20" />
    <hkern g1="B"
	g2="AE"
	k="20" />
    <hkern g1="B"
	g2="J"
	k="-15" />
    <hkern g1="B"
	g2="Y,Yacute,Ydieresis"
	k="5" />
    <hkern g1="B"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="10" />
    <hkern g1="B"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="-5" />
    <hkern g1="B"
	g2="hyphen.case,endash.case,emdash.case"
	k="5" />
    <hkern g1="B"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="5" />
    <hkern g1="B"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="B"
	g2="guillemotleft.case,guilsinglleft.case"
	k="15" />
    <hkern g1="B"
	g2="m,n,p,r,ntilde,r.ss03"
	k="5" />
    <hkern g1="B"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="-5" />
    <hkern g1="B"
	g2="oslash"
	k="5" />
    <hkern g1="B"
	g2="comma,period,ellipsis"
	k="15" />
    <hkern g1="B"
	g2="quoteright,quotedblright"
	k="15" />
    <hkern g1="B"
	g2="s,scaron"
	k="5" />
    <hkern g1="B"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="20" />
    <hkern g1="B"
	g2="w"
	k="5" />
    <hkern g1="B"
	g2="y,yacute,ydieresis"
	k="5" />
    <hkern g1="Euro"
	g2="zero,six"
	k="10" />
    <hkern g1="F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="45" />
    <hkern g1="F"
	g2="AE"
	k="95" />
    <hkern g1="F"
	g2="J"
	k="40" />
    <hkern g1="F"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="35" />
    <hkern g1="F"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="35" />
    <hkern g1="F"
	g2="hyphen.case,endash.case,emdash.case"
	k="5" />
    <hkern g1="F"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="10" />
    <hkern g1="F"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="F"
	g2="guillemotleft.case,guilsinglleft.case"
	k="10" />
    <hkern g1="F"
	g2="i,igrave,iacute,icircumflex,idieresis,dotlessi"
	k="5" />
    <hkern g1="F"
	g2="m,n,p,r,ntilde,r.ss03"
	k="25" />
    <hkern g1="F"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="35" />
    <hkern g1="F"
	g2="oslash"
	k="40" />
    <hkern g1="F"
	g2="comma,period,ellipsis"
	k="100" />
    <hkern g1="F"
	g2="quoteright,quotedblright"
	k="-15" />
    <hkern g1="F"
	g2="s,scaron"
	k="15" />
    <hkern g1="F"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="45" />
    <hkern g1="F"
	g2="w"
	k="20" />
    <hkern g1="F"
	g2="y,yacute,ydieresis"
	k="15" />
    <hkern g1="F"
	g2="Eth"
	k="-5" />
    <hkern g1="F"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="5" />
    <hkern g1="F"
	g2="T"
	k="-30" />
    <hkern g1="F"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="5" />
    <hkern g1="F"
	g2="Z,Zcaron"
	k="-10" />
    <hkern g1="F"
	g2="bracketright,braceright"
	k="-30" />
    <hkern g1="F"
	g2="bracketright.case,braceright.case"
	k="-20" />
    <hkern g1="F"
	g2="hyphen,uni00AD,endash,emdash"
	k="10" />
    <hkern g1="F"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="F"
	g2="j"
	k="5" />
    <hkern g1="F"
	g2="b,h,k,l,thorn"
	k="-5" />
    <hkern g1="F"
	g2="quotedbl,quotesingle"
	k="-20" />
    <hkern g1="F"
	g2="quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="F"
	g2="quoteleft,quotedblleft"
	k="-20" />
    <hkern g1="F"
	g2="z,zcaron"
	k="10" />
    <hkern g1="P"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="60" />
    <hkern g1="P"
	g2="AE"
	k="150" />
    <hkern g1="P"
	g2="J"
	k="70" />
    <hkern g1="P"
	g2="Y,Yacute,Ydieresis"
	k="5" />
    <hkern g1="P"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="30" />
    <hkern g1="P"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="35" />
    <hkern g1="P"
	g2="hyphen.case,endash.case,emdash.case"
	k="10" />
    <hkern g1="P"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="-5" />
    <hkern g1="P"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="P"
	g2="guillemotleft.case,guilsinglleft.case"
	k="15" />
    <hkern g1="P"
	g2="m,n,p,r,ntilde,r.ss03"
	k="15" />
    <hkern g1="P"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="40" />
    <hkern g1="P"
	g2="oslash"
	k="35" />
    <hkern g1="P"
	g2="comma,period,ellipsis"
	k="120" />
    <hkern g1="P"
	g2="quoteright,quotedblright"
	k="-10" />
    <hkern g1="P"
	g2="s,scaron"
	k="25" />
    <hkern g1="P"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="20" />
    <hkern g1="P"
	g2="y,yacute,ydieresis"
	k="-10" />
    <hkern g1="P"
	g2="Eth"
	k="-10" />
    <hkern g1="P"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="5" />
    <hkern g1="P"
	g2="Z,Zcaron"
	k="10" />
    <hkern g1="P"
	g2="hyphen,uni00AD,endash,emdash"
	k="10" />
    <hkern g1="P"
	g2="guillemotright,guilsinglright"
	k="-10" />
    <hkern g1="P"
	g2="j"
	k="5" />
    <hkern g1="P"
	g2="b,h,k,l,thorn"
	k="10" />
    <hkern g1="P"
	g2="quotedbl,quotesingle"
	k="-10" />
    <hkern g1="P"
	g2="quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="P"
	g2="quoteleft,quotedblleft"
	k="-15" />
    <hkern g1="P"
	g2="z,zcaron"
	k="10" />
    <hkern g1="P"
	g2="W"
	k="5" />
    <hkern g1="P"
	g2="guillemotright.case,guilsinglright.case"
	k="-10" />
    <hkern g1="P"
	g2="t"
	k="-10" />
    <hkern g1="Q"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="15" />
    <hkern g1="Q"
	g2="AE"
	k="15" />
    <hkern g1="Q"
	g2="J"
	k="-5" />
    <hkern g1="Q"
	g2="Y,Yacute,Ydieresis"
	k="40" />
    <hkern g1="Q"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="5" />
    <hkern g1="Q"
	g2="comma,period,ellipsis"
	k="10" />
    <hkern g1="Q"
	g2="quoteright,quotedblright"
	k="10" />
    <hkern g1="Q"
	g2="w"
	k="5" />
    <hkern g1="Q"
	g2="y,yacute,ydieresis"
	k="5" />
    <hkern g1="Q"
	g2="Eth"
	k="-5" />
    <hkern g1="Q"
	g2="T"
	k="20" />
    <hkern g1="Q"
	g2="bracketright,braceright"
	k="10" />
    <hkern g1="Q"
	g2="bracketright.case,braceright.case"
	k="10" />
    <hkern g1="Q"
	g2="guillemotright,guilsinglright"
	k="15" />
    <hkern g1="Q"
	g2="W"
	k="20" />
    <hkern g1="Thorn"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="25" />
    <hkern g1="Thorn"
	g2="AE"
	k="130" />
    <hkern g1="Thorn"
	g2="J"
	k="5" />
    <hkern g1="Thorn"
	g2="Y,Yacute,Ydieresis"
	k="20" />
    <hkern g1="Thorn"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="-10" />
    <hkern g1="Thorn"
	g2="hyphen.case,endash.case,emdash.case"
	k="-10" />
    <hkern g1="Thorn"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="-20" />
    <hkern g1="Thorn"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="-10" />
    <hkern g1="Thorn"
	g2="oslash"
	k="5" />
    <hkern g1="Thorn"
	g2="comma,period,ellipsis"
	k="30" />
    <hkern g1="Thorn"
	g2="s,scaron"
	k="-10" />
    <hkern g1="Thorn"
	g2="w"
	k="-10" />
    <hkern g1="Thorn"
	g2="y,yacute,ydieresis"
	k="-10" />
    <hkern g1="Thorn"
	g2="Eth"
	k="-15" />
    <hkern g1="Thorn"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="-5" />
    <hkern g1="Thorn"
	g2="T"
	k="20" />
    <hkern g1="Thorn"
	g2="Z,Zcaron"
	k="10" />
    <hkern g1="Thorn"
	g2="bracketright,braceright"
	k="10" />
    <hkern g1="Thorn"
	g2="bracketright.case,braceright.case"
	k="10" />
    <hkern g1="Thorn"
	g2="quotesinglbase,quotedblbase"
	k="25" />
    <hkern g1="Thorn"
	g2="z,zcaron"
	k="-20" />
    <hkern g1="Thorn"
	g2="W"
	k="15" />
    <hkern g1="Thorn"
	g2="guillemotright.case,guilsinglright.case"
	k="-10" />
    <hkern g1="Thorn"
	g2="t"
	k="-25" />
    <hkern g1="Thorn"
	g2="Oslash"
	k="-5" />
    <hkern g1="V"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="55" />
    <hkern g1="V"
	g2="AE"
	k="135" />
    <hkern g1="V"
	g2="J"
	k="55" />
    <hkern g1="V"
	g2="Y,Yacute,Ydieresis"
	k="-10" />
    <hkern g1="V"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="55" />
    <hkern g1="V"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="45" />
    <hkern g1="V"
	g2="hyphen.case,endash.case,emdash.case"
	k="50" />
    <hkern g1="V"
	g2="guillemotleft,guilsinglleft"
	k="60" />
    <hkern g1="V"
	g2="guillemotleft.case,guilsinglleft.case"
	k="40" />
    <hkern g1="V"
	g2="m,n,p,r,ntilde,r.ss03"
	k="30" />
    <hkern g1="V"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="55" />
    <hkern g1="V"
	g2="oslash"
	k="55" />
    <hkern g1="V"
	g2="comma,period,ellipsis"
	k="100" />
    <hkern g1="V"
	g2="quoteright,quotedblright"
	k="-20" />
    <hkern g1="V"
	g2="s,scaron"
	k="20" />
    <hkern g1="V"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="35" />
    <hkern g1="V"
	g2="w"
	k="10" />
    <hkern g1="V"
	g2="y,yacute,ydieresis"
	k="5" />
    <hkern g1="V"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="25" />
    <hkern g1="V"
	g2="T"
	k="-20" />
    <hkern g1="V"
	g2="bracketright,braceright"
	k="-20" />
    <hkern g1="V"
	g2="bracketright.case,braceright.case"
	k="-20" />
    <hkern g1="V"
	g2="hyphen,uni00AD,endash,emdash"
	k="55" />
    <hkern g1="V"
	g2="guillemotright,guilsinglright"
	k="40" />
    <hkern g1="V"
	g2="quotedbl,quotesingle"
	k="-20" />
    <hkern g1="V"
	g2="quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="V"
	g2="quoteleft,quotedblleft"
	k="-10" />
    <hkern g1="V"
	g2="z,zcaron"
	k="20" />
    <hkern g1="V"
	g2="W"
	k="-10" />
    <hkern g1="V"
	g2="t"
	k="5" />
    <hkern g1="V"
	g2="Oslash"
	k="25" />
    <hkern g1="V"
	g2="S,Scaron"
	k="5" />
    <hkern g1="V"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="10" />
    <hkern g1="V"
	g2="colon,semicolon"
	k="40" />
    <hkern g1="X"
	g2="J"
	k="10" />
    <hkern g1="X"
	g2="Y,Yacute,Ydieresis"
	k="-10" />
    <hkern g1="X"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="15" />
    <hkern g1="X"
	g2="hyphen.case,endash.case,emdash.case"
	k="50" />
    <hkern g1="X"
	g2="guillemotleft,guilsinglleft"
	k="80" />
    <hkern g1="X"
	g2="guillemotleft.case,guilsinglleft.case"
	k="40" />
    <hkern g1="X"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="25" />
    <hkern g1="X"
	g2="oslash"
	k="10" />
    <hkern g1="X"
	g2="quoteright,quotedblright"
	k="-10" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="10" />
    <hkern g1="X"
	g2="w"
	k="10" />
    <hkern g1="X"
	g2="y,yacute,ydieresis"
	k="15" />
    <hkern g1="X"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="X"
	g2="T"
	k="-15" />
    <hkern g1="X"
	g2="Z,Zcaron"
	k="-10" />
    <hkern g1="X"
	g2="bracketright,braceright"
	k="-20" />
    <hkern g1="X"
	g2="bracketright.case,braceright.case"
	k="-20" />
    <hkern g1="X"
	g2="hyphen,uni00AD,endash,emdash"
	k="40" />
    <hkern g1="X"
	g2="guillemotright,guilsinglright"
	k="30" />
    <hkern g1="X"
	g2="quotedbl,quotesingle"
	k="-10" />
    <hkern g1="X"
	g2="quoteleft,quotedblleft"
	k="-10" />
    <hkern g1="X"
	g2="z,zcaron"
	k="-5" />
    <hkern g1="X"
	g2="W"
	k="-10" />
    <hkern g1="X"
	g2="guillemotright.case,guilsinglright.case"
	k="10" />
    <hkern g1="X"
	g2="Oslash"
	k="15" />
    <hkern g1="X"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="15" />
    <hkern g1="a.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="ampersand"
	g2="Y,Yacute,Ydieresis"
	k="75" />
    <hkern g1="ampersand"
	g2="T"
	k="70" />
    <hkern g1="ampersand"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="10" />
    <hkern g1="ampersand"
	g2="W"
	k="35" />
    <hkern g1="ampersand.ss04"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="10" />
    <hkern g1="ampersand.ss04"
	g2="AE"
	k="30" />
    <hkern g1="ampersand.ss04"
	g2="Y,Yacute,Ydieresis"
	k="40" />
    <hkern g1="ampersand.ss04"
	g2="zero,six"
	k="-20" />
    <hkern g1="ampersand.ss04"
	g2="T"
	k="50" />
    <hkern g1="ampersand.ss04"
	g2="Z,Zcaron"
	k="20" />
    <hkern g1="ampersand.ss04"
	g2="W"
	k="10" />
    <hkern g1="ampersand.ss04"
	g2="Oslash"
	k="-10" />
    <hkern g1="approxequal"
	g2="zero,six"
	k="-20" />
    <hkern g1="asciicircum"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="20" />
    <hkern g1="asciicircum"
	g2="AE"
	k="100" />
    <hkern g1="asciicircum"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="-10" />
    <hkern g1="asciitilde"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="20" />
    <hkern g1="asciitilde"
	g2="AE"
	k="60" />
    <hkern g1="asciitilde"
	g2="Y,Yacute,Ydieresis"
	k="60" />
    <hkern g1="asciitilde"
	g2="zero,six"
	k="-10" />
    <hkern g1="asciitilde"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="-10" />
    <hkern g1="asciitilde"
	g2="T"
	k="70" />
    <hkern g1="asciitilde"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="5" />
    <hkern g1="asciitilde"
	g2="W"
	k="20" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="110" />
    <hkern g1="asterisk"
	g2="AE"
	k="190" />
    <hkern g1="asterisk"
	g2="J"
	k="50" />
    <hkern g1="asterisk"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="30" />
    <hkern g1="asterisk"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="40" />
    <hkern g1="asterisk"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="40" />
    <hkern g1="asterisk"
	g2="s,scaron"
	k="10" />
    <hkern g1="asterisk"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="5" />
    <hkern g1="asterisk"
	g2="T"
	k="-20" />
    <hkern g1="asterisk"
	g2="W"
	k="-10" />
    <hkern g1="at"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="30" />
    <hkern g1="at"
	g2="AE"
	k="70" />
    <hkern g1="at"
	g2="J"
	k="10" />
    <hkern g1="at"
	g2="Y,Yacute,Ydieresis"
	k="40" />
    <hkern g1="at"
	g2="w"
	k="-15" />
    <hkern g1="at"
	g2="y,yacute,ydieresis"
	k="-15" />
    <hkern g1="at"
	g2="Eth"
	k="-10" />
    <hkern g1="at"
	g2="T"
	k="25" />
    <hkern g1="at"
	g2="Z,Zcaron"
	k="20" />
    <hkern g1="at"
	g2="z,zcaron"
	k="-5" />
    <hkern g1="at"
	g2="W"
	k="20" />
    <hkern g1="b.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="backslash"
	g2="Y,Yacute,Ydieresis"
	k="65" />
    <hkern g1="backslash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="-10" />
    <hkern g1="backslash"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="10" />
    <hkern g1="backslash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="15" />
    <hkern g1="backslash"
	g2="w"
	k="10" />
    <hkern g1="backslash"
	g2="y,yacute,ydieresis"
	k="10" />
    <hkern g1="backslash"
	g2="zero,six"
	k="40" />
    <hkern g1="backslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="35" />
    <hkern g1="backslash"
	g2="T"
	k="50" />
    <hkern g1="backslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="20" />
    <hkern g1="backslash"
	g2="W"
	k="60" />
    <hkern g1="backslash"
	g2="Oslash"
	k="20" />
    <hkern g1="backslash"
	g2="S,Scaron"
	k="5" />
    <hkern g1="bar"
	g2="Y,Yacute,Ydieresis"
	k="-10" />
    <hkern g1="bar"
	g2="w"
	k="-20" />
    <hkern g1="bar"
	g2="y,yacute,ydieresis"
	k="-15" />
    <hkern g1="bar"
	g2="T"
	k="-20" />
    <hkern g1="bar"
	g2="z,zcaron"
	k="-10" />
    <hkern g1="bar"
	g2="W"
	k="-20" />
    <hkern g1="braceright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="10" />
    <hkern g1="braceright"
	g2="AE"
	k="40" />
    <hkern g1="braceright"
	g2="Y,Yacute,Ydieresis"
	k="40" />
    <hkern g1="braceright"
	g2="T"
	k="20" />
    <hkern g1="braceright"
	g2="Z,Zcaron"
	k="20" />
    <hkern g1="braceright"
	g2="W"
	k="10" />
    <hkern g1="braceright.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="10" />
    <hkern g1="braceright.case"
	g2="AE"
	k="10" />
    <hkern g1="braceright.case"
	g2="Y,Yacute,Ydieresis"
	k="10" />
    <hkern g1="braceright.case"
	g2="T"
	k="10" />
    <hkern g1="braceright.case"
	g2="S,Scaron"
	k="10" />
    <hkern g1="brokenbar"
	g2="Y,Yacute,Ydieresis"
	k="-10" />
    <hkern g1="brokenbar"
	g2="w"
	k="-20" />
    <hkern g1="brokenbar"
	g2="y,yacute,ydieresis"
	k="-10" />
    <hkern g1="brokenbar"
	g2="T"
	k="-20" />
    <hkern g1="brokenbar"
	g2="W"
	k="-20" />
    <hkern g1="bullet"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="30" />
    <hkern g1="bullet"
	g2="AE"
	k="50" />
    <hkern g1="bullet"
	g2="J"
	k="10" />
    <hkern g1="bullet"
	g2="Y,Yacute,Ydieresis"
	k="70" />
    <hkern g1="bullet"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="10" />
    <hkern g1="bullet"
	g2="T"
	k="70" />
    <hkern g1="bullet"
	g2="Z,Zcaron"
	k="20" />
    <hkern g1="bullet"
	g2="W"
	k="40" />
    <hkern g1="bullet"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn"
	k="10" />
    <hkern g1="c.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="cent"
	g2="zero,six"
	k="10" />
    <hkern g1="d.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="dagger"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="30" />
    <hkern g1="dagger"
	g2="w"
	k="-40" />
    <hkern g1="dagger"
	g2="y,yacute,ydieresis"
	k="-20" />
    <hkern g1="daggerdbl"
	g2="w"
	k="-40" />
    <hkern g1="daggerdbl"
	g2="y,yacute,ydieresis"
	k="-20" />
    <hkern g1="divide"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="10" />
    <hkern g1="divide"
	g2="Y,Yacute,Ydieresis"
	k="50" />
    <hkern g1="divide"
	g2="T"
	k="60" />
    <hkern g1="divide"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="5" />
    <hkern g1="divide"
	g2="W"
	k="20" />
    <hkern g1="uni2215"
	g2="zero,six"
	k="20" />
    <hkern g1="dollar"
	g2="zero,six"
	k="10" />
    <hkern g1="e.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="eight"
	g2="hyphen.case,endash.case,emdash.case"
	k="10" />
    <hkern g1="eight"
	g2="comma,period,ellipsis"
	k="10" />
    <hkern g1="eight"
	g2="zero,six"
	k="5" />
    <hkern g1="equal"
	g2="Y,Yacute,Ydieresis"
	k="30" />
    <hkern g1="equal"
	g2="zero,six"
	k="-10" />
    <hkern g1="equal"
	g2="T"
	k="30" />
    <hkern g1="equal"
	g2="W"
	k="10" />
    <hkern g1="exclamdown"
	g2="Y,Yacute,Ydieresis"
	k="50" />
    <hkern g1="exclamdown"
	g2="T"
	k="80" />
    <hkern g1="exclamdown"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="10" />
    <hkern g1="exclamdown"
	g2="j"
	k="-30" />
    <hkern g1="exclamdown"
	g2="W"
	k="10" />
    <hkern g1="exclamdown.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="10" />
    <hkern g1="f.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="five"
	g2="comma,period,ellipsis"
	k="10" />
    <hkern g1="five"
	g2="quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="five"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="florin"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="60" />
    <hkern g1="florin"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="70" />
    <hkern g1="florin"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="florin"
	g2="m,n,p,r,ntilde,r.ss03"
	k="40" />
    <hkern g1="florin"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="60" />
    <hkern g1="florin"
	g2="oslash"
	k="50" />
    <hkern g1="florin"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="florin"
	g2="quoteright,quotedblright"
	k="-40" />
    <hkern g1="florin"
	g2="s,scaron"
	k="30" />
    <hkern g1="florin"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="40" />
    <hkern g1="florin"
	g2="w"
	k="10" />
    <hkern g1="florin"
	g2="bracketright,braceright"
	k="-20" />
    <hkern g1="florin"
	g2="hyphen,uni00AD,endash,emdash"
	k="30" />
    <hkern g1="florin"
	g2="quotedbl,quotesingle"
	k="-40" />
    <hkern g1="florin"
	g2="quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="florin"
	g2="quoteleft,quotedblleft"
	k="-40" />
    <hkern g1="florin"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="florin.tf"
	g2="zero,six"
	k="40" />
    <hkern g1="four"
	g2="hyphen.case,endash.case,emdash.case"
	k="10" />
    <hkern g1="four"
	g2="quoteright,quotedblright"
	k="20" />
    <hkern g1="four"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="g.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="germandbls"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="5" />
    <hkern g1="germandbls"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="5" />
    <hkern g1="germandbls"
	g2="y,yacute,ydieresis"
	k="5" />
    <hkern g1="germandbls"
	g2="bracketright,braceright"
	k="-20" />
    <hkern g1="greater"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="20" />
    <hkern g1="greater"
	g2="Y,Yacute,Ydieresis"
	k="60" />
    <hkern g1="greater"
	g2="T"
	k="40" />
    <hkern g1="greater"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="5" />
    <hkern g1="greater"
	g2="Z,Zcaron"
	k="15" />
    <hkern g1="greater"
	g2="W"
	k="20" />
    <hkern g1="greaterequal"
	g2="zero,six"
	k="-10" />
    <hkern g1="h.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="i.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="infinity"
	g2="zero,six"
	k="-10" />
    <hkern g1="j.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="k.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="l.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="less"
	g2="zero,six"
	k="-20" />
    <hkern g1="less"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="-10" />
    <hkern g1="less"
	g2="T"
	k="10" />
    <hkern g1="less"
	g2="Z,Zcaron"
	k="-10" />
    <hkern g1="less"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn"
	k="-10" />
    <hkern g1="lessequal"
	g2="zero,six"
	k="-10" />
    <hkern g1="lslash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="-10" />
    <hkern g1="lslash"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="-25" />
    <hkern g1="lslash"
	g2="guillemotleft,guilsinglleft"
	k="-5" />
    <hkern g1="lslash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="-10" />
    <hkern g1="lslash"
	g2="oslash"
	k="-5" />
    <hkern g1="lslash"
	g2="s,scaron"
	k="-10" />
    <hkern g1="lslash"
	g2="w"
	k="-25" />
    <hkern g1="lslash"
	g2="y,yacute,ydieresis"
	k="-20" />
    <hkern g1="lslash"
	g2="z,zcaron"
	k="-20" />
    <hkern g1="lslash"
	g2="t"
	k="-20" />
    <hkern g1="lslash"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="-20" />
    <hkern g1="m.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="minus"
	g2="zero,six"
	k="-10" />
    <hkern g1="minus"
	g2="bracketright,braceright"
	k="40" />
    <hkern g1="multiply"
	g2="Y,Yacute,Ydieresis"
	k="30" />
    <hkern g1="multiply"
	g2="T"
	k="40" />
    <hkern g1="n.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="notequal"
	g2="zero,six"
	k="-10" />
    <hkern g1="numbersign"
	g2="zero,six"
	k="20" />
    <hkern g1="o.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="one"
	g2="comma,period,ellipsis"
	k="10" />
    <hkern g1="one"
	g2="quoteright,quotedblright"
	k="10" />
    <hkern g1="one"
	g2="zero,six"
	k="-5" />
    <hkern g1="one"
	g2="bracketright,braceright"
	k="-10" />
    <hkern g1="one"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="one"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="10" />
    <hkern g1="one.ss05"
	g2="hyphen.case,endash.case,emdash.case"
	k="30" />
    <hkern g1="one.ss05"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="one.ss05"
	g2="guillemotleft.case,guilsinglleft.case"
	k="25" />
    <hkern g1="one.ss05"
	g2="comma,period,ellipsis"
	k="-30" />
    <hkern g1="one.ss05"
	g2="quoteright,quotedblright"
	k="30" />
    <hkern g1="one.ss05"
	g2="bracketright,braceright"
	k="-25" />
    <hkern g1="one.ss05"
	g2="bracketright.case,braceright.case"
	k="-15" />
    <hkern g1="one.ss05"
	g2="guillemotright,guilsinglright"
	k="-10" />
    <hkern g1="one.ss05"
	g2="quotesinglbase,quotedblbase"
	k="-20" />
    <hkern g1="one.ss05"
	g2="quoteleft,quotedblleft"
	k="5" />
    <hkern g1="one.ss05"
	g2="guillemotright.case,guilsinglright.case"
	k="-10" />
    <hkern g1="onesuperior"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="100" />
    <hkern g1="onesuperior"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="onesuperior"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="ordmasculine"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="30" />
    <hkern g1="p.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="paragraph"
	g2="T"
	k="-30" />
    <hkern g1="parenleft"
	g2="J"
	k="15" />
    <hkern g1="parenleft"
	g2="Y,Yacute,Ydieresis"
	k="-20" />
    <hkern g1="parenleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="5" />
    <hkern g1="parenleft"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="20" />
    <hkern g1="parenleft"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="10" />
    <hkern g1="parenleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="20" />
    <hkern g1="parenleft"
	g2="oslash"
	k="10" />
    <hkern g1="parenleft"
	g2="w"
	k="20" />
    <hkern g1="parenleft"
	g2="zero,six"
	k="20" />
    <hkern g1="parenleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="parenleft"
	g2="T"
	k="-20" />
    <hkern g1="parenleft"
	g2="hyphen,uni00AD,endash,emdash"
	k="40" />
    <hkern g1="parenleft"
	g2="j"
	k="-50" />
    <hkern g1="parenleft"
	g2="W"
	k="-20" />
    <hkern g1="parenleft"
	g2="Oslash"
	k="20" />
    <hkern g1="parenleft.case"
	g2="J"
	k="5" />
    <hkern g1="parenleft.case"
	g2="Y,Yacute,Ydieresis"
	k="-20" />
    <hkern g1="parenleft.case"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="5" />
    <hkern g1="parenleft.case"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="10" />
    <hkern g1="parenleft.case"
	g2="zero,six"
	k="20" />
    <hkern g1="parenleft.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="parenleft.case"
	g2="T"
	k="-20" />
    <hkern g1="parenleft.case"
	g2="j"
	k="-10" />
    <hkern g1="parenleft.case"
	g2="W"
	k="-20" />
    <hkern g1="parenleft.case"
	g2="Oslash"
	k="20" />
    <hkern g1="parenright.case"
	g2="AE"
	k="30" />
    <hkern g1="percent"
	g2="zero,six"
	k="20" />
    <hkern g1="periodcentered"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="20" />
    <hkern g1="periodcentered"
	g2="AE"
	k="20" />
    <hkern g1="periodcentered"
	g2="Y,Yacute,Ydieresis"
	k="60" />
    <hkern g1="periodcentered"
	g2="T"
	k="50" />
    <hkern g1="periodcentered"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="10" />
    <hkern g1="periodcentered"
	g2="Z,Zcaron"
	k="5" />
    <hkern g1="periodcentered"
	g2="W"
	k="30" />
    <hkern g1="plus"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="15" />
    <hkern g1="plus"
	g2="Y,Yacute,Ydieresis"
	k="50" />
    <hkern g1="plus"
	g2="T"
	k="50" />
    <hkern g1="plus"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="5" />
    <hkern g1="plus"
	g2="Z,Zcaron"
	k="20" />
    <hkern g1="plus"
	g2="bracketright,braceright"
	k="40" />
    <hkern g1="plus"
	g2="W"
	k="30" />
    <hkern g1="q.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="question"
	g2="AE"
	k="70" />
    <hkern g1="question"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="20" />
    <hkern g1="question"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="5" />
    <hkern g1="questiondown"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="-20" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ydieresis"
	k="120" />
    <hkern g1="questiondown"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="20" />
    <hkern g1="questiondown"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="30" />
    <hkern g1="questiondown"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="10" />
    <hkern g1="questiondown"
	g2="m,n,p,r,ntilde,r.ss03"
	k="10" />
    <hkern g1="questiondown"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="20" />
    <hkern g1="questiondown"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="10" />
    <hkern g1="questiondown"
	g2="w"
	k="30" />
    <hkern g1="questiondown"
	g2="y,yacute,ydieresis"
	k="10" />
    <hkern g1="questiondown"
	g2="zero,six"
	k="20" />
    <hkern g1="questiondown"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="40" />
    <hkern g1="questiondown"
	g2="T"
	k="120" />
    <hkern g1="questiondown"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="30" />
    <hkern g1="questiondown"
	g2="j"
	k="-40" />
    <hkern g1="questiondown"
	g2="b,h,k,l,thorn"
	k="10" />
    <hkern g1="questiondown"
	g2="quotesinglbase,quotedblbase"
	k="-40" />
    <hkern g1="questiondown"
	g2="quoteleft,quotedblleft"
	k="30" />
    <hkern g1="questiondown"
	g2="W"
	k="60" />
    <hkern g1="questiondown"
	g2="t"
	k="10" />
    <hkern g1="questiondown"
	g2="S,Scaron"
	k="10" />
    <hkern g1="questiondown.case"
	g2="Y,Yacute,Ydieresis"
	k="55" />
    <hkern g1="questiondown.case"
	g2="T"
	k="30" />
    <hkern g1="questiondown.case"
	g2="W"
	k="20" />
    <hkern g1="r.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="radical"
	g2="zero,six"
	k="100" />
    <hkern g1="registered.ss06"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="90" />
    <hkern g1="s.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="seven"
	g2="hyphen.case,endash.case,emdash.case"
	k="60" />
    <hkern g1="seven"
	g2="guillemotleft,guilsinglleft"
	k="60" />
    <hkern g1="seven"
	g2="guillemotleft.case,guilsinglleft.case"
	k="30" />
    <hkern g1="seven"
	g2="comma,period,ellipsis"
	k="110" />
    <hkern g1="seven"
	g2="quoteright,quotedblright"
	k="-20" />
    <hkern g1="seven"
	g2="zero,six"
	k="25" />
    <hkern g1="seven"
	g2="hyphen,uni00AD,endash,emdash"
	k="50" />
    <hkern g1="seven"
	g2="guillemotright,guilsinglright"
	k="20" />
    <hkern g1="seven"
	g2="quotedbl,quotesingle"
	k="-20" />
    <hkern g1="seven"
	g2="quotesinglbase,quotedblbase"
	k="70" />
    <hkern g1="seven"
	g2="quoteleft,quotedblleft"
	k="-30" />
    <hkern g1="seven"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="10" />
    <hkern g1="seven"
	g2="colon,semicolon"
	k="30" />
    <hkern g1="six"
	g2="comma,period,ellipsis"
	k="10" />
    <hkern g1="six"
	g2="quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="six"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="10" />
    <hkern g1="slash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="70" />
    <hkern g1="slash"
	g2="AE"
	k="140" />
    <hkern g1="slash"
	g2="J"
	k="120" />
    <hkern g1="slash"
	g2="Y,Yacute,Ydieresis"
	k="-10" />
    <hkern g1="slash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="115" />
    <hkern g1="slash"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="50" />
    <hkern g1="slash"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="10" />
    <hkern g1="slash"
	g2="m,n,p,r,ntilde,r.ss03"
	k="60" />
    <hkern g1="slash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="120" />
    <hkern g1="slash"
	g2="oslash"
	k="80" />
    <hkern g1="slash"
	g2="s,scaron"
	k="80" />
    <hkern g1="slash"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="45" />
    <hkern g1="slash"
	g2="w"
	k="20" />
    <hkern g1="slash"
	g2="y,yacute,ydieresis"
	k="10" />
    <hkern g1="slash"
	g2="zero,six"
	k="40" />
    <hkern g1="slash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="35" />
    <hkern g1="slash"
	g2="T"
	k="-40" />
    <hkern g1="slash"
	g2="z,zcaron"
	k="60" />
    <hkern g1="slash"
	g2="W"
	k="-20" />
    <hkern g1="slash"
	g2="Oslash"
	k="35" />
    <hkern g1="slash"
	g2="S,Scaron"
	k="10" />
    <hkern g1="space"
	g2="hyphen,uni00AD,endash,emdash"
	k="50" />
    <hkern g1="sterling"
	g2="zero,six"
	k="10" />
    <hkern g1="t.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="three"
	g2="guillemotleft.case,guilsinglleft.case"
	k="10" />
    <hkern g1="three"
	g2="comma,period,ellipsis"
	k="10" />
    <hkern g1="three"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="three"
	g2="hyphen,uni00AD,endash,emdash"
	k="10" />
    <hkern g1="three"
	g2="guillemotright.case,guilsinglright.case"
	k="-10" />
    <hkern g1="threesuperior"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="100" />
    <hkern g1="threesuperior"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="threesuperior"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="30" />
    <hkern g1="trademark"
	g2="T"
	k="-30" />
    <hkern g1="two"
	g2="hyphen.case,endash.case,emdash.case"
	k="5" />
    <hkern g1="two"
	g2="guillemotleft.case,guilsinglleft.case"
	k="5" />
    <hkern g1="two"
	g2="guillemotright,guilsinglright"
	k="5" />
    <hkern g1="twosuperior"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="90" />
    <hkern g1="twosuperior"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="twosuperior"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="u.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="underscore"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="-30" />
    <hkern g1="underscore"
	g2="AE"
	k="-20" />
    <hkern g1="underscore"
	g2="Y,Yacute,Ydieresis"
	k="60" />
    <hkern g1="underscore"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="20" />
    <hkern g1="underscore"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="10" />
    <hkern g1="underscore"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="20" />
    <hkern g1="underscore"
	g2="oslash"
	k="10" />
    <hkern g1="underscore"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="15" />
    <hkern g1="underscore"
	g2="w"
	k="40" />
    <hkern g1="underscore"
	g2="y,yacute,ydieresis"
	k="40" />
    <hkern g1="underscore"
	g2="zero,six"
	k="20" />
    <hkern g1="underscore"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="40" />
    <hkern g1="underscore"
	g2="T"
	k="40" />
    <hkern g1="underscore"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="15" />
    <hkern g1="underscore"
	g2="Z,Zcaron"
	k="-10" />
    <hkern g1="underscore"
	g2="j"
	k="-50" />
    <hkern g1="underscore"
	g2="W"
	k="45" />
    <hkern g1="underscore"
	g2="t"
	k="15" />
    <hkern g1="underscore"
	g2="Oslash"
	k="30" />
    <hkern g1="underscore"
	g2="S,Scaron"
	k="10" />
    <hkern g1="uni2070"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="120" />
    <hkern g1="uni2070"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="uni2070"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="uni2074"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="100" />
    <hkern g1="uni2074"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="uni2074"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="uni2075"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="90" />
    <hkern g1="uni2075"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="uni2075"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="uni2076"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="90" />
    <hkern g1="uni2076"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="uni2076"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="uni2077"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="140" />
    <hkern g1="uni2077"
	g2="m,n,p,r,ntilde,r.ss03"
	k="80" />
    <hkern g1="uni2077"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="100" />
    <hkern g1="uni2077"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="uni2077"
	g2="zero,six"
	k="60" />
    <hkern g1="uni2077"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="70" />
    <hkern g1="uni2078"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="100" />
    <hkern g1="uni2078"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="uni2078"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="30" />
    <hkern g1="uni2079"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="110" />
    <hkern g1="uni2079"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="uni2079"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="30" />
    <hkern g1="uni2080"
	g2="Y,Yacute,Ydieresis"
	k="120" />
    <hkern g1="uni2080"
	g2="w"
	k="30" />
    <hkern g1="uni2080"
	g2="y,yacute,ydieresis"
	k="40" />
    <hkern g1="uni2080"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="30" />
    <hkern g1="uni2080"
	g2="T"
	k="100" />
    <hkern g1="uni2080"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="20" />
    <hkern g1="uni2080"
	g2="W"
	k="70" />
    <hkern g1="uni2081"
	g2="Y,Yacute,Ydieresis"
	k="120" />
    <hkern g1="uni2081"
	g2="w"
	k="30" />
    <hkern g1="uni2081"
	g2="y,yacute,ydieresis"
	k="30" />
    <hkern g1="uni2081"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="10" />
    <hkern g1="uni2081"
	g2="T"
	k="100" />
    <hkern g1="uni2081"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="10" />
    <hkern g1="uni2081"
	g2="W"
	k="40" />
    <hkern g1="uni2082"
	g2="Y,Yacute,Ydieresis"
	k="120" />
    <hkern g1="uni2082"
	g2="w"
	k="30" />
    <hkern g1="uni2082"
	g2="y,yacute,ydieresis"
	k="30" />
    <hkern g1="uni2082"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="uni2082"
	g2="T"
	k="100" />
    <hkern g1="uni2082"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="10" />
    <hkern g1="uni2082"
	g2="W"
	k="70" />
    <hkern g1="uni2083"
	g2="Y,Yacute,Ydieresis"
	k="100" />
    <hkern g1="uni2083"
	g2="w"
	k="30" />
    <hkern g1="uni2083"
	g2="y,yacute,ydieresis"
	k="30" />
    <hkern g1="uni2083"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="10" />
    <hkern g1="uni2083"
	g2="T"
	k="100" />
    <hkern g1="uni2083"
	g2="W"
	k="70" />
    <hkern g1="uni2084"
	g2="Y,Yacute,Ydieresis"
	k="120" />
    <hkern g1="uni2084"
	g2="w"
	k="40" />
    <hkern g1="uni2084"
	g2="y,yacute,ydieresis"
	k="30" />
    <hkern g1="uni2084"
	g2="zero,six"
	k="50" />
    <hkern g1="uni2084"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="40" />
    <hkern g1="uni2084"
	g2="T"
	k="120" />
    <hkern g1="uni2084"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="20" />
    <hkern g1="uni2084"
	g2="W"
	k="80" />
    <hkern g1="uni2085"
	g2="Y,Yacute,Ydieresis"
	k="120" />
    <hkern g1="uni2085"
	g2="w"
	k="40" />
    <hkern g1="uni2085"
	g2="y,yacute,ydieresis"
	k="30" />
    <hkern g1="uni2085"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="uni2085"
	g2="T"
	k="100" />
    <hkern g1="uni2085"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="10" />
    <hkern g1="uni2085"
	g2="W"
	k="70" />
    <hkern g1="uni2085"
	g2="S,Scaron"
	k="10" />
    <hkern g1="uni2085"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn"
	k="10" />
    <hkern g1="uni2086"
	g2="Y,Yacute,Ydieresis"
	k="120" />
    <hkern g1="uni2086"
	g2="w"
	k="30" />
    <hkern g1="uni2086"
	g2="y,yacute,ydieresis"
	k="30" />
    <hkern g1="uni2086"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="30" />
    <hkern g1="uni2086"
	g2="T"
	k="100" />
    <hkern g1="uni2086"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="10" />
    <hkern g1="uni2086"
	g2="W"
	k="80" />
    <hkern g1="uni2086"
	g2="S,Scaron"
	k="10" />
    <hkern g1="uni2086"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn"
	k="10" />
    <hkern g1="uni2087"
	g2="Y,Yacute,Ydieresis"
	k="100" />
    <hkern g1="uni2087"
	g2="w"
	k="30" />
    <hkern g1="uni2087"
	g2="y,yacute,ydieresis"
	k="30" />
    <hkern g1="uni2087"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="uni2087"
	g2="T"
	k="100" />
    <hkern g1="uni2087"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="10" />
    <hkern g1="uni2087"
	g2="W"
	k="60" />
    <hkern g1="uni2087"
	g2="S,Scaron"
	k="10" />
    <hkern g1="uni2087"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn"
	k="10" />
    <hkern g1="uni2088"
	g2="Y,Yacute,Ydieresis"
	k="100" />
    <hkern g1="uni2088"
	g2="w"
	k="40" />
    <hkern g1="uni2088"
	g2="y,yacute,ydieresis"
	k="30" />
    <hkern g1="uni2088"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="30" />
    <hkern g1="uni2088"
	g2="T"
	k="100" />
    <hkern g1="uni2088"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="10" />
    <hkern g1="uni2088"
	g2="W"
	k="70" />
    <hkern g1="uni2088"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn"
	k="10" />
    <hkern g1="uni2089"
	g2="Y,Yacute,Ydieresis"
	k="100" />
    <hkern g1="uni2089"
	g2="w"
	k="30" />
    <hkern g1="uni2089"
	g2="y,yacute,ydieresis"
	k="30" />
    <hkern g1="uni2089"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="uni2089"
	g2="T"
	k="100" />
    <hkern g1="uni2089"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="10" />
    <hkern g1="uni2089"
	g2="W"
	k="70" />
    <hkern g1="uni2089"
	g2="S,Scaron"
	k="10" />
    <hkern g1="uni2089"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn"
	k="10" />
    <hkern g1="v"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="5" />
    <hkern g1="v"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="15" />
    <hkern g1="v"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="-15" />
    <hkern g1="v"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="15" />
    <hkern g1="v"
	g2="oslash"
	k="10" />
    <hkern g1="v"
	g2="comma,period,ellipsis"
	k="90" />
    <hkern g1="v"
	g2="w"
	k="-5" />
    <hkern g1="v"
	g2="y,yacute,ydieresis"
	k="-10" />
    <hkern g1="v"
	g2="hyphen,uni00AD,endash,emdash"
	k="5" />
    <hkern g1="v"
	g2="guillemotright,guilsinglright"
	k="-20" />
    <hkern g1="v"
	g2="quotedbl,quotesingle"
	k="-10" />
    <hkern g1="v"
	g2="quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="v"
	g2="z,zcaron"
	k="-10" />
    <hkern g1="v"
	g2="t"
	k="-15" />
    <hkern g1="v.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="w.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="x"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="10" />
    <hkern g1="x"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="-15" />
    <hkern g1="x"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="x"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="10" />
    <hkern g1="x"
	g2="oslash"
	k="5" />
    <hkern g1="x"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="5" />
    <hkern g1="x"
	g2="w"
	k="-5" />
    <hkern g1="x"
	g2="y,yacute,ydieresis"
	k="-5" />
    <hkern g1="x"
	g2="hyphen,uni00AD,endash,emdash"
	k="10" />
    <hkern g1="x"
	g2="quotedbl,quotesingle"
	k="-10" />
    <hkern g1="x"
	g2="quotesinglbase,quotedblbase"
	k="-10" />
    <hkern g1="x"
	g2="z,zcaron"
	k="-20" />
    <hkern g1="x"
	g2="t"
	k="-5" />
    <hkern g1="x.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="y.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="z.ordn"
	g2="comma,period,ellipsis"
	k="80" />
    <hkern g1="bracketleft,braceleft"
	g2="V"
	k="-20" />
    <hkern g1="bracketleft,braceleft"
	g2="X"
	k="-20" />
    <hkern g1="bracketleft,braceleft"
	g2="five"
	k="20" />
    <hkern g1="bracketleft,braceleft"
	g2="florin"
	k="-60" />
    <hkern g1="bracketleft,braceleft"
	g2="four"
	k="30" />
    <hkern g1="bracketleft,braceleft"
	g2="minus"
	k="40" />
    <hkern g1="bracketleft,braceleft"
	g2="nine"
	k="-10" />
    <hkern g1="bracketleft,braceleft"
	g2="one.ss05"
	k="10" />
    <hkern g1="bracketleft,braceleft"
	g2="plus"
	k="40" />
    <hkern g1="bracketleft,braceleft"
	g2="seven"
	k="-50" />
    <hkern g1="bracketleft,braceleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="bracketleft,braceleft"
	g2="Oslash"
	k="20" />
    <hkern g1="bracketleft,braceleft"
	g2="T"
	k="-20" />
    <hkern g1="bracketleft,braceleft"
	g2="W"
	k="-20" />
    <hkern g1="bracketleft,braceleft"
	g2="Y,Yacute,Ydieresis"
	k="-40" />
    <hkern g1="bracketleft,braceleft"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="20" />
    <hkern g1="bracketleft,braceleft"
	g2="hyphen,uni00AD,endash,emdash"
	k="20" />
    <hkern g1="bracketleft,braceleft"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="10" />
    <hkern g1="bracketleft,braceleft"
	g2="j"
	k="-50" />
    <hkern g1="bracketleft,braceleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="10" />
    <hkern g1="bracketleft,braceleft"
	g2="oslash"
	k="10" />
    <hkern g1="bracketleft,braceleft"
	g2="w"
	k="20" />
    <hkern g1="bracketleft.case,braceleft.case"
	g2="V"
	k="-20" />
    <hkern g1="bracketleft.case,braceleft.case"
	g2="X"
	k="-20" />
    <hkern g1="bracketleft.case,braceleft.case"
	g2="five"
	k="20" />
    <hkern g1="bracketleft.case,braceleft.case"
	g2="four"
	k="30" />
    <hkern g1="bracketleft.case,braceleft.case"
	g2="nine"
	k="-20" />
    <hkern g1="bracketleft.case,braceleft.case"
	g2="seven"
	k="-30" />
    <hkern g1="bracketleft.case,braceleft.case"
	g2="two"
	k="-10" />
    <hkern g1="bracketleft.case,braceleft.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="bracketleft.case,braceleft.case"
	g2="Oslash"
	k="20" />
    <hkern g1="bracketleft.case,braceleft.case"
	g2="T"
	k="-20" />
    <hkern g1="bracketleft.case,braceleft.case"
	g2="W"
	k="-20" />
    <hkern g1="bracketleft.case,braceleft.case"
	g2="Y,Yacute,Ydieresis"
	k="-20" />
    <hkern g1="bracketleft.case,braceleft.case"
	g2="zero,six"
	k="30" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="V"
	k="10" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="X"
	k="15" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="five"
	k="10" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="four"
	k="10" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="one.ss05"
	k="40" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="Lslash"
	k="-10" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="one"
	k="10" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="three"
	k="30" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="-10" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="T"
	k="10" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="Y,Yacute,Ydieresis"
	k="25" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="20" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="w"
	k="-10" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="25" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="AE"
	k="80" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="Eth"
	k="-10" />
    <hkern g1="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	g2="z,zcaron"
	k="5" />
    <hkern g1="colon,semicolon"
	g2="V"
	k="40" />
    <hkern g1="colon,semicolon"
	g2="nine"
	k="-15" />
    <hkern g1="colon,semicolon"
	g2="one.ss05"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="seven"
	k="-10" />
    <hkern g1="colon,semicolon"
	g2="T"
	k="60" />
    <hkern g1="colon,semicolon"
	g2="W"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ydieresis"
	k="60" />
    <hkern g1="colon,semicolon"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="5" />
    <hkern g1="colon,semicolon"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="5" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="V"
	k="55" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="X"
	k="40" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="five"
	k="10" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="four"
	k="5" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="one.ss05"
	k="80" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="seven"
	k="30" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="Lslash"
	k="-50" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="one"
	k="20" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="three"
	k="20" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="lslash"
	k="-40" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="parenright"
	k="40" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="space"
	k="50" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="v"
	k="5" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="x"
	k="10" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="T"
	k="60" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="W"
	k="40" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="Y,Yacute,Ydieresis"
	k="80" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="j"
	k="10" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="w"
	k="5" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="20" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="AE"
	k="60" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="z,zcaron"
	k="5" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="5" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="S,Scaron"
	k="10" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="Z,Zcaron"
	k="45" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="bracketright,braceright"
	k="20" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="t"
	k="10" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="y,yacute,ydieresis"
	k="20" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="V"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="X"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="five"
	k="25" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="four"
	k="20" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="nine"
	k="20" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="one.ss05"
	k="70" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="seven"
	k="40" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="two"
	k="30" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="Lslash"
	k="-30" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="one"
	k="30" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="three"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="eight"
	k="10" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="T"
	k="60" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="W"
	k="40" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="Y,Yacute,Ydieresis"
	k="80" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="60" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="AE"
	k="130" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="S,Scaron"
	k="30" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="Z,Zcaron"
	k="60" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
	g2="J"
	k="50" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="V"
	k="40" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="X"
	k="30" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="one.ss05"
	k="20" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="two"
	k="-15" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="one"
	k="-20" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="v"
	k="-20" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T"
	k="80" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ydieresis"
	k="30" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="-10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="AE"
	k="50" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="t"
	k="-10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="J"
	k="-10" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="X"
	k="10" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="nine"
	k="-10" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="one.ss05"
	k="20" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="one"
	k="-10" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="10" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="Oslash"
	k="10" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="T"
	k="30" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="W"
	k="10" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="Y,Yacute,Ydieresis"
	k="10" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="10" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="AE"
	k="10" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="J"
	k="-10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="V"
	k="60" />
    <hkern g1="guillemotright,guilsinglright"
	g2="X"
	k="80" />
    <hkern g1="guillemotright,guilsinglright"
	g2="florin"
	k="30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="one.ss05"
	k="60" />
    <hkern g1="guillemotright,guilsinglright"
	g2="seven"
	k="30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="two"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="one"
	k="30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="three"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="lslash"
	k="-20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="v"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="x"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="5" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T"
	k="120" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W"
	k="30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ydieresis"
	k="80" />
    <hkern g1="guillemotright,guilsinglright"
	g2="w"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="AE"
	k="50" />
    <hkern g1="guillemotright,guilsinglright"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Z,Zcaron"
	k="30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn"
	k="10" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="V"
	k="40" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="X"
	k="30" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="four"
	k="20" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="one.ss05"
	k="60" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="seven"
	k="20" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="Lslash"
	k="-20" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="three"
	k="30" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="5" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="T"
	k="80" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="W"
	k="30" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="Y,Yacute,Ydieresis"
	k="60" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="30" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="AE"
	k="90" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="10" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="Z,Zcaron"
	k="40" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn"
	k="10" />
    <hkern g1="comma,period,ellipsis"
	g2="V"
	k="100" />
    <hkern g1="comma,period,ellipsis"
	g2="four"
	k="50" />
    <hkern g1="comma,period,ellipsis"
	g2="seven"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="one"
	k="70" />
    <hkern g1="comma,period,ellipsis"
	g2="v"
	k="90" />
    <hkern g1="comma,period,ellipsis"
	g2="eight"
	k="10" />
    <hkern g1="comma,period,ellipsis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="30" />
    <hkern g1="comma,period,ellipsis"
	g2="Oslash"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="T"
	k="100" />
    <hkern g1="comma,period,ellipsis"
	g2="W"
	k="100" />
    <hkern g1="comma,period,ellipsis"
	g2="Y,Yacute,Ydieresis"
	k="130" />
    <hkern g1="comma,period,ellipsis"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="15" />
    <hkern g1="comma,period,ellipsis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="15" />
    <hkern g1="comma,period,ellipsis"
	g2="w"
	k="40" />
    <hkern g1="comma,period,ellipsis"
	g2="zero,six"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="25" />
    <hkern g1="comma,period,ellipsis"
	g2="S,Scaron"
	k="10" />
    <hkern g1="comma,period,ellipsis"
	g2="t"
	k="20" />
    <hkern g1="comma,period,ellipsis"
	g2="y,yacute,ydieresis"
	k="90" />
    <hkern g1="comma,period,ellipsis"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="V"
	k="-20" />
    <hkern g1="quotedbl,quotesingle"
	g2="X"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="four"
	k="50" />
    <hkern g1="quotedbl,quotesingle"
	g2="one.ss05"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="seven"
	k="-20" />
    <hkern g1="quotedbl,quotesingle"
	g2="two"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="one"
	k="-30" />
    <hkern g1="quotedbl,quotesingle"
	g2="v"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="x"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="T"
	k="-30" />
    <hkern g1="quotedbl,quotesingle"
	g2="W"
	k="-20" />
    <hkern g1="quotedbl,quotesingle"
	g2="Y,Yacute,Ydieresis"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="-20" />
    <hkern g1="quotedbl,quotesingle"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="oslash"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="w"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="60" />
    <hkern g1="quotedbl,quotesingle"
	g2="AE"
	k="100" />
    <hkern g1="quotedbl,quotesingle"
	g2="y,yacute,ydieresis"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="J"
	k="40" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="V"
	k="80" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="florin"
	k="-20" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="four"
	k="30" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="seven"
	k="20" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="two"
	k="-10" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="one"
	k="40" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="three"
	k="20" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="v"
	k="40" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="x"
	k="-10" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="50" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="Oslash"
	k="10" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="T"
	k="80" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="W"
	k="80" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="Y,Yacute,Ydieresis"
	k="110" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="10" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="30" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="j"
	k="-10" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="10" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="w"
	k="40" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="zero,six"
	k="30" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="-30" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="40" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="S,Scaron"
	k="10" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="t"
	k="30" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="y,yacute,ydieresis"
	k="50" />
    <hkern g1="quoteleft,quotedblleft"
	g2="V"
	k="-20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="X"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="four"
	k="70" />
    <hkern g1="quoteleft,quotedblleft"
	g2="one.ss05"
	k="15" />
    <hkern g1="quoteleft,quotedblleft"
	g2="seven"
	k="-20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="one"
	k="-20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="T"
	k="-30" />
    <hkern g1="quoteleft,quotedblleft"
	g2="W"
	k="-20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Y,Yacute,Ydieresis"
	k="-20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="45" />
    <hkern g1="quoteleft,quotedblleft"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="40" />
    <hkern g1="quoteleft,quotedblleft"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="40" />
    <hkern g1="quoteleft,quotedblleft"
	g2="oslash"
	k="20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="zero,six"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="110" />
    <hkern g1="quoteleft,quotedblleft"
	g2="AE"
	k="180" />
    <hkern g1="quoteleft,quotedblleft"
	g2="J"
	k="95" />
    <hkern g1="quoteleft,quotedblleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="m,n,p,r,ntilde,r.ss03"
	k="20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="s,scaron"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="V"
	k="-20" />
    <hkern g1="quoteright,quotedblright"
	g2="X"
	k="-10" />
    <hkern g1="quoteright,quotedblright"
	g2="five"
	k="30" />
    <hkern g1="quoteright,quotedblright"
	g2="four"
	k="70" />
    <hkern g1="quoteright,quotedblright"
	g2="one.ss05"
	k="30" />
    <hkern g1="quoteright,quotedblright"
	g2="seven"
	k="-20" />
    <hkern g1="quoteright,quotedblright"
	g2="two"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="one"
	k="-10" />
    <hkern g1="quoteright,quotedblright"
	g2="lslash"
	k="30" />
    <hkern g1="quoteright,quotedblright"
	g2="eight"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="question"
	k="-20" />
    <hkern g1="quoteright,quotedblright"
	g2="questiondown"
	k="60" />
    <hkern g1="quoteright,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="T"
	k="-30" />
    <hkern g1="quoteright,quotedblright"
	g2="W"
	k="-20" />
    <hkern g1="quoteright,quotedblright"
	g2="Y,Yacute,Ydieresis"
	k="-20" />
    <hkern g1="quoteright,quotedblright"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="40" />
    <hkern g1="quoteright,quotedblright"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="60" />
    <hkern g1="quoteright,quotedblright"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="60" />
    <hkern g1="quoteright,quotedblright"
	g2="oslash"
	k="20" />
    <hkern g1="quoteright,quotedblright"
	g2="zero,six"
	k="20" />
    <hkern g1="quoteright,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="110" />
    <hkern g1="quoteright,quotedblright"
	g2="AE"
	k="130" />
    <hkern g1="quoteright,quotedblright"
	g2="J"
	k="60" />
    <hkern g1="quoteright,quotedblright"
	g2="m,n,p,r,ntilde,r.ss03"
	k="40" />
    <hkern g1="quoteright,quotedblright"
	g2="s,scaron"
	k="55" />
    <hkern g1="quoteright,quotedblright"
	g2="comma,period,ellipsis"
	k="70" />
    <hkern g1="zero,nine"
	g2="five"
	k="5" />
    <hkern g1="zero,nine"
	g2="minus"
	k="-10" />
    <hkern g1="zero,nine"
	g2="one.ss05"
	k="5" />
    <hkern g1="zero,nine"
	g2="one"
	k="5" />
    <hkern g1="zero,nine"
	g2="three"
	k="5" />
    <hkern g1="zero,nine"
	g2="parenright"
	k="20" />
    <hkern g1="zero,nine"
	g2="eight"
	k="5" />
    <hkern g1="zero,nine"
	g2="ampersand.ss04"
	k="20" />
    <hkern g1="zero,nine"
	g2="approxequal"
	k="-20" />
    <hkern g1="zero,nine"
	g2="asciitilde"
	k="-10" />
    <hkern g1="zero,nine"
	g2="backslash"
	k="40" />
    <hkern g1="zero,nine"
	g2="uni2215"
	k="20" />
    <hkern g1="zero,nine"
	g2="equal"
	k="-10" />
    <hkern g1="zero,nine"
	g2="florin.tf"
	k="70" />
    <hkern g1="zero,nine"
	g2="greaterequal"
	k="-10" />
    <hkern g1="zero,nine"
	g2="infinity"
	k="-10" />
    <hkern g1="zero,nine"
	g2="lessequal"
	k="-10" />
    <hkern g1="zero,nine"
	g2="notequal"
	k="-10" />
    <hkern g1="zero,nine"
	g2="numbersign"
	k="20" />
    <hkern g1="zero,nine"
	g2="parenright.case"
	k="20" />
    <hkern g1="zero,nine"
	g2="percent"
	k="20" />
    <hkern g1="zero,nine"
	g2="slash"
	k="40" />
    <hkern g1="zero,nine"
	g2="summation"
	k="20" />
    <hkern g1="zero,nine"
	g2="underscore"
	k="20" />
    <hkern g1="zero,nine"
	g2="uni2077"
	k="40" />
    <hkern g1="zero,nine"
	g2="uni2084"
	k="70" />
    <hkern g1="zero,nine"
	g2="zero,six"
	k="5" />
    <hkern g1="zero,nine"
	g2="comma,period,ellipsis"
	k="40" />
    <hkern g1="zero,nine"
	g2="bracketright.case,braceright.case"
	k="10" />
    <hkern g1="zero,nine"
	g2="quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="zero,nine"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="zero,nine"
	g2="quoteright,quotedblright"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="V"
	k="55" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="a.ordn"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="ampersand"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="ampersand.ss04"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="asciicircum"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="asciitilde"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="asterisk"
	k="110" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="at"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="b.ordn"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="backslash"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="braceleft"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="braceleft.case"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="bracketleft.case"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="bullet"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="c.ordn"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="d.ordn"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="dagger"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="divide"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="e.ordn"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="f.ordn"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="g.ordn"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="germandbls"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="h.ordn"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="i.ordn"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="j.ordn"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="k.ordn"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="l.ordn"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="less"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="m.ordn"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="n.ordn"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="o.ordn"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="onesuperior"
	k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="ordfeminine"
	k="110" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="ordmasculine"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="p.ordn"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="paragraph"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="parenleft"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="parenleft.case"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="periodcentered"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="plus"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="q.ordn"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="question"
	k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="questiondown"
	k="-30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="r.ordn"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="registered.ss06"
	k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="s.ordn"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="slash"
	k="-20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="t.ordn"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="threesuperior"
	k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="trademark"
	k="80" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="twosuperior"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="u.ordn"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="underscore"
	k="-30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="uni2070"
	k="120" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="uni2074"
	k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="uni2075"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="uni2076"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="uni2077"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="uni2078"
	k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="uni2079"
	k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="v"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="v.ordn"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="w.ordn"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="x.ordn"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="y.ordn"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="z.ordn"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="J"
	k="-10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="Oslash"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="S,Scaron"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="T"
	k="65" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="W"
	k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="Y,Yacute,Ydieresis"
	k="65" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="hyphen,uni00AD,endash,emdash"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="hyphen.case,endash.case,emdash.case"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="guillemotleft.case,guilsinglleft.case"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="guillemotright.case,guilsinglright.case"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="m,n,p,r,ntilde,r.ss03"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="oslash"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="quotesinglbase,quotedblbase"
	k="-10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="quoteleft,quotedblleft"
	k="110" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="quoteright,quotedblright"
	k="110" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="t"
	k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="w"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="y,yacute,ydieresis"
	k="25" />
    <hkern g1="C,Ccedilla"
	g2="V"
	k="10" />
    <hkern g1="C,Ccedilla"
	g2="ampersand"
	k="15" />
    <hkern g1="C,Ccedilla"
	g2="ampersand.ss04"
	k="15" />
    <hkern g1="C,Ccedilla"
	g2="asciicircum"
	k="10" />
    <hkern g1="C,Ccedilla"
	g2="at"
	k="30" />
    <hkern g1="C,Ccedilla"
	g2="backslash"
	k="25" />
    <hkern g1="C,Ccedilla"
	g2="braceleft"
	k="20" />
    <hkern g1="C,Ccedilla"
	g2="braceleft.case"
	k="20" />
    <hkern g1="C,Ccedilla"
	g2="germandbls"
	k="20" />
    <hkern g1="C,Ccedilla"
	g2="ordfeminine"
	k="20" />
    <hkern g1="C,Ccedilla"
	g2="ordmasculine"
	k="5" />
    <hkern g1="C,Ccedilla"
	g2="questiondown"
	k="20" />
    <hkern g1="C,Ccedilla"
	g2="slash"
	k="25" />
    <hkern g1="C,Ccedilla"
	g2="trademark"
	k="10" />
    <hkern g1="C,Ccedilla"
	g2="underscore"
	k="10" />
    <hkern g1="C,Ccedilla"
	g2="v"
	k="10" />
    <hkern g1="C,Ccedilla"
	g2="X"
	k="15" />
    <hkern g1="C,Ccedilla"
	g2="florin"
	k="40" />
    <hkern g1="C,Ccedilla"
	g2="greater"
	k="-10" />
    <hkern g1="C,Ccedilla"
	g2="questiondown.case"
	k="-15" />
    <hkern g1="C,Ccedilla"
	g2="section"
	k="5" />
    <hkern g1="C,Ccedilla"
	g2="uni2080"
	k="20" />
    <hkern g1="C,Ccedilla"
	g2="uni2083"
	k="20" />
    <hkern g1="C,Ccedilla"
	g2="uni2084"
	k="40" />
    <hkern g1="C,Ccedilla"
	g2="uni2085"
	k="20" />
    <hkern g1="C,Ccedilla"
	g2="uni2086"
	k="20" />
    <hkern g1="C,Ccedilla"
	g2="x"
	k="10" />
    <hkern g1="C,Ccedilla"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="10" />
    <hkern g1="C,Ccedilla"
	g2="Oslash"
	k="5" />
    <hkern g1="C,Ccedilla"
	g2="T"
	k="5" />
    <hkern g1="C,Ccedilla"
	g2="W"
	k="15" />
    <hkern g1="C,Ccedilla"
	g2="Y,Yacute,Ydieresis"
	k="25" />
    <hkern g1="C,Ccedilla"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="20" />
    <hkern g1="C,Ccedilla"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="20" />
    <hkern g1="C,Ccedilla"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="5" />
    <hkern g1="C,Ccedilla"
	g2="hyphen.case,endash.case,emdash.case"
	k="15" />
    <hkern g1="C,Ccedilla"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="C,Ccedilla"
	g2="guillemotleft.case,guilsinglleft.case"
	k="15" />
    <hkern g1="C,Ccedilla"
	g2="m,n,p,r,ntilde,r.ss03"
	k="15" />
    <hkern g1="C,Ccedilla"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="20" />
    <hkern g1="C,Ccedilla"
	g2="oslash"
	k="15" />
    <hkern g1="C,Ccedilla"
	g2="t"
	k="5" />
    <hkern g1="C,Ccedilla"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="20" />
    <hkern g1="C,Ccedilla"
	g2="w"
	k="10" />
    <hkern g1="C,Ccedilla"
	g2="y,yacute,ydieresis"
	k="15" />
    <hkern g1="C,Ccedilla"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="20" />
    <hkern g1="C,Ccedilla"
	g2="AE"
	k="45" />
    <hkern g1="C,Ccedilla"
	g2="bracketright,braceright"
	k="-10" />
    <hkern g1="C,Ccedilla"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="10" />
    <hkern g1="C,Ccedilla"
	g2="b,h,k,l,thorn"
	k="10" />
    <hkern g1="C,Ccedilla"
	g2="comma,period,ellipsis"
	k="10" />
    <hkern g1="C,Ccedilla"
	g2="s,scaron"
	k="10" />
    <hkern g1="C,Ccedilla"
	g2="z,zcaron"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="at"
	k="25" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="braceleft"
	k="30" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="braceleft.case"
	k="30" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="germandbls"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="ordfeminine"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="ordmasculine"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="v"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="greater"
	k="-10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="parenright"
	k="-10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="J"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="Oslash"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="hyphen,uni00AD,endash,emdash"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="hyphen.case,endash.case,emdash.case"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="guillemotleft.case,guilsinglleft.case"
	k="25" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="m,n,p,r,ntilde,r.ss03"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="oslash"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="w"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="y,yacute,ydieresis"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="bracketright,braceright"
	k="-15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="s,scaron"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="bracketright.case,braceright.case"
	k="-10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="G"
	g2="V"
	k="15" />
    <hkern g1="G"
	g2="ampersand"
	k="15" />
    <hkern g1="G"
	g2="asciitilde"
	k="-5" />
    <hkern g1="G"
	g2="backslash"
	k="20" />
    <hkern g1="G"
	g2="braceleft"
	k="10" />
    <hkern g1="G"
	g2="braceleft.case"
	k="20" />
    <hkern g1="G"
	g2="questiondown"
	k="-20" />
    <hkern g1="G"
	g2="slash"
	k="-5" />
    <hkern g1="G"
	g2="trademark"
	k="10" />
    <hkern g1="G"
	g2="underscore"
	k="-10" />
    <hkern g1="G"
	g2="greater"
	k="-20" />
    <hkern g1="G"
	g2="lslash"
	k="-20" />
    <hkern g1="G"
	g2="T"
	k="5" />
    <hkern g1="G"
	g2="W"
	k="10" />
    <hkern g1="G"
	g2="Y,Yacute,Ydieresis"
	k="30" />
    <hkern g1="G"
	g2="hyphen.case,endash.case,emdash.case"
	k="5" />
    <hkern g1="G"
	g2="guillemotleft,guilsinglleft"
	k="5" />
    <hkern g1="G"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="G"
	g2="t"
	k="-25" />
    <hkern g1="G"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="5" />
    <hkern g1="G"
	g2="AE"
	k="10" />
    <hkern g1="G.ss01"
	g2="V"
	k="25" />
    <hkern g1="G.ss01"
	g2="ampersand"
	k="10" />
    <hkern g1="G.ss01"
	g2="asterisk"
	k="5" />
    <hkern g1="G.ss01"
	g2="backslash"
	k="25" />
    <hkern g1="G.ss01"
	g2="germandbls"
	k="5" />
    <hkern g1="G.ss01"
	g2="ordfeminine"
	k="5" />
    <hkern g1="G.ss01"
	g2="questiondown"
	k="20" />
    <hkern g1="G.ss01"
	g2="slash"
	k="25" />
    <hkern g1="G.ss01"
	g2="trademark"
	k="35" />
    <hkern g1="G.ss01"
	g2="underscore"
	k="20" />
    <hkern g1="G.ss01"
	g2="uni2077"
	k="10" />
    <hkern g1="G.ss01"
	g2="X"
	k="15" />
    <hkern g1="G.ss01"
	g2="florin"
	k="20" />
    <hkern g1="G.ss01"
	g2="uni2084"
	k="30" />
    <hkern g1="G.ss01"
	g2="uni2085"
	k="10" />
    <hkern g1="G.ss01"
	g2="parenright"
	k="10" />
    <hkern g1="G.ss01"
	g2="lslash"
	k="-10" />
    <hkern g1="G.ss01"
	g2="Lslash"
	k="-10" />
    <hkern g1="G.ss01"
	g2="parenright.case"
	k="10" />
    <hkern g1="G.ss01"
	g2="J"
	k="-5" />
    <hkern g1="G.ss01"
	g2="T"
	k="20" />
    <hkern g1="G.ss01"
	g2="W"
	k="20" />
    <hkern g1="G.ss01"
	g2="Y,Yacute,Ydieresis"
	k="40" />
    <hkern g1="G.ss01"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="5" />
    <hkern g1="G.ss01"
	g2="hyphen,uni00AD,endash,emdash"
	k="-10" />
    <hkern g1="G.ss01"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="-5" />
    <hkern g1="G.ss01"
	g2="quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="G.ss01"
	g2="t"
	k="-25" />
    <hkern g1="G.ss01"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="20" />
    <hkern g1="G.ss01"
	g2="AE"
	k="70" />
    <hkern g1="G.ss01"
	g2="bracketright,braceright"
	k="10" />
    <hkern g1="G.ss01"
	g2="comma,period,ellipsis"
	k="10" />
    <hkern g1="G.ss01"
	g2="s,scaron"
	k="-5" />
    <hkern g1="G.ss01"
	g2="bracketright.case,braceright.case"
	k="10" />
    <hkern g1="G.ss01"
	g2="Eth"
	k="-5" />
    <hkern g1="G.ss01"
	g2="Z,Zcaron"
	k="5" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde"
	g2="at"
	k="10" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde"
	g2="braceleft"
	k="30" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde"
	g2="braceleft.case"
	k="30" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde"
	g2="bullet"
	k="10" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde"
	g2="questiondown"
	k="30" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde"
	g2="greater"
	k="-10" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde"
	g2="uni2084"
	k="30" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde"
	g2="uni2085"
	k="20" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde"
	g2="uni2086"
	k="30" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde"
	g2="uni2087"
	k="10" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde"
	g2="uni2088"
	k="10" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde"
	g2="uni2089"
	k="10" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="5" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde"
	g2="guillemotleft.case,guilsinglleft.case"
	k="10" />
    <hkern g1="K"
	g2="V"
	k="15" />
    <hkern g1="K"
	g2="a.ordn"
	k="30" />
    <hkern g1="K"
	g2="ampersand"
	k="55" />
    <hkern g1="K"
	g2="ampersand.ss04"
	k="30" />
    <hkern g1="K"
	g2="asciicircum"
	k="20" />
    <hkern g1="K"
	g2="asciitilde"
	k="60" />
    <hkern g1="K"
	g2="asterisk"
	k="10" />
    <hkern g1="K"
	g2="at"
	k="55" />
    <hkern g1="K"
	g2="braceleft"
	k="40" />
    <hkern g1="K"
	g2="braceleft.case"
	k="60" />
    <hkern g1="K"
	g2="bullet"
	k="50" />
    <hkern g1="K"
	g2="c.ordn"
	k="30" />
    <hkern g1="K"
	g2="d.ordn"
	k="30" />
    <hkern g1="K"
	g2="divide"
	k="30" />
    <hkern g1="K"
	g2="e.ordn"
	k="30" />
    <hkern g1="K"
	g2="g.ordn"
	k="30" />
    <hkern g1="K"
	g2="germandbls"
	k="35" />
    <hkern g1="K"
	g2="j.ordn"
	k="30" />
    <hkern g1="K"
	g2="less"
	k="50" />
    <hkern g1="K"
	g2="m.ordn"
	k="20" />
    <hkern g1="K"
	g2="n.ordn"
	k="20" />
    <hkern g1="K"
	g2="o.ordn"
	k="30" />
    <hkern g1="K"
	g2="onesuperior"
	k="20" />
    <hkern g1="K"
	g2="ordfeminine"
	k="40" />
    <hkern g1="K"
	g2="ordmasculine"
	k="30" />
    <hkern g1="K"
	g2="p.ordn"
	k="10" />
    <hkern g1="K"
	g2="parenleft.case"
	k="20" />
    <hkern g1="K"
	g2="periodcentered"
	k="40" />
    <hkern g1="K"
	g2="plus"
	k="40" />
    <hkern g1="K"
	g2="q.ordn"
	k="30" />
    <hkern g1="K"
	g2="question"
	k="5" />
    <hkern g1="K"
	g2="questiondown"
	k="-10" />
    <hkern g1="K"
	g2="registered.ss06"
	k="30" />
    <hkern g1="K"
	g2="slash"
	k="-20" />
    <hkern g1="K"
	g2="threesuperior"
	k="20" />
    <hkern g1="K"
	g2="twosuperior"
	k="20" />
    <hkern g1="K"
	g2="underscore"
	k="-20" />
    <hkern g1="K"
	g2="uni2070"
	k="40" />
    <hkern g1="K"
	g2="uni2074"
	k="70" />
    <hkern g1="K"
	g2="uni2075"
	k="40" />
    <hkern g1="K"
	g2="uni2076"
	k="40" />
    <hkern g1="K"
	g2="uni2077"
	k="20" />
    <hkern g1="K"
	g2="uni2078"
	k="30" />
    <hkern g1="K"
	g2="uni2079"
	k="40" />
    <hkern g1="K"
	g2="v"
	k="45" />
    <hkern g1="K"
	g2="florin"
	k="50" />
    <hkern g1="K"
	g2="questiondown.case"
	k="10" />
    <hkern g1="K"
	g2="section"
	k="15" />
    <hkern g1="K"
	g2="x"
	k="10" />
    <hkern g1="K"
	g2="parenright"
	k="-5" />
    <hkern g1="K"
	g2="parenright.case"
	k="-5" />
    <hkern g1="K"
	g2="equal"
	k="50" />
    <hkern g1="K"
	g2="multiply"
	k="10" />
    <hkern g1="K"
	g2="J"
	k="5" />
    <hkern g1="K"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="55" />
    <hkern g1="K"
	g2="Oslash"
	k="20" />
    <hkern g1="K"
	g2="S,Scaron"
	k="10" />
    <hkern g1="K"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="10" />
    <hkern g1="K"
	g2="W"
	k="15" />
    <hkern g1="K"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="15" />
    <hkern g1="K"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="40" />
    <hkern g1="K"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="55" />
    <hkern g1="K"
	g2="hyphen,uni00AD,endash,emdash"
	k="70" />
    <hkern g1="K"
	g2="hyphen.case,endash.case,emdash.case"
	k="55" />
    <hkern g1="K"
	g2="guillemotleft,guilsinglleft"
	k="65" />
    <hkern g1="K"
	g2="guillemotleft.case,guilsinglleft.case"
	k="65" />
    <hkern g1="K"
	g2="guillemotright.case,guilsinglright.case"
	k="10" />
    <hkern g1="K"
	g2="m,n,p,r,ntilde,r.ss03"
	k="15" />
    <hkern g1="K"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="35" />
    <hkern g1="K"
	g2="oslash"
	k="25" />
    <hkern g1="K"
	g2="t"
	k="30" />
    <hkern g1="K"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="55" />
    <hkern g1="K"
	g2="w"
	k="45" />
    <hkern g1="K"
	g2="y,yacute,ydieresis"
	k="60" />
    <hkern g1="K"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="10" />
    <hkern g1="K"
	g2="AE"
	k="50" />
    <hkern g1="K"
	g2="bracketright,braceright"
	k="-5" />
    <hkern g1="K"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="20" />
    <hkern g1="K"
	g2="s,scaron"
	k="10" />
    <hkern g1="K"
	g2="bracketright.case,braceright.case"
	k="-5" />
    <hkern g1="K"
	g2="guillemotright,guilsinglright"
	k="25" />
    <hkern g1="K"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="L,Lslash"
	g2="V"
	k="60" />
    <hkern g1="L,Lslash"
	g2="a.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="ampersand"
	k="25" />
    <hkern g1="L,Lslash"
	g2="asciicircum"
	k="50" />
    <hkern g1="L,Lslash"
	g2="asciitilde"
	k="50" />
    <hkern g1="L,Lslash"
	g2="asterisk"
	k="70" />
    <hkern g1="L,Lslash"
	g2="at"
	k="20" />
    <hkern g1="L,Lslash"
	g2="b.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="backslash"
	k="70" />
    <hkern g1="L,Lslash"
	g2="braceleft"
	k="30" />
    <hkern g1="L,Lslash"
	g2="braceleft.case"
	k="30" />
    <hkern g1="L,Lslash"
	g2="bullet"
	k="20" />
    <hkern g1="L,Lslash"
	g2="c.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="d.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="divide"
	k="10" />
    <hkern g1="L,Lslash"
	g2="e.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="f.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="g.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="germandbls"
	k="5" />
    <hkern g1="L,Lslash"
	g2="h.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="i.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="j.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="k.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="l.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="less"
	k="40" />
    <hkern g1="L,Lslash"
	g2="m.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="n.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="o.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="onesuperior"
	k="70" />
    <hkern g1="L,Lslash"
	g2="ordfeminine"
	k="80" />
    <hkern g1="L,Lslash"
	g2="ordmasculine"
	k="80" />
    <hkern g1="L,Lslash"
	g2="p.ordn"
	k="40" />
    <hkern g1="L,Lslash"
	g2="paragraph"
	k="50" />
    <hkern g1="L,Lslash"
	g2="parenleft.case"
	k="20" />
    <hkern g1="L,Lslash"
	g2="plus"
	k="20" />
    <hkern g1="L,Lslash"
	g2="q.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="question"
	k="45" />
    <hkern g1="L,Lslash"
	g2="questiondown"
	k="-30" />
    <hkern g1="L,Lslash"
	g2="r.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="registered.ss06"
	k="100" />
    <hkern g1="L,Lslash"
	g2="s.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="slash"
	k="-40" />
    <hkern g1="L,Lslash"
	g2="t.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="threesuperior"
	k="70" />
    <hkern g1="L,Lslash"
	g2="trademark"
	k="110" />
    <hkern g1="L,Lslash"
	g2="twosuperior"
	k="70" />
    <hkern g1="L,Lslash"
	g2="u.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="underscore"
	k="-30" />
    <hkern g1="L,Lslash"
	g2="uni2070"
	k="80" />
    <hkern g1="L,Lslash"
	g2="uni2074"
	k="120" />
    <hkern g1="L,Lslash"
	g2="uni2075"
	k="70" />
    <hkern g1="L,Lslash"
	g2="uni2076"
	k="70" />
    <hkern g1="L,Lslash"
	g2="uni2077"
	k="70" />
    <hkern g1="L,Lslash"
	g2="uni2078"
	k="70" />
    <hkern g1="L,Lslash"
	g2="uni2079"
	k="70" />
    <hkern g1="L,Lslash"
	g2="v"
	k="40" />
    <hkern g1="L,Lslash"
	g2="v.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="w.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="x.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="y.ordn"
	k="50" />
    <hkern g1="L,Lslash"
	g2="z.ordn"
	k="60" />
    <hkern g1="L,Lslash"
	g2="uni2080"
	k="-10" />
    <hkern g1="L,Lslash"
	g2="parenright"
	k="-10" />
    <hkern g1="L,Lslash"
	g2="uni2088"
	k="-10" />
    <hkern g1="L,Lslash"
	g2="equal"
	k="20" />
    <hkern g1="L,Lslash"
	g2="bar"
	k="-20" />
    <hkern g1="L,Lslash"
	g2="brokenbar"
	k="-20" />
    <hkern g1="L,Lslash"
	g2="uni2081"
	k="-10" />
    <hkern g1="L,Lslash"
	g2="J"
	k="-20" />
    <hkern g1="L,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="35" />
    <hkern g1="L,Lslash"
	g2="Oslash"
	k="20" />
    <hkern g1="L,Lslash"
	g2="T"
	k="70" />
    <hkern g1="L,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="10" />
    <hkern g1="L,Lslash"
	g2="W"
	k="50" />
    <hkern g1="L,Lslash"
	g2="Y,Yacute,Ydieresis"
	k="65" />
    <hkern g1="L,Lslash"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="5" />
    <hkern g1="L,Lslash"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="20" />
    <hkern g1="L,Lslash"
	g2="hyphen,uni00AD,endash,emdash"
	k="40" />
    <hkern g1="L,Lslash"
	g2="hyphen.case,endash.case,emdash.case"
	k="30" />
    <hkern g1="L,Lslash"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="L,Lslash"
	g2="guillemotleft.case,guilsinglleft.case"
	k="30" />
    <hkern g1="L,Lslash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="5" />
    <hkern g1="L,Lslash"
	g2="oslash"
	k="5" />
    <hkern g1="L,Lslash"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="L,Lslash"
	g2="quotesinglbase,quotedblbase"
	k="-20" />
    <hkern g1="L,Lslash"
	g2="quoteleft,quotedblleft"
	k="70" />
    <hkern g1="L,Lslash"
	g2="quoteright,quotedblright"
	k="60" />
    <hkern g1="L,Lslash"
	g2="t"
	k="15" />
    <hkern g1="L,Lslash"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="20" />
    <hkern g1="L,Lslash"
	g2="w"
	k="40" />
    <hkern g1="L,Lslash"
	g2="y,yacute,ydieresis"
	k="45" />
    <hkern g1="L,Lslash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="-20" />
    <hkern g1="L,Lslash"
	g2="bracketright,braceright"
	k="-10" />
    <hkern g1="L,Lslash"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="10" />
    <hkern g1="L,Lslash"
	g2="comma,period,ellipsis"
	k="-10" />
    <hkern g1="L,Lslash"
	g2="bracketright.case,braceright.case"
	k="-10" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="V"
	k="25" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="ampersand"
	k="20" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="asciicircum"
	k="-10" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="asciitilde"
	k="-10" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="asterisk"
	k="5" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="backslash"
	k="35" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="bracketleft.case"
	k="5" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="onesuperior"
	k="20" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="ordfeminine"
	k="10" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="questiondown"
	k="20" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="slash"
	k="35" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="t.ordn"
	k="10" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="threesuperior"
	k="30" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="trademark"
	k="35" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="twosuperior"
	k="20" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="underscore"
	k="40" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2070"
	k="20" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2074"
	k="20" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2075"
	k="30" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2076"
	k="30" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2077"
	k="40" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2078"
	k="30" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2079"
	k="30" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="X"
	k="30" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="florin"
	k="60" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="greater"
	k="-10" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2080"
	k="30" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2083"
	k="10" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2084"
	k="70" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2085"
	k="40" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2086"
	k="50" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="parenright"
	k="20" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="lslash"
	k="-5" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="Lslash"
	k="-10" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="parenright.case"
	k="20" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2087"
	k="20" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2088"
	k="30" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2089"
	k="20" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2081"
	k="10" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="bracketleft"
	k="5" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="uni2082"
	k="20" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="S,Scaron"
	k="5" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="T"
	k="25" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="W"
	k="20" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="Y,Yacute,Ydieresis"
	k="45" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="-10" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="guillemotleft,guilsinglleft"
	k="5" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="guillemotleft.case,guilsinglleft.case"
	k="5" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="guillemotright.case,guilsinglright.case"
	k="10" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="quoteleft,quotedblleft"
	k="5" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="quoteright,quotedblright"
	k="10" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="t"
	k="-25" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="25" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="AE"
	k="60" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="bracketright,braceright"
	k="20" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="-5" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="comma,period,ellipsis"
	k="30" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="bracketright.case,braceright.case"
	k="20" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis"
	g2="Z,Zcaron"
	k="10" />
    <hkern g1="Oslash"
	g2="V"
	k="20" />
    <hkern g1="Oslash"
	g2="ampersand.ss04"
	k="10" />
    <hkern g1="Oslash"
	g2="backslash"
	k="25" />
    <hkern g1="Oslash"
	g2="germandbls"
	k="15" />
    <hkern g1="Oslash"
	g2="ordfeminine"
	k="10" />
    <hkern g1="Oslash"
	g2="questiondown"
	k="35" />
    <hkern g1="Oslash"
	g2="slash"
	k="35" />
    <hkern g1="Oslash"
	g2="underscore"
	k="40" />
    <hkern g1="Oslash"
	g2="X"
	k="15" />
    <hkern g1="Oslash"
	g2="florin"
	k="40" />
    <hkern g1="Oslash"
	g2="parenright"
	k="20" />
    <hkern g1="Oslash"
	g2="lslash"
	k="-5" />
    <hkern g1="Oslash"
	g2="parenright.case"
	k="20" />
    <hkern g1="Oslash"
	g2="T"
	k="20" />
    <hkern g1="Oslash"
	g2="W"
	k="15" />
    <hkern g1="Oslash"
	g2="Y,Yacute,Ydieresis"
	k="35" />
    <hkern g1="Oslash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="5" />
    <hkern g1="Oslash"
	g2="guillemotright.case,guilsinglright.case"
	k="10" />
    <hkern g1="Oslash"
	g2="quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="Oslash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="25" />
    <hkern g1="Oslash"
	g2="AE"
	k="90" />
    <hkern g1="Oslash"
	g2="bracketright,braceright"
	k="20" />
    <hkern g1="Oslash"
	g2="comma,period,ellipsis"
	k="30" />
    <hkern g1="Oslash"
	g2="bracketright.case,braceright.case"
	k="20" />
    <hkern g1="Oslash"
	g2="Z,Zcaron"
	k="10" />
    <hkern g1="R"
	g2="V"
	k="5" />
    <hkern g1="R"
	g2="ampersand"
	k="5" />
    <hkern g1="R"
	g2="asciicircum"
	k="-10" />
    <hkern g1="R"
	g2="asciitilde"
	k="10" />
    <hkern g1="R"
	g2="asterisk"
	k="-10" />
    <hkern g1="R"
	g2="at"
	k="15" />
    <hkern g1="R"
	g2="backslash"
	k="5" />
    <hkern g1="R"
	g2="braceleft"
	k="10" />
    <hkern g1="R"
	g2="braceleft.case"
	k="20" />
    <hkern g1="R"
	g2="bullet"
	k="20" />
    <hkern g1="R"
	g2="dagger"
	k="-10" />
    <hkern g1="R"
	g2="germandbls"
	k="15" />
    <hkern g1="R"
	g2="j.ordn"
	k="10" />
    <hkern g1="R"
	g2="less"
	k="5" />
    <hkern g1="R"
	g2="ordfeminine"
	k="20" />
    <hkern g1="R"
	g2="periodcentered"
	k="5" />
    <hkern g1="R"
	g2="plus"
	k="10" />
    <hkern g1="R"
	g2="registered.ss06"
	k="-10" />
    <hkern g1="R"
	g2="underscore"
	k="-5" />
    <hkern g1="R"
	g2="v.ordn"
	k="-10" />
    <hkern g1="R"
	g2="w.ordn"
	k="-10" />
    <hkern g1="R"
	g2="y.ordn"
	k="-20" />
    <hkern g1="R"
	g2="florin"
	k="50" />
    <hkern g1="R"
	g2="greater"
	k="-25" />
    <hkern g1="R"
	g2="uni2083"
	k="20" />
    <hkern g1="R"
	g2="uni2084"
	k="20" />
    <hkern g1="R"
	g2="uni2085"
	k="10" />
    <hkern g1="R"
	g2="uni2086"
	k="20" />
    <hkern g1="R"
	g2="x"
	k="-10" />
    <hkern g1="R"
	g2="parenright"
	k="-5" />
    <hkern g1="R"
	g2="lslash"
	k="5" />
    <hkern g1="R"
	g2="parenright.case"
	k="-5" />
    <hkern g1="R"
	g2="uni2087"
	k="30" />
    <hkern g1="R"
	g2="uni2088"
	k="20" />
    <hkern g1="R"
	g2="multiply"
	k="-15" />
    <hkern g1="R"
	g2="daggerdbl"
	k="-10" />
    <hkern g1="R"
	g2="J"
	k="15" />
    <hkern g1="R"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="5" />
    <hkern g1="R"
	g2="Oslash"
	k="5" />
    <hkern g1="R"
	g2="W"
	k="5" />
    <hkern g1="R"
	g2="Y,Yacute,Ydieresis"
	k="10" />
    <hkern g1="R"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="5" />
    <hkern g1="R"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="30" />
    <hkern g1="R"
	g2="hyphen,uni00AD,endash,emdash"
	k="20" />
    <hkern g1="R"
	g2="hyphen.case,endash.case,emdash.case"
	k="10" />
    <hkern g1="R"
	g2="guillemotleft,guilsinglleft"
	k="15" />
    <hkern g1="R"
	g2="guillemotleft.case,guilsinglleft.case"
	k="10" />
    <hkern g1="R"
	g2="m,n,p,r,ntilde,r.ss03"
	k="5" />
    <hkern g1="R"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="20" />
    <hkern g1="R"
	g2="oslash"
	k="30" />
    <hkern g1="R"
	g2="quoteleft,quotedblleft"
	k="-10" />
    <hkern g1="R"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="20" />
    <hkern g1="R"
	g2="w"
	k="10" />
    <hkern g1="R"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="5" />
    <hkern g1="R"
	g2="AE"
	k="20" />
    <hkern g1="R"
	g2="bracketright,braceright"
	k="-10" />
    <hkern g1="R"
	g2="comma,period,ellipsis"
	k="5" />
    <hkern g1="R"
	g2="s,scaron"
	k="5" />
    <hkern g1="R"
	g2="bracketright.case,braceright.case"
	k="-5" />
    <hkern g1="S,Scaron"
	g2="asciicircum"
	k="5" />
    <hkern g1="S,Scaron"
	g2="backslash"
	k="25" />
    <hkern g1="S,Scaron"
	g2="braceleft"
	k="20" />
    <hkern g1="S,Scaron"
	g2="braceleft.case"
	k="10" />
    <hkern g1="S,Scaron"
	g2="germandbls"
	k="30" />
    <hkern g1="S,Scaron"
	g2="ordfeminine"
	k="10" />
    <hkern g1="S,Scaron"
	g2="ordmasculine"
	k="10" />
    <hkern g1="S,Scaron"
	g2="underscore"
	k="10" />
    <hkern g1="S,Scaron"
	g2="v"
	k="20" />
    <hkern g1="S,Scaron"
	g2="florin"
	k="50" />
    <hkern g1="S,Scaron"
	g2="greater"
	k="-25" />
    <hkern g1="S,Scaron"
	g2="uni2084"
	k="10" />
    <hkern g1="S,Scaron"
	g2="uni2085"
	k="20" />
    <hkern g1="S,Scaron"
	g2="uni2086"
	k="10" />
    <hkern g1="S,Scaron"
	g2="x"
	k="15" />
    <hkern g1="S,Scaron"
	g2="Lslash"
	k="-5" />
    <hkern g1="S,Scaron"
	g2="parenright.case"
	k="-10" />
    <hkern g1="S,Scaron"
	g2="uni2089"
	k="10" />
    <hkern g1="S,Scaron"
	g2="multiply"
	k="5" />
    <hkern g1="S,Scaron"
	g2="J"
	k="-10" />
    <hkern g1="S,Scaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="5" />
    <hkern g1="S,Scaron"
	g2="Oslash"
	k="5" />
    <hkern g1="S,Scaron"
	g2="T"
	k="5" />
    <hkern g1="S,Scaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="5" />
    <hkern g1="S,Scaron"
	g2="Y,Yacute,Ydieresis"
	k="10" />
    <hkern g1="S,Scaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="5" />
    <hkern g1="S,Scaron"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="15" />
    <hkern g1="S,Scaron"
	g2="hyphen,uni00AD,endash,emdash"
	k="5" />
    <hkern g1="S,Scaron"
	g2="hyphen.case,endash.case,emdash.case"
	k="10" />
    <hkern g1="S,Scaron"
	g2="guillemotleft,guilsinglleft"
	k="5" />
    <hkern g1="S,Scaron"
	g2="m,n,p,r,ntilde,r.ss03"
	k="30" />
    <hkern g1="S,Scaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="10" />
    <hkern g1="S,Scaron"
	g2="oslash"
	k="10" />
    <hkern g1="S,Scaron"
	g2="t"
	k="10" />
    <hkern g1="S,Scaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="25" />
    <hkern g1="S,Scaron"
	g2="w"
	k="20" />
    <hkern g1="S,Scaron"
	g2="y,yacute,ydieresis"
	k="25" />
    <hkern g1="S,Scaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="5" />
    <hkern g1="S,Scaron"
	g2="AE"
	k="50" />
    <hkern g1="S,Scaron"
	g2="bracketright,braceright"
	k="-15" />
    <hkern g1="S,Scaron"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="15" />
    <hkern g1="S,Scaron"
	g2="i,igrave,iacute,icircumflex,idieresis,dotlessi"
	k="5" />
    <hkern g1="S,Scaron"
	g2="j"
	k="5" />
    <hkern g1="S,Scaron"
	g2="b,h,k,l,thorn"
	k="5" />
    <hkern g1="S,Scaron"
	g2="z,zcaron"
	k="5" />
    <hkern g1="S,Scaron"
	g2="bracketright.case,braceright.case"
	k="-15" />
    <hkern g1="S,Scaron"
	g2="guillemotright,guilsinglright"
	k="5" />
    <hkern g1="T"
	g2="V"
	k="-20" />
    <hkern g1="T"
	g2="ampersand"
	k="30" />
    <hkern g1="T"
	g2="ampersand.ss04"
	k="20" />
    <hkern g1="T"
	g2="asciitilde"
	k="70" />
    <hkern g1="T"
	g2="asterisk"
	k="-20" />
    <hkern g1="T"
	g2="at"
	k="25" />
    <hkern g1="T"
	g2="backslash"
	k="-40" />
    <hkern g1="T"
	g2="braceleft"
	k="30" />
    <hkern g1="T"
	g2="braceleft.case"
	k="40" />
    <hkern g1="T"
	g2="bullet"
	k="70" />
    <hkern g1="T"
	g2="c.ordn"
	k="10" />
    <hkern g1="T"
	g2="d.ordn"
	k="10" />
    <hkern g1="T"
	g2="dagger"
	k="-20" />
    <hkern g1="T"
	g2="divide"
	k="60" />
    <hkern g1="T"
	g2="e.ordn"
	k="10" />
    <hkern g1="T"
	g2="g.ordn"
	k="10" />
    <hkern g1="T"
	g2="germandbls"
	k="35" />
    <hkern g1="T"
	g2="j.ordn"
	k="10" />
    <hkern g1="T"
	g2="less"
	k="40" />
    <hkern g1="T"
	g2="onesuperior"
	k="-10" />
    <hkern g1="T"
	g2="paragraph"
	k="-10" />
    <hkern g1="T"
	g2="periodcentered"
	k="50" />
    <hkern g1="T"
	g2="plus"
	k="50" />
    <hkern g1="T"
	g2="question"
	k="-20" />
    <hkern g1="T"
	g2="questiondown"
	k="60" />
    <hkern g1="T"
	g2="r.ordn"
	k="-20" />
    <hkern g1="T"
	g2="registered.ss06"
	k="-30" />
    <hkern g1="T"
	g2="slash"
	k="70" />
    <hkern g1="T"
	g2="threesuperior"
	k="-10" />
    <hkern g1="T"
	g2="trademark"
	k="-30" />
    <hkern g1="T"
	g2="twosuperior"
	k="-10" />
    <hkern g1="T"
	g2="underscore"
	k="40" />
    <hkern g1="T"
	g2="uni2070"
	k="-10" />
    <hkern g1="T"
	g2="uni2074"
	k="10" />
    <hkern g1="T"
	g2="uni2076"
	k="-10" />
    <hkern g1="T"
	g2="uni2077"
	k="-20" />
    <hkern g1="T"
	g2="uni2078"
	k="-10" />
    <hkern g1="T"
	g2="uni2079"
	k="-10" />
    <hkern g1="T"
	g2="v"
	k="70" />
    <hkern g1="T"
	g2="v.ordn"
	k="-20" />
    <hkern g1="T"
	g2="w.ordn"
	k="-20" />
    <hkern g1="T"
	g2="x.ordn"
	k="-20" />
    <hkern g1="T"
	g2="y.ordn"
	k="-20" />
    <hkern g1="T"
	g2="X"
	k="-15" />
    <hkern g1="T"
	g2="florin"
	k="110" />
    <hkern g1="T"
	g2="greater"
	k="10" />
    <hkern g1="T"
	g2="questiondown.case"
	k="20" />
    <hkern g1="T"
	g2="section"
	k="-20" />
    <hkern g1="T"
	g2="uni2080"
	k="100" />
    <hkern g1="T"
	g2="uni2083"
	k="100" />
    <hkern g1="T"
	g2="uni2084"
	k="120" />
    <hkern g1="T"
	g2="uni2085"
	k="100" />
    <hkern g1="T"
	g2="uni2086"
	k="100" />
    <hkern g1="T"
	g2="x"
	k="75" />
    <hkern g1="T"
	g2="parenright"
	k="-20" />
    <hkern g1="T"
	g2="lslash"
	k="20" />
    <hkern g1="T"
	g2="parenright.case"
	k="-20" />
    <hkern g1="T"
	g2="uni2087"
	k="100" />
    <hkern g1="T"
	g2="uni2088"
	k="100" />
    <hkern g1="T"
	g2="uni2089"
	k="100" />
    <hkern g1="T"
	g2="equal"
	k="30" />
    <hkern g1="T"
	g2="multiply"
	k="40" />
    <hkern g1="T"
	g2="bar"
	k="-20" />
    <hkern g1="T"
	g2="brokenbar"
	k="-20" />
    <hkern g1="T"
	g2="uni2081"
	k="100" />
    <hkern g1="T"
	g2="uni2082"
	k="100" />
    <hkern g1="T"
	g2="daggerdbl"
	k="-10" />
    <hkern g1="T"
	g2="exclamdown"
	k="30" />
    <hkern g1="T"
	g2="J"
	k="65" />
    <hkern g1="T"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="25" />
    <hkern g1="T"
	g2="Oslash"
	k="20" />
    <hkern g1="T"
	g2="T"
	k="-20" />
    <hkern g1="T"
	g2="W"
	k="-20" />
    <hkern g1="T"
	g2="Y,Yacute,Ydieresis"
	k="-20" />
    <hkern g1="T"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="85" />
    <hkern g1="T"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="75" />
    <hkern g1="T"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="10" />
    <hkern g1="T"
	g2="hyphen,uni00AD,endash,emdash"
	k="60" />
    <hkern g1="T"
	g2="hyphen.case,endash.case,emdash.case"
	k="60" />
    <hkern g1="T"
	g2="guillemotleft,guilsinglleft"
	k="120" />
    <hkern g1="T"
	g2="guillemotleft.case,guilsinglleft.case"
	k="80" />
    <hkern g1="T"
	g2="guillemotright.case,guilsinglright.case"
	k="30" />
    <hkern g1="T"
	g2="m,n,p,r,ntilde,r.ss03"
	k="60" />
    <hkern g1="T"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="95" />
    <hkern g1="T"
	g2="oslash"
	k="75" />
    <hkern g1="T"
	g2="quotedbl,quotesingle"
	k="-30" />
    <hkern g1="T"
	g2="quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="T"
	g2="quoteleft,quotedblleft"
	k="-30" />
    <hkern g1="T"
	g2="quoteright,quotedblright"
	k="-30" />
    <hkern g1="T"
	g2="t"
	k="25" />
    <hkern g1="T"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="95" />
    <hkern g1="T"
	g2="w"
	k="75" />
    <hkern g1="T"
	g2="y,yacute,ydieresis"
	k="70" />
    <hkern g1="T"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="65" />
    <hkern g1="T"
	g2="AE"
	k="130" />
    <hkern g1="T"
	g2="bracketright,braceright"
	k="-20" />
    <hkern g1="T"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="40" />
    <hkern g1="T"
	g2="i,igrave,iacute,icircumflex,idieresis,dotlessi"
	k="20" />
    <hkern g1="T"
	g2="j"
	k="10" />
    <hkern g1="T"
	g2="comma,period,ellipsis"
	k="100" />
    <hkern g1="T"
	g2="s,scaron"
	k="70" />
    <hkern g1="T"
	g2="z,zcaron"
	k="45" />
    <hkern g1="T"
	g2="bracketright.case,braceright.case"
	k="-20" />
    <hkern g1="T"
	g2="guillemotright,guilsinglright"
	k="80" />
    <hkern g1="T"
	g2="colon,semicolon"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="ampersand"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="asciitilde"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="at"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="braceleft"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="braceleft.case"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="bullet"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="divide"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="germandbls"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="j.ordn"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="less"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="periodcentered"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="plus"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="questiondown"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="slash"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="underscore"
	k="15" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="florin"
	k="50" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="uni2080"
	k="30" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="uni2083"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="uni2084"
	k="40" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="uni2085"
	k="30" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="uni2086"
	k="40" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="uni2087"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="uni2088"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="uni2089"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="uni2081"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="uni2082"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="exclamdown"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="exclamdown.case"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="hyphen,uni00AD,endash,emdash"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="guillemotleft.case,guilsinglleft.case"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="m,n,p,r,ntilde,r.ss03"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="oslash"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="30" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="AE"
	k="70" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="comma,period,ellipsis"
	k="25" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis"
	g2="colon,semicolon"
	k="5" />
    <hkern g1="W"
	g2="V"
	k="-10" />
    <hkern g1="W"
	g2="ampersand.ss04"
	k="10" />
    <hkern g1="W"
	g2="asciitilde"
	k="20" />
    <hkern g1="W"
	g2="asterisk"
	k="-10" />
    <hkern g1="W"
	g2="at"
	k="20" />
    <hkern g1="W"
	g2="backslash"
	k="-20" />
    <hkern g1="W"
	g2="braceleft"
	k="30" />
    <hkern g1="W"
	g2="braceleft.case"
	k="30" />
    <hkern g1="W"
	g2="bullet"
	k="30" />
    <hkern g1="W"
	g2="divide"
	k="20" />
    <hkern g1="W"
	g2="germandbls"
	k="15" />
    <hkern g1="W"
	g2="less"
	k="20" />
    <hkern g1="W"
	g2="onesuperior"
	k="-10" />
    <hkern g1="W"
	g2="periodcentered"
	k="30" />
    <hkern g1="W"
	g2="plus"
	k="30" />
    <hkern g1="W"
	g2="questiondown"
	k="60" />
    <hkern g1="W"
	g2="slash"
	k="60" />
    <hkern g1="W"
	g2="trademark"
	k="-20" />
    <hkern g1="W"
	g2="underscore"
	k="45" />
    <hkern g1="W"
	g2="v"
	k="5" />
    <hkern g1="W"
	g2="v.ordn"
	k="-20" />
    <hkern g1="W"
	g2="w.ordn"
	k="-20" />
    <hkern g1="W"
	g2="y.ordn"
	k="-20" />
    <hkern g1="W"
	g2="z.ordn"
	k="-10" />
    <hkern g1="W"
	g2="X"
	k="-10" />
    <hkern g1="W"
	g2="florin"
	k="60" />
    <hkern g1="W"
	g2="questiondown.case"
	k="30" />
    <hkern g1="W"
	g2="uni2080"
	k="70" />
    <hkern g1="W"
	g2="uni2083"
	k="70" />
    <hkern g1="W"
	g2="uni2084"
	k="100" />
    <hkern g1="W"
	g2="uni2085"
	k="90" />
    <hkern g1="W"
	g2="uni2086"
	k="80" />
    <hkern g1="W"
	g2="parenright"
	k="-20" />
    <hkern g1="W"
	g2="parenright.case"
	k="-20" />
    <hkern g1="W"
	g2="uni2087"
	k="50" />
    <hkern g1="W"
	g2="uni2088"
	k="70" />
    <hkern g1="W"
	g2="uni2089"
	k="70" />
    <hkern g1="W"
	g2="equal"
	k="10" />
    <hkern g1="W"
	g2="bar"
	k="-20" />
    <hkern g1="W"
	g2="brokenbar"
	k="-20" />
    <hkern g1="W"
	g2="uni2081"
	k="40" />
    <hkern g1="W"
	g2="uni2082"
	k="70" />
    <hkern g1="W"
	g2="exclamdown"
	k="10" />
    <hkern g1="W"
	g2="J"
	k="50" />
    <hkern g1="W"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="W"
	g2="Oslash"
	k="20" />
    <hkern g1="W"
	g2="T"
	k="-20" />
    <hkern g1="W"
	g2="W"
	k="-10" />
    <hkern g1="W"
	g2="Y,Yacute,Ydieresis"
	k="-10" />
    <hkern g1="W"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="30" />
    <hkern g1="W"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="40" />
    <hkern g1="W"
	g2="hyphen,uni00AD,endash,emdash"
	k="40" />
    <hkern g1="W"
	g2="hyphen.case,endash.case,emdash.case"
	k="40" />
    <hkern g1="W"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="W"
	g2="guillemotleft.case,guilsinglleft.case"
	k="30" />
    <hkern g1="W"
	g2="guillemotright.case,guilsinglright.case"
	k="10" />
    <hkern g1="W"
	g2="m,n,p,r,ntilde,r.ss03"
	k="30" />
    <hkern g1="W"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="35" />
    <hkern g1="W"
	g2="oslash"
	k="45" />
    <hkern g1="W"
	g2="quotedbl,quotesingle"
	k="-20" />
    <hkern g1="W"
	g2="quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="W"
	g2="quoteleft,quotedblleft"
	k="-20" />
    <hkern g1="W"
	g2="quoteright,quotedblright"
	k="-20" />
    <hkern g1="W"
	g2="t"
	k="5" />
    <hkern g1="W"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="30" />
    <hkern g1="W"
	g2="w"
	k="10" />
    <hkern g1="W"
	g2="y,yacute,ydieresis"
	k="5" />
    <hkern g1="W"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="45" />
    <hkern g1="W"
	g2="AE"
	k="115" />
    <hkern g1="W"
	g2="bracketright,braceright"
	k="-20" />
    <hkern g1="W"
	g2="comma,period,ellipsis"
	k="100" />
    <hkern g1="W"
	g2="s,scaron"
	k="20" />
    <hkern g1="W"
	g2="z,zcaron"
	k="5" />
    <hkern g1="W"
	g2="bracketright.case,braceright.case"
	k="-20" />
    <hkern g1="W"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="W"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="V"
	k="-10" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="ampersand"
	k="50" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="asciitilde"
	k="60" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="at"
	k="40" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="backslash"
	k="-10" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="braceleft"
	k="20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="braceleft.case"
	k="30" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="bullet"
	k="70" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="c.ordn"
	k="20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="d.ordn"
	k="20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="divide"
	k="50" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="e.ordn"
	k="20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="g.ordn"
	k="20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="germandbls"
	k="40" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="j.ordn"
	k="10" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="less"
	k="60" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="o.ordn"
	k="20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="onesuperior"
	k="-20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="periodcentered"
	k="60" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="plus"
	k="50" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="q.ordn"
	k="20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="questiondown"
	k="100" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="s.ordn"
	k="10" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="slash"
	k="85" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="threesuperior"
	k="-10" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="underscore"
	k="60" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="uni2074"
	k="20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="uni2077"
	k="-20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="v"
	k="40" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="y.ordn"
	k="-20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="X"
	k="-10" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="florin"
	k="90" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="questiondown.case"
	k="40" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="uni2080"
	k="120" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="uni2083"
	k="100" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="uni2084"
	k="120" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="uni2085"
	k="120" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="uni2086"
	k="120" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="x"
	k="30" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="parenright"
	k="-20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="parenright.case"
	k="-20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="uni2087"
	k="100" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="uni2088"
	k="100" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="uni2089"
	k="100" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="equal"
	k="30" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="multiply"
	k="30" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="bar"
	k="-10" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="brokenbar"
	k="-10" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="uni2081"
	k="120" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="uni2082"
	k="120" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="exclamdown"
	k="30" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="J"
	k="75" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="45" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="Oslash"
	k="40" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="S,Scaron"
	k="10" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="T"
	k="-20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="W"
	k="-10" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="Y,Yacute,Ydieresis"
	k="-10" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="65" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="85" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="25" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="hyphen,uni00AD,endash,emdash"
	k="80" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="hyphen.case,endash.case,emdash.case"
	k="70" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="guillemotleft,guilsinglleft"
	k="80" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="guillemotleft.case,guilsinglleft.case"
	k="60" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="guillemotright.case,guilsinglright.case"
	k="10" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="m,n,p,r,ntilde,r.ss03"
	k="60" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="90" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="oslash"
	k="90" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="quotedbl,quotesingle"
	k="-10" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="quoteleft,quotedblleft"
	k="-20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="quoteright,quotedblright"
	k="-20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="t"
	k="10" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="60" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="w"
	k="50" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="y,yacute,ydieresis"
	k="45" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="65" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="AE"
	k="150" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="bracketright,braceright"
	k="-20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="25" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="i,igrave,iacute,icircumflex,idieresis,dotlessi"
	k="5" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="j"
	k="5" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="comma,period,ellipsis"
	k="130" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="s,scaron"
	k="65" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="z,zcaron"
	k="35" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="bracketright.case,braceright.case"
	k="-20" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="guillemotright,guilsinglright"
	k="30" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="Z,Zcaron"
	k="-5" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="colon,semicolon"
	k="60" />
    <hkern g1="Z,Zcaron"
	g2="at"
	k="15" />
    <hkern g1="Z,Zcaron"
	g2="bullet"
	k="10" />
    <hkern g1="Z,Zcaron"
	g2="germandbls"
	k="5" />
    <hkern g1="Z,Zcaron"
	g2="onesuperior"
	k="-20" />
    <hkern g1="Z,Zcaron"
	g2="periodcentered"
	k="10" />
    <hkern g1="Z,Zcaron"
	g2="plus"
	k="10" />
    <hkern g1="Z,Zcaron"
	g2="question"
	k="-5" />
    <hkern g1="Z,Zcaron"
	g2="underscore"
	k="-10" />
    <hkern g1="Z,Zcaron"
	g2="v.ordn"
	k="-10" />
    <hkern g1="Z,Zcaron"
	g2="w.ordn"
	k="-10" />
    <hkern g1="Z,Zcaron"
	g2="y.ordn"
	k="-10" />
    <hkern g1="Z,Zcaron"
	g2="X"
	k="-10" />
    <hkern g1="Z,Zcaron"
	g2="florin"
	k="20" />
    <hkern g1="Z,Zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,OE,G.ss01"
	k="20" />
    <hkern g1="Z,Zcaron"
	g2="Oslash"
	k="20" />
    <hkern g1="Z,Zcaron"
	g2="T"
	k="-15" />
    <hkern g1="Z,Zcaron"
	g2="Y,Yacute,Ydieresis"
	k="-5" />
    <hkern g1="Z,Zcaron"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="5" />
    <hkern g1="Z,Zcaron"
	g2="hyphen,uni00AD,endash,emdash"
	k="45" />
    <hkern g1="Z,Zcaron"
	g2="hyphen.case,endash.case,emdash.case"
	k="60" />
    <hkern g1="Z,Zcaron"
	g2="guillemotleft,guilsinglleft"
	k="25" />
    <hkern g1="Z,Zcaron"
	g2="guillemotleft.case,guilsinglleft.case"
	k="35" />
    <hkern g1="Z,Zcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="5" />
    <hkern g1="Z,Zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="5" />
    <hkern g1="Z,Zcaron"
	g2="w"
	k="5" />
    <hkern g1="Z,Zcaron"
	g2="y,yacute,ydieresis"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="a.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="ampersand"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="ampersand.ss04"
	k="5" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="asterisk"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="at"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="b.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="backslash"
	k="60" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="c.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="d.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="e.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="f.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="g.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="h.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="i.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="j.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="k.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="l.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="m.ordn"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="n.ordn"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="o.ordn"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="onesuperior"
	k="50" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="ordfeminine"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="ordmasculine"
	k="15" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="q.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="registered.ss06"
	k="50" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="s.ordn"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="t.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="threesuperior"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="trademark"
	k="70" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="twosuperior"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="u.ordn"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="underscore"
	k="-30" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="uni2070"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="uni2075"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="uni2076"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="uni2077"
	k="60" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="uni2078"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="uni2079"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="v.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="w.ordn"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="y.ordn"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="z.ordn"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="quoteleft,quotedblleft"
	k="40" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="quoteright,quotedblright"
	k="20" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="y,yacute,ydieresis"
	k="10" />
    <hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	g2="ampersand"
	k="5" />
    <hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	g2="asciicircum"
	k="10" />
    <hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	g2="backslash"
	k="55" />
    <hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	g2="germandbls"
	k="5" />
    <hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	g2="trademark"
	k="40" />
    <hkern g1="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	g2="y,yacute,ydieresis"
	k="5" />
    <hkern g1="c,ccedilla"
	g2="ampersand"
	k="5" />
    <hkern g1="c,ccedilla"
	g2="ampersand.ss04"
	k="10" />
    <hkern g1="c,ccedilla"
	g2="asterisk"
	k="10" />
    <hkern g1="c,ccedilla"
	g2="at"
	k="5" />
    <hkern g1="c,ccedilla"
	g2="backslash"
	k="70" />
    <hkern g1="c,ccedilla"
	g2="onesuperior"
	k="30" />
    <hkern g1="c,ccedilla"
	g2="ordfeminine"
	k="10" />
    <hkern g1="c,ccedilla"
	g2="ordmasculine"
	k="10" />
    <hkern g1="c,ccedilla"
	g2="questiondown"
	k="10" />
    <hkern g1="c,ccedilla"
	g2="registered.ss06"
	k="50" />
    <hkern g1="c,ccedilla"
	g2="slash"
	k="15" />
    <hkern g1="c,ccedilla"
	g2="t.ordn"
	k="20" />
    <hkern g1="c,ccedilla"
	g2="trademark"
	k="30" />
    <hkern g1="c,ccedilla"
	g2="underscore"
	k="10" />
    <hkern g1="c,ccedilla"
	g2="v"
	k="5" />
    <hkern g1="c,ccedilla"
	g2="v.ordn"
	k="10" />
    <hkern g1="c,ccedilla"
	g2="w.ordn"
	k="10" />
    <hkern g1="c,ccedilla"
	g2="uni2084"
	k="10" />
    <hkern g1="c,ccedilla"
	g2="x"
	k="5" />
    <hkern g1="c,ccedilla"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="5" />
    <hkern g1="c,ccedilla"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="5" />
    <hkern g1="c,ccedilla"
	g2="guillemotright.case,guilsinglright.case"
	k="-10" />
    <hkern g1="c,ccedilla"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="5" />
    <hkern g1="c,ccedilla"
	g2="oslash"
	k="5" />
    <hkern g1="c,ccedilla"
	g2="quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="c,ccedilla"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="c,ccedilla"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="5" />
    <hkern g1="c,ccedilla"
	g2="w"
	k="5" />
    <hkern g1="c,ccedilla"
	g2="y,yacute,ydieresis"
	k="5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="ampersand"
	k="15" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="ampersand.ss04"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="asterisk"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="backslash"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="germandbls"
	k="5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="onesuperior"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="ordfeminine"
	k="15" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="ordmasculine"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="question"
	k="25" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="questiondown"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="registered.ss06"
	k="50" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="t.ordn"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="trademark"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="underscore"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="uni2074"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="uni2077"
	k="50" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="v"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="uni2084"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="x"
	k="5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="parenright"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="lslash"
	k="-10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="quoteleft,quotedblleft"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="quoteright,quotedblright"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="15" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="w"
	k="5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="y,yacute,ydieresis"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="bracketright,braceright"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="comma,period,ellipsis"
	k="15" />
    <hkern g1="f,uniFB00"
	g2="a.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="asciicircum"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="asciitilde"
	k="10" />
    <hkern g1="f,uniFB00"
	g2="asterisk"
	k="-40" />
    <hkern g1="f,uniFB00"
	g2="b.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="backslash"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="bullet"
	k="10" />
    <hkern g1="f,uniFB00"
	g2="c.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="d.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="dagger"
	k="-40" />
    <hkern g1="f,uniFB00"
	g2="e.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="f.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="g.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="h.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="i.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="j.ordn"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="k.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="l.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="m.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="n.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="o.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="onesuperior"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="p.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="paragraph"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="plus"
	k="10" />
    <hkern g1="f,uniFB00"
	g2="q.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="question"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="questiondown"
	k="25" />
    <hkern g1="f,uniFB00"
	g2="r.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="registered.ss06"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="s.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="slash"
	k="15" />
    <hkern g1="f,uniFB00"
	g2="t.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="threesuperior"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="trademark"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="twosuperior"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="u.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="underscore"
	k="20" />
    <hkern g1="f,uniFB00"
	g2="uni2070"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="uni2074"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="uni2075"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="uni2076"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="uni2077"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="uni2078"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="uni2079"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="v"
	k="-10" />
    <hkern g1="f,uniFB00"
	g2="v.ordn"
	k="-40" />
    <hkern g1="f,uniFB00"
	g2="w.ordn"
	k="-40" />
    <hkern g1="f,uniFB00"
	g2="x.ordn"
	k="-40" />
    <hkern g1="f,uniFB00"
	g2="y.ordn"
	k="-40" />
    <hkern g1="f,uniFB00"
	g2="z.ordn"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="greater"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="questiondown.case"
	k="10" />
    <hkern g1="f,uniFB00"
	g2="section"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="uni2080"
	k="10" />
    <hkern g1="f,uniFB00"
	g2="uni2083"
	k="20" />
    <hkern g1="f,uniFB00"
	g2="uni2084"
	k="30" />
    <hkern g1="f,uniFB00"
	g2="uni2085"
	k="20" />
    <hkern g1="f,uniFB00"
	g2="uni2086"
	k="20" />
    <hkern g1="f,uniFB00"
	g2="x"
	k="-10" />
    <hkern g1="f,uniFB00"
	g2="parenright"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="lslash"
	k="-10" />
    <hkern g1="f,uniFB00"
	g2="uni2087"
	k="10" />
    <hkern g1="f,uniFB00"
	g2="uni2088"
	k="20" />
    <hkern g1="f,uniFB00"
	g2="uni2089"
	k="20" />
    <hkern g1="f,uniFB00"
	g2="multiply"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="bar"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="brokenbar"
	k="-40" />
    <hkern g1="f,uniFB00"
	g2="uni2081"
	k="10" />
    <hkern g1="f,uniFB00"
	g2="uni2082"
	k="20" />
    <hkern g1="f,uniFB00"
	g2="daggerdbl"
	k="-30" />
    <hkern g1="f,uniFB00"
	g2="exclamdown.case"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="exclam"
	k="-15" />
    <hkern g1="f,uniFB00"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="15" />
    <hkern g1="f,uniFB00"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="10" />
    <hkern g1="f,uniFB00"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="-10" />
    <hkern g1="f,uniFB00"
	g2="hyphen,uni00AD,endash,emdash"
	k="5" />
    <hkern g1="f,uniFB00"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="f,uniFB00"
	g2="guillemotright.case,guilsinglright.case"
	k="-5" />
    <hkern g1="f,uniFB00"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="20" />
    <hkern g1="f,uniFB00"
	g2="oslash"
	k="20" />
    <hkern g1="f,uniFB00"
	g2="quotedbl,quotesingle"
	k="-50" />
    <hkern g1="f,uniFB00"
	g2="quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="f,uniFB00"
	g2="quoteleft,quotedblleft"
	k="-40" />
    <hkern g1="f,uniFB00"
	g2="quoteright,quotedblright"
	k="-20" />
    <hkern g1="f,uniFB00"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="5" />
    <hkern g1="f,uniFB00"
	g2="w"
	k="-10" />
    <hkern g1="f,uniFB00"
	g2="y,yacute,ydieresis"
	k="-15" />
    <hkern g1="f,uniFB00"
	g2="bracketright,braceright"
	k="-25" />
    <hkern g1="f,uniFB00"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="5" />
    <hkern g1="f,uniFB00"
	g2="j"
	k="-25" />
    <hkern g1="f,uniFB00"
	g2="comma,period,ellipsis"
	k="50" />
    <hkern g1="f,uniFB00"
	g2="z,zcaron"
	k="-10" />
    <hkern g1="g"
	g2="ampersand"
	k="5" />
    <hkern g1="g"
	g2="ampersand.ss04"
	k="5" />
    <hkern g1="g"
	g2="backslash"
	k="40" />
    <hkern g1="g"
	g2="divide"
	k="5" />
    <hkern g1="g"
	g2="f.ordn"
	k="20" />
    <hkern g1="g"
	g2="j.ordn"
	k="10" />
    <hkern g1="g"
	g2="ordfeminine"
	k="20" />
    <hkern g1="g"
	g2="registered.ss06"
	k="30" />
    <hkern g1="g"
	g2="t.ordn"
	k="20" />
    <hkern g1="g"
	g2="trademark"
	k="30" />
    <hkern g1="g"
	g2="uni2077"
	k="10" />
    <hkern g1="g"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="5" />
    <hkern g1="g"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="10" />
    <hkern g1="g"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="5" />
    <hkern g1="g"
	g2="w"
	k="5" />
    <hkern g1="g"
	g2="y,yacute,ydieresis"
	k="5" />
    <hkern g1="i,q,igrave,iacute,icircumflex,idieresis,dotlessi,fi,uniFB03"
	g2="backslash"
	k="60" />
    <hkern g1="j"
	g2="at"
	k="5" />
    <hkern g1="j"
	g2="germandbls"
	k="5" />
    <hkern g1="j"
	g2="daggerdbl"
	k="-50" />
    <hkern g1="j"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="5" />
    <hkern g1="k"
	g2="ampersand"
	k="5" />
    <hkern g1="k"
	g2="asciitilde"
	k="20" />
    <hkern g1="k"
	g2="asterisk"
	k="10" />
    <hkern g1="k"
	g2="at"
	k="5" />
    <hkern g1="k"
	g2="backslash"
	k="25" />
    <hkern g1="k"
	g2="braceleft"
	k="10" />
    <hkern g1="k"
	g2="bullet"
	k="20" />
    <hkern g1="k"
	g2="dagger"
	k="-10" />
    <hkern g1="k"
	g2="divide"
	k="10" />
    <hkern g1="k"
	g2="less"
	k="20" />
    <hkern g1="k"
	g2="plus"
	k="10" />
    <hkern g1="k"
	g2="slash"
	k="-20" />
    <hkern g1="k"
	g2="trademark"
	k="20" />
    <hkern g1="k"
	g2="underscore"
	k="-30" />
    <hkern g1="k"
	g2="greater"
	k="-40" />
    <hkern g1="k"
	g2="uni2080"
	k="-10" />
    <hkern g1="k"
	g2="uni2083"
	k="-10" />
    <hkern g1="k"
	g2="uni2084"
	k="-10" />
    <hkern g1="k"
	g2="uni2085"
	k="-10" />
    <hkern g1="k"
	g2="uni2086"
	k="-10" />
    <hkern g1="k"
	g2="uni2088"
	k="-10" />
    <hkern g1="k"
	g2="uni2089"
	k="-10" />
    <hkern g1="k"
	g2="multiply"
	k="-20" />
    <hkern g1="k"
	g2="uni2081"
	k="-10" />
    <hkern g1="k"
	g2="uni2082"
	k="-10" />
    <hkern g1="k"
	g2="daggerdbl"
	k="-10" />
    <hkern g1="k"
	g2="exclamdown"
	k="-10" />
    <hkern g1="k"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="15" />
    <hkern g1="k"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="20" />
    <hkern g1="k"
	g2="hyphen,uni00AD,endash,emdash"
	k="30" />
    <hkern g1="k"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="k"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="25" />
    <hkern g1="k"
	g2="quotesinglbase,quotedblbase"
	k="-20" />
    <hkern g1="k"
	g2="quoteright,quotedblright"
	k="-5" />
    <hkern g1="k"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="5" />
    <hkern g1="k"
	g2="z,zcaron"
	k="-5" />
    <hkern g1="k"
	g2="colon,semicolon"
	k="5" />
    <hkern g1="d,l,fl,uniFB04"
	g2="germandbls"
	k="5" />
    <hkern g1="d,l,fl,uniFB04"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="5" />
    <hkern g1="h,m,n,ntilde"
	g2="ampersand.ss04"
	k="10" />
    <hkern g1="h,m,n,ntilde"
	g2="asterisk"
	k="10" />
    <hkern g1="h,m,n,ntilde"
	g2="at"
	k="5" />
    <hkern g1="h,m,n,ntilde"
	g2="backslash"
	k="65" />
    <hkern g1="h,m,n,ntilde"
	g2="germandbls"
	k="10" />
    <hkern g1="h,m,n,ntilde"
	g2="onesuperior"
	k="40" />
    <hkern g1="h,m,n,ntilde"
	g2="ordfeminine"
	k="10" />
    <hkern g1="h,m,n,ntilde"
	g2="question"
	k="15" />
    <hkern g1="h,m,n,ntilde"
	g2="registered.ss06"
	k="50" />
    <hkern g1="h,m,n,ntilde"
	g2="trademark"
	k="40" />
    <hkern g1="h,m,n,ntilde"
	g2="uni2077"
	k="50" />
    <hkern g1="h,m,n,ntilde"
	g2="equal"
	k="5" />
    <hkern g1="h,m,n,ntilde"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="5" />
    <hkern g1="h,m,n,ntilde"
	g2="quoteleft,quotedblleft"
	k="40" />
    <hkern g1="h,m,n,ntilde"
	g2="quoteright,quotedblright"
	k="50" />
    <hkern g1="h,m,n,ntilde"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="10" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="a.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="ampersand"
	k="10" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="ampersand.ss04"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="asterisk"
	k="40" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="b.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="backslash"
	k="100" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="c.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="d.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="e.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="f.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="g.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="germandbls"
	k="5" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="h.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="i.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="j.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="k.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="l.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="m.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="n.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="o.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="onesuperior"
	k="60" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="ordfeminine"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="ordmasculine"
	k="30" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="p.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="q.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="question"
	k="25" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="r.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="registered.ss06"
	k="50" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="s.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="slash"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="t.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="threesuperior"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="trademark"
	k="50" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="twosuperior"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="u.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="underscore"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="uni2070"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="uni2074"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="uni2075"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="uni2076"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="uni2077"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="uni2078"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="uni2079"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="v"
	k="15" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="v.ordn"
	k="40" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="w.ordn"
	k="40" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="y.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="z.ordn"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="uni2083"
	k="10" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="uni2084"
	k="30" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="uni2085"
	k="10" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="uni2086"
	k="10" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="x"
	k="10" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="parenright"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="lslash"
	k="-20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="quoteleft,quotedblleft"
	k="40" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="quoteright,quotedblright"
	k="40" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="t"
	k="5" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="5" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="w"
	k="10" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="y,yacute,ydieresis"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="bracketright,braceright"
	k="20" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="comma,period,ellipsis"
	k="15" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="z,zcaron"
	k="5" />
    <hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,thorn"
	g2="colon,semicolon"
	k="5" />
    <hkern g1="oslash"
	g2="ampersand"
	k="10" />
    <hkern g1="oslash"
	g2="backslash"
	k="70" />
    <hkern g1="oslash"
	g2="registered.ss06"
	k="30" />
    <hkern g1="oslash"
	g2="underscore"
	k="20" />
    <hkern g1="oslash"
	g2="v"
	k="10" />
    <hkern g1="oslash"
	g2="x"
	k="5" />
    <hkern g1="oslash"
	g2="parenright"
	k="10" />
    <hkern g1="oslash"
	g2="lslash"
	k="-5" />
    <hkern g1="oslash"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="oslash"
	g2="quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="oslash"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="oslash"
	g2="quoteright,quotedblright"
	k="20" />
    <hkern g1="oslash"
	g2="w"
	k="10" />
    <hkern g1="oslash"
	g2="y,yacute,ydieresis"
	k="10" />
    <hkern g1="oslash"
	g2="bracketright,braceright"
	k="10" />
    <hkern g1="oslash"
	g2="comma,period,ellipsis"
	k="15" />
    <hkern g1="r"
	g2="asterisk"
	k="-40" />
    <hkern g1="r"
	g2="at"
	k="-20" />
    <hkern g1="r"
	g2="backslash"
	k="10" />
    <hkern g1="r"
	g2="dagger"
	k="-30" />
    <hkern g1="r"
	g2="paragraph"
	k="-40" />
    <hkern g1="r"
	g2="question"
	k="-20" />
    <hkern g1="r"
	g2="questiondown"
	k="10" />
    <hkern g1="r"
	g2="registered.ss06"
	k="-20" />
    <hkern g1="r"
	g2="underscore"
	k="10" />
    <hkern g1="r"
	g2="uni2077"
	k="10" />
    <hkern g1="r"
	g2="v"
	k="-15" />
    <hkern g1="r"
	g2="section"
	k="-30" />
    <hkern g1="r"
	g2="uni2080"
	k="20" />
    <hkern g1="r"
	g2="uni2083"
	k="20" />
    <hkern g1="r"
	g2="uni2084"
	k="30" />
    <hkern g1="r"
	g2="uni2085"
	k="20" />
    <hkern g1="r"
	g2="uni2086"
	k="20" />
    <hkern g1="r"
	g2="x"
	k="-30" />
    <hkern g1="r"
	g2="uni2087"
	k="10" />
    <hkern g1="r"
	g2="uni2088"
	k="20" />
    <hkern g1="r"
	g2="uni2089"
	k="20" />
    <hkern g1="r"
	g2="bar"
	k="-30" />
    <hkern g1="r"
	g2="brokenbar"
	k="-30" />
    <hkern g1="r"
	g2="uni2081"
	k="20" />
    <hkern g1="r"
	g2="uni2082"
	k="30" />
    <hkern g1="r"
	g2="daggerdbl"
	k="-30" />
    <hkern g1="r"
	g2="exclamdown"
	k="-20" />
    <hkern g1="r"
	g2="exclam"
	k="-30" />
    <hkern g1="r"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="10" />
    <hkern g1="r"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="-30" />
    <hkern g1="r"
	g2="guillemotleft.case,guilsinglleft.case"
	k="-20" />
    <hkern g1="r"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="5" />
    <hkern g1="r"
	g2="oslash"
	k="5" />
    <hkern g1="r"
	g2="quotedbl,quotesingle"
	k="-30" />
    <hkern g1="r"
	g2="quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="r"
	g2="quoteright,quotedblright"
	k="-30" />
    <hkern g1="r"
	g2="t"
	k="-25" />
    <hkern g1="r"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="-5" />
    <hkern g1="r"
	g2="w"
	k="-10" />
    <hkern g1="r"
	g2="y,yacute,ydieresis"
	k="-15" />
    <hkern g1="r"
	g2="bracketright,braceright"
	k="-20" />
    <hkern g1="r"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="-25" />
    <hkern g1="r"
	g2="comma,period,ellipsis"
	k="90" />
    <hkern g1="r"
	g2="z,zcaron"
	k="-10" />
    <hkern g1="r"
	g2="guillemotright,guilsinglright"
	k="-30" />
    <hkern g1="r.ss03"
	g2="asterisk"
	k="-10" />
    <hkern g1="r.ss03"
	g2="at"
	k="-40" />
    <hkern g1="r.ss03"
	g2="backslash"
	k="15" />
    <hkern g1="r.ss03"
	g2="bullet"
	k="-15" />
    <hkern g1="r.ss03"
	g2="dagger"
	k="-30" />
    <hkern g1="r.ss03"
	g2="periodcentered"
	k="-25" />
    <hkern g1="r.ss03"
	g2="question"
	k="-20" />
    <hkern g1="r.ss03"
	g2="questiondown"
	k="5" />
    <hkern g1="r.ss03"
	g2="registered.ss06"
	k="-20" />
    <hkern g1="r.ss03"
	g2="v"
	k="-25" />
    <hkern g1="r.ss03"
	g2="uni2080"
	k="20" />
    <hkern g1="r.ss03"
	g2="uni2084"
	k="30" />
    <hkern g1="r.ss03"
	g2="uni2085"
	k="10" />
    <hkern g1="r.ss03"
	g2="uni2086"
	k="20" />
    <hkern g1="r.ss03"
	g2="x"
	k="-40" />
    <hkern g1="r.ss03"
	g2="parenright"
	k="-20" />
    <hkern g1="r.ss03"
	g2="bar"
	k="-10" />
    <hkern g1="r.ss03"
	g2="uni2082"
	k="10" />
    <hkern g1="r.ss03"
	g2="daggerdbl"
	k="-30" />
    <hkern g1="r.ss03"
	g2="exclamdown"
	k="-10" />
    <hkern g1="r.ss03"
	g2="exclam"
	k="-20" />
    <hkern g1="r.ss03"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="-20" />
    <hkern g1="r.ss03"
	g2="guillemotright.case,guilsinglright.case"
	k="-20" />
    <hkern g1="r.ss03"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="-5" />
    <hkern g1="r.ss03"
	g2="quotedbl,quotesingle"
	k="-20" />
    <hkern g1="r.ss03"
	g2="quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="r.ss03"
	g2="quoteleft,quotedblleft"
	k="-10" />
    <hkern g1="r.ss03"
	g2="quoteright,quotedblright"
	k="-20" />
    <hkern g1="r.ss03"
	g2="t"
	k="-20" />
    <hkern g1="r.ss03"
	g2="w"
	k="-25" />
    <hkern g1="r.ss03"
	g2="y,yacute,ydieresis"
	k="-25" />
    <hkern g1="r.ss03"
	g2="bracketright,braceright"
	k="-20" />
    <hkern g1="r.ss03"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="-15" />
    <hkern g1="r.ss03"
	g2="comma,period,ellipsis"
	k="70" />
    <hkern g1="r.ss03"
	g2="s,scaron"
	k="-5" />
    <hkern g1="r.ss03"
	g2="z,zcaron"
	k="-10" />
    <hkern g1="r.ss03"
	g2="guillemotright,guilsinglright"
	k="-20" />
    <hkern g1="r.ss03"
	g2="colon,semicolon"
	k="-30" />
    <hkern g1="s,scaron"
	g2="asterisk"
	k="10" />
    <hkern g1="s,scaron"
	g2="backslash"
	k="60" />
    <hkern g1="s,scaron"
	g2="germandbls"
	k="5" />
    <hkern g1="s,scaron"
	g2="ordfeminine"
	k="20" />
    <hkern g1="s,scaron"
	g2="ordmasculine"
	k="20" />
    <hkern g1="s,scaron"
	g2="registered.ss06"
	k="40" />
    <hkern g1="s,scaron"
	g2="slash"
	k="-10" />
    <hkern g1="s,scaron"
	g2="trademark"
	k="40" />
    <hkern g1="s,scaron"
	g2="v"
	k="5" />
    <hkern g1="s,scaron"
	g2="greater"
	k="-10" />
    <hkern g1="s,scaron"
	g2="x"
	k="5" />
    <hkern g1="s,scaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="10" />
    <hkern g1="s,scaron"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="5" />
    <hkern g1="s,scaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="5" />
    <hkern g1="s,scaron"
	g2="oslash"
	k="5" />
    <hkern g1="s,scaron"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="s,scaron"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="s,scaron"
	g2="quoteright,quotedblright"
	k="55" />
    <hkern g1="s,scaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="5" />
    <hkern g1="s,scaron"
	g2="w"
	k="5" />
    <hkern g1="s,scaron"
	g2="y,yacute,ydieresis"
	k="5" />
    <hkern g1="t"
	g2="question"
	k="-5" />
    <hkern g1="t"
	g2="slash"
	k="-20" />
    <hkern g1="t"
	g2="underscore"
	k="-30" />
    <hkern g1="t"
	g2="v"
	k="-10" />
    <hkern g1="t"
	g2="x"
	k="-20" />
    <hkern g1="t"
	g2="parenright"
	k="-10" />
    <hkern g1="t"
	g2="m,n,p,r,ntilde,r.ss03"
	k="10" />
    <hkern g1="t"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="5" />
    <hkern g1="t"
	g2="quotesinglbase,quotedblbase"
	k="-10" />
    <hkern g1="t"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="t"
	g2="quoteright,quotedblright"
	k="25" />
    <hkern g1="t"
	g2="w"
	k="-10" />
    <hkern g1="t"
	g2="y,yacute,ydieresis"
	k="-5" />
    <hkern g1="t"
	g2="bracketright,braceright"
	k="-10" />
    <hkern g1="t"
	g2="s,scaron"
	k="-5" />
    <hkern g1="t"
	g2="z,zcaron"
	k="-20" />
    <hkern g1="t"
	g2="guillemotright,guilsinglright"
	k="-10" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis"
	g2="at"
	k="5" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis"
	g2="backslash"
	k="50" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis"
	g2="germandbls"
	k="5" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis"
	g2="ordfeminine"
	k="20" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis"
	g2="ordmasculine"
	k="15" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis"
	g2="registered.ss06"
	k="20" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis"
	g2="trademark"
	k="20" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="5" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis"
	g2="guillemotleft.case,guilsinglleft.case"
	k="10" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="5" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis"
	g2="oslash"
	k="5" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis"
	g2="quoteleft,quotedblleft"
	k="10" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="5" />
    <hkern g1="w"
	g2="ampersand"
	k="10" />
    <hkern g1="w"
	g2="at"
	k="-15" />
    <hkern g1="w"
	g2="backslash"
	k="20" />
    <hkern g1="w"
	g2="dagger"
	k="-30" />
    <hkern g1="w"
	g2="questiondown"
	k="20" />
    <hkern g1="w"
	g2="registered.ss06"
	k="-20" />
    <hkern g1="w"
	g2="slash"
	k="20" />
    <hkern g1="w"
	g2="underscore"
	k="40" />
    <hkern g1="w"
	g2="v"
	k="-5" />
    <hkern g1="w"
	g2="uni2080"
	k="30" />
    <hkern g1="w"
	g2="uni2083"
	k="30" />
    <hkern g1="w"
	g2="uni2084"
	k="60" />
    <hkern g1="w"
	g2="uni2085"
	k="40" />
    <hkern g1="w"
	g2="uni2086"
	k="40" />
    <hkern g1="w"
	g2="x"
	k="-5" />
    <hkern g1="w"
	g2="parenright"
	k="20" />
    <hkern g1="w"
	g2="uni2087"
	k="10" />
    <hkern g1="w"
	g2="uni2088"
	k="40" />
    <hkern g1="w"
	g2="uni2089"
	k="30" />
    <hkern g1="w"
	g2="bar"
	k="-20" />
    <hkern g1="w"
	g2="brokenbar"
	k="-20" />
    <hkern g1="w"
	g2="uni2081"
	k="30" />
    <hkern g1="w"
	g2="uni2082"
	k="30" />
    <hkern g1="w"
	g2="daggerdbl"
	k="-40" />
    <hkern g1="w"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="5" />
    <hkern g1="w"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="10" />
    <hkern g1="w"
	g2="copyright,registered,uni24C5,circle,H18533,uni262E,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788"
	k="-10" />
    <hkern g1="w"
	g2="hyphen,uni00AD,endash,emdash"
	k="5" />
    <hkern g1="w"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="w"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="10" />
    <hkern g1="w"
	g2="oslash"
	k="10" />
    <hkern g1="w"
	g2="quotedbl,quotesingle"
	k="-10" />
    <hkern g1="w"
	g2="quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="w"
	g2="t"
	k="-15" />
    <hkern g1="w"
	g2="y,yacute,ydieresis"
	k="-5" />
    <hkern g1="w"
	g2="bracketright,braceright"
	k="20" />
    <hkern g1="w"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="-15" />
    <hkern g1="w"
	g2="comma,period,ellipsis"
	k="40" />
    <hkern g1="w"
	g2="z,zcaron"
	k="-5" />
    <hkern g1="y,yacute,ydieresis"
	g2="ampersand"
	k="20" />
    <hkern g1="y,yacute,ydieresis"
	g2="at"
	k="-10" />
    <hkern g1="y,yacute,ydieresis"
	g2="backslash"
	k="30" />
    <hkern g1="y,yacute,ydieresis"
	g2="dagger"
	k="-20" />
    <hkern g1="y,yacute,ydieresis"
	g2="question"
	k="-10" />
    <hkern g1="y,yacute,ydieresis"
	g2="questiondown"
	k="30" />
    <hkern g1="y,yacute,ydieresis"
	g2="registered.ss06"
	k="-10" />
    <hkern g1="y,yacute,ydieresis"
	g2="slash"
	k="10" />
    <hkern g1="y,yacute,ydieresis"
	g2="underscore"
	k="50" />
    <hkern g1="y,yacute,ydieresis"
	g2="v"
	k="-5" />
    <hkern g1="y,yacute,ydieresis"
	g2="uni2080"
	k="40" />
    <hkern g1="y,yacute,ydieresis"
	g2="uni2083"
	k="30" />
    <hkern g1="y,yacute,ydieresis"
	g2="uni2084"
	k="70" />
    <hkern g1="y,yacute,ydieresis"
	g2="uni2085"
	k="40" />
    <hkern g1="y,yacute,ydieresis"
	g2="uni2086"
	k="40" />
    <hkern g1="y,yacute,ydieresis"
	g2="uni2087"
	k="30" />
    <hkern g1="y,yacute,ydieresis"
	g2="uni2088"
	k="30" />
    <hkern g1="y,yacute,ydieresis"
	g2="uni2089"
	k="30" />
    <hkern g1="y,yacute,ydieresis"
	g2="bar"
	k="-15" />
    <hkern g1="y,yacute,ydieresis"
	g2="brokenbar"
	k="-10" />
    <hkern g1="y,yacute,ydieresis"
	g2="uni2081"
	k="30" />
    <hkern g1="y,yacute,ydieresis"
	g2="uni2082"
	k="30" />
    <hkern g1="y,yacute,ydieresis"
	g2="daggerdbl"
	k="-20" />
    <hkern g1="y,yacute,ydieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="15" />
    <hkern g1="y,yacute,ydieresis"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="20" />
    <hkern g1="y,yacute,ydieresis"
	g2="hyphen,uni00AD,endash,emdash"
	k="20" />
    <hkern g1="y,yacute,ydieresis"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="y,yacute,ydieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oe"
	k="20" />
    <hkern g1="y,yacute,ydieresis"
	g2="oslash"
	k="15" />
    <hkern g1="y,yacute,ydieresis"
	g2="quotedbl,quotesingle"
	k="-10" />
    <hkern g1="y,yacute,ydieresis"
	g2="quotesinglbase,quotedblbase"
	k="70" />
    <hkern g1="y,yacute,ydieresis"
	g2="t"
	k="-5" />
    <hkern g1="y,yacute,ydieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="5" />
    <hkern g1="y,yacute,ydieresis"
	g2="y,yacute,ydieresis"
	k="-5" />
    <hkern g1="y,yacute,ydieresis"
	g2="f,uniFB00,fi,fl,uniFB03,uniFB04"
	k="-10" />
    <hkern g1="y,yacute,ydieresis"
	g2="comma,period,ellipsis"
	k="110" />
    <hkern g1="y,yacute,ydieresis"
	g2="s,scaron"
	k="10" />
    <hkern g1="z,zcaron"
	g2="backslash"
	k="35" />
    <hkern g1="z,zcaron"
	g2="dagger"
	k="-10" />
    <hkern g1="z,zcaron"
	g2="underscore"
	k="-10" />
    <hkern g1="z,zcaron"
	g2="x"
	k="-5" />
    <hkern g1="z,zcaron"
	g2="lslash"
	k="-10" />
    <hkern g1="z,zcaron"
	g2="bar"
	k="-15" />
    <hkern g1="z,zcaron"
	g2="daggerdbl"
	k="-10" />
    <hkern g1="z,zcaron"
	g2="a.ss02,agrave.ss02,aacute.ss02,acircumflex.ss02,atilde.ss02,adieresis.ss02,aring.ss02"
	k="10" />
    <hkern g1="z,zcaron"
	g2="guillemotleft,guilsinglleft"
	k="10" />
  </font>
</defs></svg>
