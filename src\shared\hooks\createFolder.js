import { useState, useRef, useEffect } from 'react';
import api from 'shared/utils/api';

export const useCreateFolder = (fetchFolders, closeModal) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);

  const isMounted = useRef(true);

  useEffect(() => {
    // Mark as mounted when the component is mounted
    isMounted.current = true;

    // Cleanup function to mark as unmounted
    return () => {
      isMounted.current = false;
    };
  }, []);

  const createFolder = async (folderName, currentUserId, parentId) => {
    if (!folderName || !currentUserId) {
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const data = {
        name: folderName,
        parent: parentId,
        createdBy: currentUserId,
      };
      const response = await api.post(`/folder`, data);
      console.log('API response:', response.data);

      if (isMounted.current) {
        if (fetchFolders) fetchFolders();
        if (closeModal) closeModal();
      }
    } catch (err) {
      console.error('Error creating folder:', err);
      setError(err);
    } finally {
      if (isMounted.current) {
        setIsSubmitting(false);
      }
    }
  };

  return { createFolder, isSubmitting, error };
};
