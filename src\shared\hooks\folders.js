import { useState, useEffect, useCallback } from 'react';
import api from 'shared/utils/api';

const useFolders = id => {
  const [folders, setFolders] = useState([]);
  const [folderDetails, setFolderDetails] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchFolders = useCallback(async () => {
    setLoading(true);
    setError(null);

    const url = id ? `/folders/${id}` : `/folders/root`;
    try {
      const response = await api.get(url);
      if (id) {
        const res = await api.get(`/folder/${id}`);
        setFolderDetails(res || null);
      }
      setFolders(response);
    } catch (err) {
      setError(err.response ? err.response.data : err.message);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchFolders();
  }, [fetchFolders]);

  return { folders, folderDetails, loading, error, refetch: fetchFolders };
};

export default useFolders;
