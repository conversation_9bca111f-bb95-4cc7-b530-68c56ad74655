import React from 'react';
import { useParams } from 'react-router-dom';
import useFolders from 'shared/hooks/folders';
import { Divider } from './Styles';
import SubBar from './SubBar/SubBar';
import ShowFolders from './ShowItems/ShowFolders';

const FolderList = () => {
  const { id } = useParams();
  const { folders, folderDetails, loading, error, refetch } = useFolders(id);
  const { data, status } = folders;
  return (
    <div className="ml-60 px-8">
      <SubBar folderDetails={folderDetails?.folder} folders={data} fetchFolders={refetch} />
      <Divider />
      <ShowFolders
        folders={data}
        status={status}
        isLoading={loading}
        error={error}
        fetchFolders={refetch}
      />
    </div>
  );
};

export default FolderList;
