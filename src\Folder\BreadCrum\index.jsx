import React from 'react';
import { Link } from 'react-router-dom';

const Breadcrumb = ({ id, path, pathNames }) => {
  const pathParts = path?.split('/').filter(Boolean); // Extract path IDs as an array
  const nameParts = pathNames?.split('/').filter(Boolean); // Extract path names as an array

  if (pathParts?.length !== nameParts?.length) {
    console.error('Path and pathNames length mismatch!');
    return null;
  }

  const ChevronIcon = () => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      height="10px"
      viewBox="0 -960 960 960"
      width="10px"
      fill="#999999"
    >
      <path d="m321-80-71-71 329-329-329-329 71-71 400 400L321-80Z" />
    </svg>
  );

  return (
    <div className="flex flex-row items-center text-sm text-gray-500">
      {/* Render breadcrumb links */}
      {id && (
        <Link to="/" className="text-sm text-gray-500">
          Home
        </Link>
      )}
      {id && nameParts?.map((name, index) => {
        const currentPath = `/folder/${pathParts.slice(0, index + 1).join('/')}`;

        return (
          <React.Fragment key={index}>
            <span className="mx-1">
              <ChevronIcon />
            </span>
            <Link to={currentPath} className="text-sm text-gray-500">
              {name}
            </Link>
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default Breadcrumb;
