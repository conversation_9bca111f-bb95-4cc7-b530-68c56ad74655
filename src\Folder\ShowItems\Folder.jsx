import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { ProjectAvatar } from 'shared/components';
import { useUpdateFolder } from 'shared/hooks/updateFolder';
import EditableField from 'Folder/ShowItems/EditableField';
import Dropdown from 'shared/components/OptionsDropdown';
import { useDeleteFolder } from 'shared/hooks/deleteFolder';

const Folder = ({ item, index, folderimg, fetchFolders }) => {
  const [folderName, setFolderName] = useState(item.name);

  const { updateFolder } = useUpdateFolder(fetchFolders);
  const { deleteFolder } = useDeleteFolder(fetchFolders);

  const dropdownOptions = [{ label: 'Delete', action: 'delete' }];

  const handleDelete = async () => {
    const confirmDelete = window.confirm(
      `Are you sure you want to delete the folder "${folderName}"?`,
    );
    if (confirmDelete) {
      try {
        await deleteFolder(item?.id); // Call delete API hook
        alert(`Folder "${folderName}" deleted successfully.`);
      } catch (error) {
        console.error('Error deleting folder:', error);
        alert('Failed to delete the folder. Please try again.');
      }
    }
  };

  const handleOptionSelect = option => {
    switch (option.action) {
      case 'delete':
        handleDelete();
        break;
      case 'create-folder':
        // Handle folder creation logic here (if needed)
        console.log('Create Folder clicked');
        break;
      case 'create-project':
        // Handle project creation logic here (if needed)
        console.log('Create Project clicked');
        break;
      default:
        break;
    }
  };

  const handleName = () => {
    updateFolder(folderName, item?.id);
  };

  return (
    <div>
      {item.type === 'folder' && (
        <div className="flex flex-col items-center">
          <Link key={`${index}${item.type}`} to={`/folder/${item.id}`}>
            <img src={folderimg} width={100} height={200} alt="Logo" />
          </Link>
          <div className="flex flex-row items-center">
            <EditableField name={folderName} setName={setFolderName} onSubmit={handleName} />
            <Dropdown
              title=""
              options={dropdownOptions}
              onOptionSelect={handleOptionSelect}
              className={{
                button:
                  'inline-flex w-full justify-center gap-x-1 rounded-md px-3 py-2 text-sm font-medium text-gray-900 shadow-sm hover:bg-gray-50 border-none ring-0', // Custom button style
              }}
            />
          </div>
        </div>
      )}
      {item.type === 'project' && (
        <Link key={`${index}${item.type}`} to={`/project/${item.id}/board`}>
          <div className="flex flex-col justify-between mt-4 ml-10">
            <ProjectAvatar />
            <span className="mt-3">{item.name}</span>
          </div>
        </Link>
      )}
    </div>
  );
};

export default Folder;
