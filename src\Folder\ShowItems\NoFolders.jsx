import React, { useState } from 'react';
import Dropdown from 'shared/components/OptionsDropdown';
import Add from 'shared/icons/Add';
import CreateFolder from 'Folder/SubBar/Create/CreateFolder';
import { Modal } from 'shared/components';
import brand from '../../../dev/brand7.png';

const NoFolders = ({ fetchFolders }) => {
  const [modalContent, setModalContent] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOptionSelect = option => {
    if (option.modalContent) {
      setModalContent(option.modalContent);
      setIsModalOpen(true);
    }
  };
  const closeModal = () => {
    setIsModalOpen(false);
    setModalContent(null);
  };
  const dropdownOptions = [
    {
      label: 'Create Folder',
      modalContent: <CreateFolder closeModal={closeModal} fetchFolders={fetchFolders} />,
    },
    { label: 'Create Project', modalContent: <div>Project Form</div> },
  ];
  return (
    <div className="ml-auto">
      <div className="flex flex-col justify-center items-center mb-20">
        <div className="text-8xl text-brand">
          <img
            src={brand}
            className="rounded-md object-cover w-40 h-auto sm:w-40 md:w-40 lg:w-40 xl:w-40 2xl:w-40 mt-7"
            alt="Logo"
          />
        </div>
        <div className="text-lg font-black mb-4 mt-4">This folder is empty.</div>
        <p className="pb-4">
          Add a notebook, import a document, or create a folder to fill it with life.
        </p>
        <Dropdown
          title="New"
          options={dropdownOptions}
          onOptionSelect={handleOptionSelect}
          icon={<Add />}
          className={{
            button:
              'inline-flex w-full justify-center gap-x-1 rounded-md px-3 py-2 text-sm font-medium  text-white bg-blue-600 hover:bg-blue-700',
            dropdown:
              'absolute right-0 z-10 mt-3 w-56 origin-top-right bg-white shadow-lg ring-1 ring-black/5',
          }}
        />
      </div>
      {isModalOpen && (
        <Modal
          isOpen
          testid="modal:create"
          width={400}
          withCloseIcon
          onClose={closeModal}
          renderContent={() => modalContent}
        />
      )}
    </div>
  );
};

export default NoFolders;
