/* eslint-disable global-require */
/* eslint-disable import/no-extraneous-dependencies */

// ***********************************************************
// This example plugins/index.js can be used to load plugins
//
// You can change the location of this file or turn off loading
// the plugins file with the 'pluginsFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/plugins-guide
// ***********************************************************

// This function is called when a project is opened or re-opened (e.g. due to
// the project's config changing)

const webpack = require('@cypress/webpack-preprocessor');
const webpackOptions = require('../../webpack.config.js');

module.exports = on => {
  on('file:preprocessor', webpack({ webpackOptions }));
};
