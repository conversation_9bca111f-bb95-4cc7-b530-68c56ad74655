import React from 'react';
import PropTypes from 'prop-types';
import { xor } from 'lodash';
import { useNavigate, useParams } from 'react-router-dom';

import {
  Filters,
  SearchInput,
  Avatars,
  AvatarIsActiveBorder,
  StyledAvatar,
  StyledButton,
  ClearAll,
} from './Styles';

const propTypes = {
  projectUsers: PropTypes.array.isRequired,
  defaultFilters: PropTypes.object.isRequired,
  filters: PropTypes.object.isRequired,
  mergeFilters: PropTypes.func.isRequired,
};

function ProjectBoardFilters({ projectUsers, defaultFilters, filters, mergeFilters }) {
  const navigate = useNavigate();
  const { id } = useParams();
  const goToBoard = () => {
    navigate(`/project/${id}/board`);
  };
  const goToSettings = () => {
    navigate(`/project/${id}/settings`);
  };
  const { searchTerm, userIds, myOnly, recent } = filters;

  const areFiltersCleared = !searchTerm && userIds.length === 0 && !myOnly && !recent;

  return (
    <Filters data-testid="board-filters">
      <SearchInput
        icon="search"
        value={searchTerm}
        onChange={(value) => mergeFilters({ searchTerm: value })}
      />
      <Avatars>
        {projectUsers.map((user) => (
          <AvatarIsActiveBorder key={user.id} isActive={userIds.includes(user.id)}>
            <StyledAvatar
              avatarUrl={user.avatarUrl}
              name={user.name}
              onClick={() => mergeFilters({ userIds: xor(userIds, [user.id]) })}
            />
          </AvatarIsActiveBorder>
        ))}
      </Avatars>
      <StyledButton
        variant="empty"
        isActive={myOnly}
        onClick={() => mergeFilters({ myOnly: !myOnly })}
      >
        Only My Issues
      </StyledButton>
      <StyledButton
        variant="empty"
        isActive={recent}
        onClick={() => mergeFilters({ recent: !recent })}
      >
        Recently Updated
      </StyledButton>
      <StyledButton variant="empty" isActive={recent} onClick={goToBoard}>
        Board
      </StyledButton>
      <StyledButton variant="empty" isActive={recent} onClick={goToSettings}>
        Settings
      </StyledButton>
      {!areFiltersCleared && (
        <ClearAll onClick={() => mergeFilters(defaultFilters)}>Clear all</ClearAll>
      )}
    </Filters>
  );
}

ProjectBoardFilters.propTypes = propTypes;

export default ProjectBoardFilters;
