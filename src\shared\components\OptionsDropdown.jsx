import React, { useEffect, useRef, useState } from 'react';
import clsx from 'clsx'; // Import clsx for conditional class handling

const Dropdown = ({
  title,
  options,
  onOptionSelect,
  icon,
  className = {}, // Accept className as an object for more control
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  const toggleDropdown = () => setIsOpen(prev => !prev);

  useEffect(() => {
    const handleClickOutside = event => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Default styles for dropdown
  const defaultClasses = {
    button:
      'inline-flex w-full justify-center gap-x-1 rounded-md px-3 py-2 text-sm font-medium text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50',
    dropdown:
      'absolute left-0 z-10 mt-3 w-56 origin-top-right bg-white shadow-lg ring-1 ring-black/5',
    item: 'block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100',
    icon: '-mr-1 size-5',
  };

  return (
    <div className="relative inline-block text-left" ref={dropdownRef}>
      <button
        type="button"
        className={clsx(className.button || defaultClasses.button)} // Merge default and custom button classes
        onClick={toggleDropdown}
      >
        {icon}
        {title}
        <svg
          className={clsx(defaultClasses.icon, className.icon)} // Merge default and custom icon classes
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z"
            clipRule="evenodd"
          />
        </svg>
      </button>
      {isOpen && (
        <div className={clsx(className.dropdown || defaultClasses.dropdown)}>
          <div className="py-1">
            {options.map((option, index) => (
              <button
                type="button"
                key={index}
                className={clsx(defaultClasses.item, className.item)} // Merge default and custom item classes
                onClick={() => {
                  onOptionSelect(option);
                  setIsOpen(false);
                }}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Dropdown;
