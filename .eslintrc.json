{
  "parserOptions": {
    "sourceType": "module",
    "ecmaVersion": 2024,
    "ecmaFeatures": {
      "jsx": true
    }
  },
  "env": {
    "browser": true,
    "jest": true,
    "es2024": true,
    "node": true
  },
  "extends": ["airbnb", "prettier"],
  "plugins": ["react-hooks"],
  "rules": {
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    "radix": 0,
    "no-restricted-syntax": 0,
    "no-await-in-loop": 0,
    "no-console": 0,
    "consistent-return": 0,
    "no-param-reassign": [2, { "props": false }],
    "no-return-assign": [2, "except-parens"],
    "no-use-before-define": 0,
    "import/prefer-default-export": 0,
    "import/no-cycle": 0,
    "react/no-array-index-key": 0,
    "react/forbid-prop-types": 0,
    "react/prop-types": [2, { "skipUndeclared": true }],
    "react/jsx-fragments": [2, "element"],
    "react/state-in-constructor": 0,
    "react/jsx-props-no-spreading": 0,
    "react/react-in-jsx-scope": "off",
    "jsx-a11y/click-events-have-key-events": 0
  },
  "settings": {
    // Allows us to lint absolute imports within codebase
    "import/resolver": {
      "node": {
        "moduleDirectory": ["node_modules", "src/"]
      }
    }
  }
}
